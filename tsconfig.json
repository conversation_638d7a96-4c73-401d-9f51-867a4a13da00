{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "resolveJsonModule": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "inlineSourceMap": false, "inlineSources": true, "baseUrl": ".", "types": ["webpack-env", "node"], "typeRoots": ["src/@types"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules"]}