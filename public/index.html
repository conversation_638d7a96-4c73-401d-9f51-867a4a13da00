<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover" name="viewport" />
    <link rel="icon" href="<%= BASE_URL %>cs_logo.png">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <script>
      console.log('index.html', 20250701)
      // 判断url为https://fpcs-web.kingsglorygames.com 或者 https://fpcs-web.kingsglorygames.com/，不包含任何路径，则跳转到 https://fpcs-web.kingsglorygames.com/pc/tickets?os=windows&sid=&uid=&fpid=&lang=en&fpx_app_id=worldx.global.prod&channel=Funplus-PC&subchannel=Funplus-PC&pkgChannel=worldx_funplus_pc&game_token=&sdk_version=2.0&name=&avatar=&portrait=&server_time=&city_lv=&lord_lv=&vip_lv=&vertical_plate=1&os_version=&device_type=windows&ram_mb=&country_code=HK&device_id=&track_key=6&role_id=&role_name=&scene=1&entrance=&json_data=

      const url = window.location
      const { hostname, pathname } = url

      console.log('hostname', hostname)
      console.log('pathname', pathname)

      if ((hostname === 'fpcs-web.kingsglorygames.com') && (pathname === '/' || pathname === '' || pathname === '/smart')) {
        console.log('redirect to pc/tickets')
        window.location.href = 'https://fpcs-web.kingsglorygames.com/pc/tickets?os=windows&sid=&uid=&fpid=&lang=en&fpx_app_id=worldx.global.prod&channel=Funplus-PC&subchannel=Funplus-PC&pkgChannel=worldx_funplus_pc&game_token=&sdk_version=2.0&name=&avatar=&portrait=&server_time=&city_lv=&lord_lv=&vip_lv=&vertical_plate=1&os_version=&device_type=windows&ram_mb=&country_code=HK&device_id=&track_key=6&role_id=&role_name=&scene=1&entrance=&json_data='
      }
    </script>

    <script>
      (function () {
        const { hostname, pathname } = window.location;
        const isTest =
          hostname.includes("test") || hostname.includes("localhost") || hostname.includes("**********");
        console.log('isTest', isTest)
        const gameMapData = {
          'mo_global': ['70001', '70007', '70008', '70009', '70010', '8111', '8118', '8118', '8119', '81120']
        }
        const testWhiteList = ['mo_global']
        const searchParams = new URLSearchParams(window.location.search || '');
        const gameId = searchParams.get('gameid') || '';
        const fpxAppId = searchParams.get('fpx_app_id') || '';
        // 从gameMapData中找到对应的gameid的key
        const gameProject = fpxAppId ? fpxAppId : Object.keys(gameMapData).find(key => gameMapData[key].includes(gameId || ''));
        console.log('gameProject', gameProject)
        const isWhiteGame = testWhiteList.includes(gameProject || '');
        console.log('isWhiteGame', isWhiteGame)
        if (isTest || isWhiteGame) {
          var script = document.createElement("script");
          script.src = "https://cdn.jsdelivr.net/npm/eruda";
          document.body.appendChild(script);
          script.onload = function () {
            eruda.init();
            console.log("eruda init href", window.location.href);
          };
        }
      })();
    </script>
  </body>
</html>
