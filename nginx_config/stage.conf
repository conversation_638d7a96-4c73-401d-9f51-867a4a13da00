log_format k8s '{"remote_addr":"$remote_addr",'
                  '"@timestamp":"$time_iso8601",'
                  '"msec":"$msec",'
                  '"http_host": "$http_host",'
                  '"request_time":"$request_time",'
                  '"upstream_response_time":"$upstream_response_time",'
                  '"request_length":"$request_length",'
                  '"request_method":"$request_method",'
                  '"bytes_sent":"$bytes_sent",'
                  '"status":"$status",'
                  '"uri":"$uri",'
                  '"request_uri":"$request_uri",'
                  '"http_user_agent":"$http_user_agent",'
                  '"http_x_forwarded_for":"$http_x_forwarded_for"'
                '}';
server {
  listen 80;
  server_name fpcs-web-stage.funplus.com;
  # access_log  /data/logs/nginx/fpcs-web-stage.funplus.com.log  main;
  access_log /dev/stdout k8s;

  # CORS headers
  add_header 'Access-Control-Allow-Origin' *;
  add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  add_header 'Access-Control-Allow-Headers' 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,auth,api-key,sign,lang';

  if ($request_method = 'OPTIONS') {
    return 204;
  }

  location / {
    root  /data/nginx/dist;
    index  index.php index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

  location ^~ /backend/{
    proxy_pass http://cs-api.ops-tools-stage.svc.cluster.local:9072/front/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ^~ /egress_survey/{
    proxy_pass http://ops-ticket-api.ops-tools-stage:9000/egress_survey/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

	location ^~ /api/v2/notice {
    proxy_pass http://ticket-api.ops-tools-stage.svc.cluster.local:9000/api/v2/notice;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
  
  location ^~ /api/fpx/notice {
    proxy_pass http://ticket-api.ops-tools-stage.svc.cluster.local:9000/api/fpx/notice;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
  location ^~ /api/v1/notice {
    proxy_pass http://ticket-api.ops-tools-stage.svc.cluster.local:9000/api/v1/notice;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
  
  location = /50x.html {
      root   html;
  }
}