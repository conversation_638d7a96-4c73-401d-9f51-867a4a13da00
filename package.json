{"name": "general-cs", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider BABEL_ENV=development vue-cli-service serve", "dev:fast": "cross-env NODE_OPTIONS=--openssl-legacy-provider BABEL_ENV=development vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint", "serve": "vue-cli-service serve", "serve:test": "vue-cli-service serve --mode test", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint"}, "dependencies": {"@vueuse/components": "^10.9.0", "@vueuse/core": "^10.9.0", "axios": "^0.27.2", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "hard-source-webpack-plugin": "^0.13.1", "hls.js": "^1.0.10", "throttle-debounce": "^3.0.1", "vant": "^3.4.8", "vue": "^3.2.33", "vue-i18n": "^9.1.10", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/node": "^20.11.25", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.11", "@vue/cli-plugin-eslint": "~4.5.11", "@vue/cli-plugin-router": "~4.5.11", "@vue/cli-plugin-typescript": "~4.5.11", "@vue/cli-plugin-vuex": "~4.5.11", "@vue/cli-service": "~4.5.11", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-plugin-import": "^1.13.5", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "image-webpack-loader": "^8.1.0", "less-loader": "6.0.0", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "postcss-pxtorem": "^5.1.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "~4.1.5", "vconsole": "^3.14.6", "vue-axios": "^3.4.1"}}