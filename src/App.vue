<template>
  <router-view></router-view>
  <CustomLoading>
    <template #loadicon>
      <div class="loading-icon"></div>
    </template>
  </CustomLoading>
  <div class="img-preload">
    <!-- 预加载关键图片，防止首次加载时闪烁或者加载缓慢 -->
    <img src="~@/assets/img/conversation/classify-popup-one-bg.png" alt="" />
    <img src="~@/assets/img/conversation/classify-popup-title.png" alt="" />
    <img src="~@/assets/img/conversation/upload-dialog-close.png" alt="" />
    <img src="~@/assets/img/conversation/classify-popup-arrow.png" alt="" />
  </div>
</template>
<script setup lang="ts">
import CustomLoading from '@/components/customLoading/index.vue'

console.log('general-cs-web 测试发布-app', 20250702)

// 打印当前页面url
console.log('当前页面url', window.location.href)
</script>
<style lang="scss">
// 图片预加载区域
.img-preload {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
  z-index: -1;
  opacity: 0;

  img {
    width: 1px;
    height: 1px;
  }
}

.van-overlay {
  // z-index: 2001 !important;
  // position: fixed !important;
  // width: 100% !important;
  // height: 100% !important;
  // transform: translateZ(0);
  // -webkit-transform: translateZ(0);
}
.van-popup {
  z-index: 99999 !important;
  position: fixed !important;
  width: 100% !important;
  // left: 0 !important;
  // transform: translateZ(0) !important;
  // -webkit-transform: translateZ(0) !important;
}
.classify-popup-container {
  // z-index: 2005 !important;
  position: fixed !important;
  width: 100% !important;
  .classify-popup {
    width: 100% !important;
  }
}
</style>
