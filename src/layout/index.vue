<template>
  <div class="h"></div>
  <div class="cs-body">
		<!-- 私域客服才显示 -->
		<van-nav-bar
			v-if="isPrivZone"
			:title="$t('title_general_my_cs')"
			fixed
			z-index="10"
			safe-area-inset-top
			left-arrow
			@click-left="goBack"
		/>
		<!-- 测试环境显示 -->
		<van-nav-bar
			v-if="!isPrivZone && isTest"
			class="test-nav-bar"
			:title="$t('title_general_my_cs')"
			fixed
			z-index="10"
			safe-area-inset-top
			left-arrow
			@click-left="goBack"
		/>
    <div :class="['position-rel', `lang_${lang}`]">
      <router-view v-slot="{ Component }">
        <keep-alive include="NewSmart, Smart, PcSmart">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script lang="ts">
import { getCurrentInstance, defineComponent, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
// import { useStorage } from '@vueuse/core'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import langConf from '@/assets/lang/index'
// import projectMap from '@/customConfig/projectMap'
// import grayUidList from '@/customConfig/grayList.json'
import { homePageIndex } from '@/api/new'
import { JsHandler } from '@/utils/jsHandler'
import { parseBase64Params } from '@/utils'

export default defineComponent({
	name: 'Layout'
})
</script>
<script setup lang="ts">
import type { TNewCard, Tbanner, TArticle } from '@/enum/types'
interface TindexInfo {
  banner_data: {
    article_lists: TArticle[],
    banner_lists: Tbanner[],
    card_lists: TNewCard[]
  },
  bottom_data: string[],
  is_open_input: boolean
}
const router = useRouter()
const route = useRoute()
const { commit, dispatch, state } = useStore()
const { locale } = useI18n()
const isTest = computed(() => {
  return location.hostname.includes('-test') || location.hostname.includes('localhost') || location.hostname.includes('-stage')
})

// 定制化配置信息
// const storageCustom = useStorage('c', {}, sessionStorage)
// const customInfo  = computed(() => {
// 	if (Object.keys(state.customInfo).length === 0) {
// 		return storageCustom.value
// 	} else {
// 		return state.customInfo
// 	}
// })
const initCs = (): void => {
	const Properties = getCurrentInstance()?.appContext.config.globalProperties
	const params = Properties && Properties.$utils.getParamsFromUrl()
  const url = window.location.href
	const sessonP = sessionStorage.getItem('p')
	if (window.sessionStorage.getItem('p')) {
		const localParams = JSON.parse(window.sessionStorage.getItem('p') as string)
		const lang = localParams.lang && localParams.lang.toLowerCase()
		const newLang = lang === 'ms' ? 'my' : lang
		localParams.lang = Object.keys(langConf.config).indexOf(newLang) > -1 ? newLang : 'en' // 兜底21种语言不存在的语言，默认展示英文
		commit('setUserInfo', localParams)
		locale.value = localParams.lang
		const headerHeight = Number(params.head_height) || Number(localParams.head_height) || 100
		// 开发环境下的调试日志
		if (process.env.NODE_ENV === 'development') {
			console.log('headerHeight:', headerHeight)
		}
		if (url.includes('/qn/questionNaire')) {
      document.documentElement.style.setProperty('--head-bar-height', 0 + 'px')
      document.documentElement.style.setProperty('--head-bar-height-negative', 0 + 'px')
    } else {
		document.documentElement.style.setProperty('--head-bar-height', headerHeight / window.devicePixelRatio + 'px')
		document.documentElement.style.setProperty('--head-bar-height-negative', -((headerHeight) / window.devicePixelRatio) + 'px')
    }
	} else {
		if (Object.keys(params).length > 0) {
			const base64Params = parseBase64Params()
			const lang = params.lang?.toLowerCase() || base64Params.lang?.toLowerCase()
			// 兼容ms改为my
			const newLang = lang === 'ms' ? 'my' : lang
			params.lang = Object.keys(langConf.config).indexOf(newLang) > -1 ? newLang : 'en' // 兜底21种语言不存在的语言，默认展示英文
			// pc端推送气泡和头像点击进入的工单详情页，不做清除url
			if (!base64Params.ticket_id)  {
				// 清除url参数
				router.push({
					path: window.location.pathname,
					query: {}
				})
			}

			commit('setUserInfo', params)
			// PC本地缓存（用于PC刷新页面情况）
			if (window.sessionStorage) {
				window.sessionStorage.setItem('p', JSON.stringify(params))
			}
			locale.value = params.lang
      if (url.includes('/qn/questionNaire')) {
        document.documentElement.style.setProperty('--head-bar-height', 0 + 'px')
        document.documentElement.style.setProperty('--head-bar-height-negative', 0 + 'px')
      } else {
        const headerHeight = Number(params.head_height) || 100
        document.documentElement.style.setProperty('--head-bar-height', headerHeight / window.devicePixelRatio + 'px')
        document.documentElement.style.setProperty('--head-bar-height-negative', -((headerHeight) / window.devicePixelRatio) + 'px')
      }
		} else {
			console.log('缺少参数')
		}
	}
	const mark = JSON.parse(sessionStorage.getItem('mark') ?? 'false')
	console.log('layout mark', mark)
	// 判断是否游戏外场景（游戏正在加载过程中点击联系客服，直接跳工单）
	// scene含义：0：定制版游戏内，3：通用版游戏内，1：加载，2：封号
	if (mark) {
    commit('setLoadingCount', 1)
    // index接口
		homePageIndex({}).then((res: TindexInfo) => {
      commit('setBaseInfo', res)
    }).catch((err: string) => {
      console.log(err)
    }).finally(() => {
      commit('setLoadingCount', 0)
    })
		// 重定向到升级版客服首页
    if (route.path.indexOf('/newcs') > -1) return
		// 从私域背包打开，携带redirect_url=answers_detail&priv_rights_use=1&priv_rights_type=1，跳转到/newcs/answersDetail?cat_id=149
		if (params.redirect_url && params.redirect_url.indexOf('answers_detail') > -1 && params.priv_rights_use && params.priv_rights_type) {
			router.replace('/newcs/answersDetail?cat_id=' + params.priv_rights_type)
		} else {
			router.replace('/newcs')
		}
	} else {
    commit('setLoadingCount', -1)
		const img= require('@/assets/img/csbg.jpg')
		document.getElementById('app')?.setAttribute('style', `background-image: url(${img})`)
		// console.log('layout params.json_data', params.json_data)
		let json_data = null
    try {
			const jsonData = params.json_data || '{}'
      // console.log('layout decode jsonData', decodeURIComponent(jsonData))
      json_data = JSON.parse(decodeURIComponent(jsonData)) || {}
    } catch (error) {
      console.log('layout json_data error', error)
      json_data = {}
    }
		// console.log('layout json_data', json_data)
    if (params.autoUnMatch === 'true' || json_data.autoUnMatch === 1) {
      if (route.path.indexOf('/pc') > -1) {
        router.push('/pc/tickets')
      } else {
        router.push('/tickets')
      }
    }
	}
}
initCs()
const lang = computed(() => state.userInfo.lang)
const isPrivZone = computed(() => state.userInfo?.zone_from === 'privZone')
console.log('isPrivZone', isPrivZone.value)

const goBack = () => {
	// 只有当客服返回到私域的时候，才清空sessionStorage，客服内的返回操作正常进行，从首页点返回一定是返回私域
	if (location.pathname === '/newcs/homePage') {
		sessionStorage.clear()
	}
	router.go(-1)
}

onMounted(() => {
  // 只对私域-初始化时检查是否在 webview 中
  if (isPrivZone.value) {
		JsHandler.IS_WEBVIEW((data) => {
			dispatch("updateGlobalEnvConfig", { isInWebview: data ? true : false })
		})
  }
})
</script>

<style lang="scss" scoped>
// .h{
// 	height: 40Px; background:#1D1D1F;position:absolute;top:0px;left:0px;width:100%;z-index:2
// }
// 私域返回栏样式
:deep .van-nav-bar {
	padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
:deep .van-nav-bar__content {
	height: var(--head-bar-height);
}

.test-nav-bar {
	z-index: 3000;
  background-color: #0E1920;
	:deep .van-nav-bar__title {
		color: #E2C885;
	}
	:deep &:after {
		display: none;
		border: none
	}
}
</style>
