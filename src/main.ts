// import VConsole from 'vconsole'
import { createApp } from 'vue'
import { RendererElement } from '@vue/runtime-core'
import App from './App.vue'
import router from './router'
import store from './store'
import VueAxios from 'vue-axios'
import FpAxios from './server/interceptor'
import '@/styles/reset.scss'
// import '@/styles/common.scss'
import packages from './packages'
import { i18n } from './plugins/i18n' // 直接导入 i18n 实例
import './utils/flexible'
import fpUtils, { Upgrade, parseBase64Params } from './utils'
// 导入 UBT，它会在 window 上注册 UBT 构造函数
import './utils/ubt'

// 保存初始页面URL，用于错误上报
const saveInitHomeUrl = () => {
  try {
    // 使用存储管理器处理本地存储
    if (!localStorage.getItem('init_home_url')) {
      // 清除URL中的敏感参数
      const url = new URL(window.location.href);
      // 移除可能包含敏感信息的参数
      // const sensitiveParams = ['token', 'api_key', 'secret'];
      // sensitiveParams.forEach(param => url.searchParams.delete(param));

      localStorage.setItem('init_home_url', url.toString());
    }
  } catch (error) {
    console.error('Error saving initial URL:', error);
  }
}

// 在应用启动时立即保存初始URL
saveInitHomeUrl()

// 动态加载样式组
let stylePromise: Promise<any> | null = null;

// 获取 @/styles 目录下可用的样式文件夹列表
const availableStyleFolders = [
  'dc', 'eden', 'entropy', 'foundation', 'generalBase', 'gog',
  'koa', 'l', 'mc', 'sky', 'soc', 'ss', 'st', 'st2', 'tides', 'ts', 'worldx'
];

async function loadStyles() {
  console.log('loadStyles')
  // 检查URL中是否有scene参数，需要重置样式缓存
  const params = new URLSearchParams(window.location.search)
  const hasScene = params.has('scene')

  // 如果有scene参数或者没有缓存的样式，需要重新加载
  if (hasScene || !stylePromise) {
    // 有scene时，先清除样式缓存
    if (hasScene) {
      stylePromise = null
    }

    // 创建新的样式加载Promise
    stylePromise = (async () => {
      try {
        // 这里会使用Upgrade内部的缓存机制
        const { mark, projectName } = await Upgrade()
        // mark为 hit为true, 且scene为3时，为true
        console.log('loadStyles mark, projectName', mark, projectName)

        const params = new URLSearchParams(window.location.search)
        const gameName = params.get('game')
        const isPcTicketDetail = location.href.includes('pc/ticket-detail') && gameName
        if(isPcTicketDetail) {
          console.log('load pc ticket detail styles')
          console.log('gameName', gameName)
          await import(`@/styles/${gameName}/style.scss`)
          return false
        }

        if(!mark) {
          console.log('load mark styles')
          if(projectName && availableStyleFolders.includes(projectName as string)) {
            console.log('load project styles', projectName)
            await import(`@/styles/${projectName}/style.scss`)
          } else {
            console.log('load generalBase styles')
            await import('@/styles/generalBase/style.scss')
          }
        } else {
          console.log('load default styles')
          await import('@/styles/style.scss')
        }
      } catch (error) {
        console.error('Error while loading styles:', error)
        // 加载失败时，使用默认样式作为fallback
        try {
          console.log('load default styles')
          await import('@/styles/style.scss')
        } catch (fallbackError) {
          console.error('Failed to load fallback styles:', fallbackError)
        }
      }
    })();
  }

  return stylePromise;
}

import setupPlugins  from './plugins'
import "@/assets/iconfont/iconfont.css"

async function startApp() {
  await loadStyles() // 等待loadStyles函数执行完成

   // 判断环境并初始化
   const env = window.location.href.includes("test") ? "test" : window.location.href.includes("localhost") ? "development" : "production";
  //  console.log('env', env)
   // 初始化 UBT，window.UBT 来自 ./utils/ubt.js
   if (typeof window.UBT === 'function') {
     window.ubt = new window.UBT(env);
   } else {
     console.error('UBT not loaded properly');
   }

   // 更新 UBT 选项
   window.ubt?.updateOptions({
    agent: {
      appName: "Custom Web",
      // appVer: "<%= appVer %>",
    }
  });

  // 调试模式
	// if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development' || location.host === 'fpcs-web-test.funplus.com' || location.host === 'fpcs-web-test.funplus.com.cn') {
	// 	new VConsole()
	// }

	const app = createApp(<RendererElement>App)

	// // 添加全局错误处理
	// app.config.errorHandler = (err, vm, info) => {
	// 	// 保留原始错误信息，但不阻止错误传播
	// 	console.error('Vue Error:', err)
	// 	console.error('Component:', vm?.$options.name || 'Unknown component')
	// 	console.error('Error Info:', info)

	// 	// 将错误重新抛出，以便浏览器可以显示完整的错误堆栈
	// 	setTimeout(() => {
	// 		throw err;
	// 	}, 0);
	// }

	// setupI18n(app)
	app
		.use(i18n)
		.use(store)
		.use(router)
		.use(VueAxios, FpAxios.init())
		.mount('#app')
	app
		.use(packages)
		.use(fpUtils)
	setupPlugins(app)
}
startApp()
