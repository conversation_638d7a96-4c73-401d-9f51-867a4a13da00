import FpRequest from '../server'

// 对话API
export const getAnswer: ApiT = params => FpRequest.post('/backend/v2/question/answer', params)
// 获取热门大卡ID
export const hotCard: ApiT = params => FpRequest.post('/backend/v1/question/hot_cardid', params)
// 获取卡片信息
export const getCards: ApiT = params => FpRequest.post('/backend/v1/question/card', params)
// 自助查询
export const getSelfQuery: ApiT = params => FpRequest.post('/backend/v2/question/self_check', params)
// 点赞点踩
export const sendSolve: ApiT = params => FpRequest.post('/backend/v1/question/score', params)
// 点踩原因列表
export const dislikeOpt: ApiT = params => FpRequest.post('/backend/v1/dislike/opt', params)
// 点踩原因提交
export const dislikeSave: ApiT = params => FpRequest.post('/backend/v1/dislike/save', params)
// 精灵点踩原因提交
export const newDislikeSave: ApiT = params => FpRequest.post('/backend/v3/elfin/dislike/save', params)
// 点踩关联工单列表
export const tkCats: ApiT = params => FpRequest.post('/backend/v2/question/tk_cats', params)
// 检查是否有未读工单
export const checkUnread: ApiT = params => FpRequest.post('/backend/v1/egress/msg/notice', params)
