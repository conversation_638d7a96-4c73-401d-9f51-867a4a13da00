import FpRequest from '../server'

// const baseUrl = 'http://10.9.54.184:9000'

const baseUrl = '/backend/v4/opts'

// 首页获取继续对话状态
export const getContinueConversationStatus = () => FpRequest.post(`${baseUrl}/egress/conversation/continue`)

// 关闭继续对话状态
export const closeContinueConversationStatus: ApiT = (params: any) => FpRequest.post(`${baseUrl}/egress/conversation/close`, params)

// 流程+工单模板返回接口
export const flowAndTemplateConversation: ApiT = params => FpRequest.post(`${baseUrl}/egress/cat/info`, params)

// 对话创建/更新对话接口 -- 存储用户对话历史
export const updateConversation = (params: any) => FpRequest.post(`${baseUrl}/egress/conversation/update`, params)

// 对话历史接口 - 恢复对话接口
export const getConversationHistory = (params: any) => FpRequest.post(`${baseUrl}/egress/conversation/history`, params)

// 对话交互接口
export const chatConversation = (params: any) => FpRequest.post(`${baseUrl}/egress/conversation/chat`, params)

// 创建对话工单提交接口-参考原接口 createTicket- egress/ticket/create，返回值中包含ticket_id，区别是入参新增conversation_id
export const createConversationTicket = (params: any) => FpRequest.post(`${baseUrl}/egress/conversation/create`, params)
