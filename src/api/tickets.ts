import FpRequest from '../server'

// 获取入口树
export const sceneEntrance: ApiT = params => FpRequest.post('/backend/v1/egress/scene/cats', params)
// 获取模板
export const getTplInfo: ApiT = params => FpRequest.post('/backend/v1/egress/tpl/info', params)
// 获取玩家头像
export const getUserInfo: ApiT = params => FpRequest.post('/backend/v1/question/user_info', params)
// 提交工单
export const createTicket: ApiT = params => FpRequest.post('/backend/v1/egress/ticket/create', params)
// 已提交工单详情
export const ticketDetail: ApiT = params => FpRequest.post('/backend/v1/egress/ticket/detail', params)
// pc 工单详情
export const pcTicketDetail: ApiT = params => FpRequest.post('/backend/v4/opts/egress/ticket/rdo/detail', params)
// 历史工单列表
export const getHistoryList: ApiT = params => FpRequest.post('/backend/v1/egress/ticket/mine', params)
// 补填工单
export const completeTicket: ApiT = params => FpRequest.post('/backend/v1/egress/ticket/replenish', params)
// 工单评价
export const appraiseTicket: ApiT = params => FpRequest.post('/backend/v1/egress/ticket/appraise', params)
// 自动化流程
export const autoFlow: ApiT = params => FpRequest.post('/backend/v1/egress/process/next', params)
// 流程+工单模板返回接口
export const flowAndTemplate: ApiT = params => FpRequest.post('/backend/v1/egress/cat/info', params)
// 工单重开
export const reopenTicket: ApiT = (params) => FpRequest.post('/backend/v1/egress/ticket/reopen', params)
// 工单评价反馈接口
export const appraiseFeedback: ApiT = (params) => FpRequest.post('/backend/v1/egress/ticket/appraise/feedback', params)
// 玩家给客服发消息
export const playerSendMsg: ApiT = (params) => FpRequest.post('/backend/v1/egress/ticket/communicate', params)
// 获取关联工单任意层级及其下属的所有问题分类
export const getRelationCats: ApiT = (params) => FpRequest.post('/backend/v1/egress/scene/cats/info', params)
