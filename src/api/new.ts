import FpRequest from '../server'

// 客服升级首页信息
export const homePageIndex: ApiT = params => FpRequest.post('/backend/v3/elfin/index', params)
export const secondCards: ApiT = params => FpRequest.post('/backend/v3/elfin/temp/sub_card', params)
// 搜索联想词
export const getHotQList: ApiT = (params) => FpRequest.post('/backend/v3/elfin/fuzzy_query', params)
// 点赞点踩
export const pushChatAppraise: ApiT = (params) => FpRequest.post('/backend/v3/elfin/chat_appraise', params)
// 文章详情/知识
export const getArticleDetail: ApiT = (params) => FpRequest.post('/backend/v3/elfin/article', params)
// 纠错弹窗
export const getCorrection: ApiT = (params) => FpRequest.post('/backend/v3/elfin/correction/report', params)