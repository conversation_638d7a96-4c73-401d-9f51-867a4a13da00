import { RouteRecordRaw } from 'vue-router'
interface CompPromise {
	(): Promise<typeof import("*.vue")>
}
const Layout: CompPromise = () => import('@/layout/index.vue')
const Smart: CompPromise = () => import('@/views/Smart.vue')
const Tickets: CompPromise = () => import('@/views/Tickets.vue')
const Detail: CompPromise = () => import('@/views/Detail.vue')
const History: CompPromise = () => import('@/views/History.vue')
const Complete: CompPromise = () => import('@/views/Complete.vue')
const Appraise: CompPromise = () => import('@/views/Appraise.vue')

// PC端
const PcSmart: CompPromise = () => import('@/views/PC/Smart.vue')
const PcTickets: CompPromise = () => import('@/views/PC/Tickets.vue')
const PcDetail: CompPromise = () => import('@/views/PC/Detail.vue')
const PcHistory: CompPromise = () => import('@/views/PC/History.vue')
const PcComplete: CompPromise = () => import('@/views/PC/Complete.vue')
const PcAppraise: CompPromise = () => import('@/views/PC/Appraise.vue')
const PcTicketDetail: CompPromise = () => import('@/views/PC/TicketDetail.vue')

// newcs
const HomePage: CompPromise = () => import('@/views/New/HomePage.vue')
const ArticleDetail: CompPromise = () => import('@/views/New/ArticleDetail.vue')
const NewSmart: CompPromise = () => import('@/views/New/NewSmart.vue');
const NewHistory: CompPromise = () => import('@/views/New/History.vue')
const AnswersDetail: CompPromise = () => import('@/views/New/AnswersDetail.vue')
const SecondLevel: CompPromise = () => import('@/views/New/SecondLevel.vue')
const ClassifyQuestion: CompPromise = () => import('@/views/New/ClassifyQuestion.vue')
const ClassifyQuestionSub: CompPromise = () => import('@/views/New/ClassifyQuestionSub.vue')
const ClassifyQuestionThird: CompPromise = () => import('@/views/New/ClassifyQuestionThird.vue')
const TicketDetail: CompPromise = () => import('@/views/New/TicketDetail.vue')
const TicketAppraise: CompPromise = () => import('@/views/New/TicketAppraise.vue')
const TicketComplete: CompPromise = () => import('@/views/New/TicketComplete.vue')
const oldTicketAppraise: CompPromise = () => import('@/views/New/oldTicketAppraise.vue')
// 新增工单对话页面
const TicketConversation: CompPromise = () => import('@/views/New/TicketConversation.vue')
// 问卷调查
const questionNaire: CompPromise = () => import('@/views/QN/questionnaire.vue')

export const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    redirect: '/smart',
    children: [
      {
        path: '/smart',
        name: 'Smart',
        component: Smart,
      },
      {
        path: '/tickets',
        name: 'Tickets',
        component: Tickets,
      },
      {
        path: '/detail',
        name: 'Detail',
        component: Detail,
      },
      {
        path: '/history',
        name: 'History',
        component: History,
      },
      {
        path: '/complete',
        name: 'Complete',
        component: Complete,
      },
      {
        path: '/appraise',
        name: 'Appraise',
        component: Appraise,
      },
      // PC路由
      {
        path: '/pc',
        redirect: '/pc/smart',
      },
      {
        path: '/pc/smart',
        name: 'PcSmart',
        component: PcSmart,
      },
      {
        path: '/pc/tickets',
        name: 'PcTickets',
        component: PcTickets,
      },
      {
        path: '/pc/detail',
        name: 'PcDetail',
        component: PcDetail,
      },
      {
        path: '/pc/history',
        name: 'PcHistory',
        component: PcHistory,
      },
      {
        path: '/pc/complete',
        name: 'PcComplete',
        component: PcComplete,
      },
      {
        path: '/pc/appraise',
        name: 'PcAppraise',
        component: PcAppraise,
      },
      {
        path: '/pc/ticket-detail',
        name: 'PcTicketDetail',
        component: PcTicketDetail,
      },
      // newcs路由
      {
        path: '/newcs',
        redirect: '/newcs/homePage',
      },
      {
        path: '/newcs/homePage',
        name: 'HomePage',
        component: HomePage,
      },
      {
        path: '/newcs/articleDetail',
        name: 'ArticleDetail',
        component: ArticleDetail,
      },
      {
        path: '/newcs/newSmart',
        name: 'NewSmart',
        component: NewSmart,
      },
      {
        path: '/newcs/newHistory',
        name: 'NewHistory',
        component: NewHistory,
      },
      {
        path: '/newcs/answersDetail',
        name: 'AnswersDetail',
        component: AnswersDetail,
      },
      {
        path: '/newcs/secondLevel',
        name: 'SecondLevel',
        component: SecondLevel,
      },
      {
        path: '/newcs/classifyQuestion',
        name: 'ClassifyQuestion',
        component: ClassifyQuestion,
      },
      {
        path: '/newcs/classifyQuestionSub',
        name: 'ClassifyQuestionSub',
        component: ClassifyQuestionSub,
      },
      {
        path: '/newcs/classifyQuestionThird',
        name: 'ClassifyQuestionThird',
        component: ClassifyQuestionThird,
      },
      {
        path: '/newcs/ticketDetail',
        name: 'TicketDetail',
        component: TicketDetail,
      },
      {
        path: '/newcs/ticketAppraise',
        name: 'TicketAppraise',
        component: TicketAppraise,
      },
      {
        path: '/newcs/ticketComplete',
        name: 'TicketComplete',
        component: TicketComplete,
      },
      // 新增工单对话页面路由
      {
        path: '/newcs/ticketConversation',
        name: 'TicketConversation',
        component: TicketConversation,
      },
      // 临时 兼容新老工单
      {
        path: '/newcs/oldticketAppraise',
        name: 'oldTicketAppraise',
        component: oldTicketAppraise,
      },
      // 推送问卷调查
      {
        path: '/qn',
        redirect: '/qn/questionNaire',
      },
      {
        path: '/qn/questionNaire',
        name: 'questionNaire',
        component: questionNaire,
      },
    ],
  },
];
