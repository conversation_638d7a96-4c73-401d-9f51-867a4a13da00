/*
 * @Author: wenh<PERSON>.wang 
 * @Date: 2022-05-09 11:13:17 
 * @Last Modified by:   wenhao.wang 
 * @Last Modified time: 2022-05-09 11:13:17 
 */
import { App as Application } from 'vue'
/* eslint-disable */ 
export const withInstall = (custom: any): typeof custom => {
  custom.install = (app: Application): void => {
    for (const comp of [custom]) {
      app.component(comp.name, comp)
    }
  }
  return custom
}
