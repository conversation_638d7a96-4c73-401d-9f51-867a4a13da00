<template>
  <div class="pc_box">
    <div class="pc_head" v-if="!isNoHead && !isNoFp && !isKoaRom && !isWorldX">
      <div class="fp_logo"></div>
    </div>
    <div :class="['pc_body', isNoHead || isKoaRom ? 'nohead' : '', isNoFp ? 'noFP' : '']">
      <slot></slot>
    </div>
    <div v-if="!isNoFp && !isWorldX">
      <div class="pc_foot" v-if="isNoHead">
        <a href="https://glc.haowancheng.cn/" target="_blank">归龙潮官网</a>
        <span>沪ICP备2022031013号-1</span>
        <span> Copyright © 2023|上海好玩橙信息科技有限公司. All Rights Reserved.</span>
      </div>
      <div class="pc_foot" v-else-if="isKoaRom">
        <a style="text-decoration:none;cursor: inherit;">© Puzala 2024</a>
        <a href="https://www.puzala.com/privacy-policy/" target="_blank">Privacy Policy</a>
        <a href="https://www.puzala.com/terms-of-service/" target="_blank">Terms & Conditions</a>
      </div>
      <div class="pc_foot" v-else>
        <a style="text-decoration:none;cursor: inherit;">© FunPlus 2022</a>
        <a href="https://funplus.com/privacy-policy-en-as/" target="_blank">Privacy Policy</a>
        <a href="https://funplus.com/terms-conditions-en-as/" target="_blank">Terms & Conditions</a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue"
import { useStore } from 'vuex'
export default defineComponent({
  name: 'PcWrapper'
})
</script>
<script lang="ts" setup>
const { state } = useStore()
const userInfo = computed(() => state.userInfo)
const isNoHead = computed(() => userInfo.value.fpx_app_id && userInfo.value.fpx_app_id === 'deep.cn.prod')
const isNoFp = computed(() => userInfo.value.open_source === 'yandex')
const isKoaRom = computed(() => userInfo.value.channel === 'rom_global_pc')
const isWorldX = location.hostname === 'fpcs-web.kingsglorygames.com' || location.hostname === 'localhost'
</script>

<style lang="scss" scoped>
.pc_box {
  height: 100%;
  width: 100%;
  background: url('~@/assets/img/csbg.jpg') no-repeat;
  background-size: cover;
  background-position: center center;
  overflow: hidden;
}
.pc_head {
  width: 100%;
  height: 55Px;
  overflow: hidden;
  min-width: 720Px;
  background: rgba(0, 0, 0, 0.7);
  .fp_logo {
    width: 110Px;
    height: 26Px;
    background: url('~@/assets/img/fp_logo.png') no-repeat;
    background-position: center center;
    margin: 13Px 0px 0px 25Px;
    background-size: 110Px 26Px;
  }
}
.pc_foot {
  width: 100%;
  height: 45Px;
  background: rgba(0, 0, 0, 0.7);
  text-align: center;
  min-width: 720Px;
  a {
    color: #FF5A00;
    text-decoration: underline;
    line-height: 45Px;
    font-size: 14Px;
    display: inline-block;
    margin: 0 70Px;
    cursor: pointer;
  }
  span {
    color: #FF5A00;
    font-size: 14Px;
    margin: 0 70Px;
    display: inline-block;
    text-decoration: none;
  }
}
.pc_body {
  height: calc(100% - 100Px);
  min-width: 720Px;
}
.nohead {
  height: calc(100% - 45Px);
}
.noFP {
  height: calc(100%);
}
</style>
