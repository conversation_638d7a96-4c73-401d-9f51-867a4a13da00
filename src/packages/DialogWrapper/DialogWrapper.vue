<template>
  <div class="dialog_box clearfix">
    <div :class="['dialog_wrapper', isReq ? 'rightDialog' : '', leftTopIcon ? 'leftTopIcon' : '']">
      <div class="icon_h"></div>
      <div class="icon_l"></div>
      <slot></slot>
    </div>
  </div>
</template>

<script lang='ts'>
import { defineComponent, withDefaults, defineProps } from 'vue'
interface Props {
  isReq?: boolean,
  leftTopIcon?: boolean
}
export default defineComponent({
  name: 'DialogWrapper'
})
</script>
<script lang="ts" setup>
withDefaults(defineProps<Props>(), {
  isReq: false,
  leftTopIcon: false
})
</script>

<style lang="scss" scoped>

.dialog_box {
  display: block;
  // overflow: hidden;
}
.dialog_wrapper {
  border: 2px solid rgba(190, 165, 95, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  padding: 26.68px 25.346px;
  border-radius: 10px;
  float: left;
  font-size: 34.684px;
  margin: 26.68px 0px 13.34px;
  color: #CECFC9;
  max-width: 1317.992px;
  // overflow: hidden;
  word-break: break-all;
}
.moreBox .dialog_wrapper {
	padding: 0px;
}
.rightDialog {
  float: right;
}
.leftTopIcon {
  position: relative;
  .icon_l {
    content: '';
    position: absolute;
    left: -4Px;
    top: 4Px;
    height: 1.5Px;
    opacity: 0.8;
    z-index: 1;
    width: 96.048px;
    background: linear-gradient(to right, #F5C133, rgba(245, 212, 121, 0));
  }
  .icon_h {
    content: '';
    position: absolute;
    left: 4Px;
    top: -4Px;
    height: 96.048px;
    opacity: 0.8;
    z-index: 1;
    width: 1.5Px;
    background: linear-gradient(to bottom, #F5C133, rgba(245, 212, 121, 0));
  }
}
@media all and (orientation : portrait) {
  .dialog_wrapper {
    max-width: 12.75rem;
    margin: 0.5rem 0px 0.25rem;
    padding: 0.4rem 0.2rem;
    font-size: 0.46rem;
    background: #ffffff;
    color: #000;
    border: 1Px solid rgba(252, 104, 5, 0.2);
    border-radius: 5Px;
  }
  .leftTopIcon {
    .icon_l {
      width: 1.29rem;
      border-radius: 10Px;
      background: linear-gradient(to right, #ff7415, rgba(245, 212, 121, 0));
    }
    .icon_h {
      height: 1.29rem;
      border-radius: 10Px;
      background: linear-gradient(to bottom, #ff7415, rgba(245, 212, 121, 0));
    }
  }
}
</style>
