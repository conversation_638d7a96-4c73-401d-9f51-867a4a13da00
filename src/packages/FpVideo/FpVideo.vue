<template>
	<video-play v-bind="options"></video-play>
</template>
<script lang="ts">
	import { defineComponent, reactive, defineProps, toRefs } from 'vue'
	import VideoPlay from './VideoPlay'
	export default defineComponent({
		name: 'FpVideo',
		components: { VideoPlay }
	})
</script>
<script setup lang="ts">
	const props = defineProps<{
		src: string
	}>()
	const data: {
		options: {
			width: string,
			height: string,
			src: string,
			autoPlay: boolean,
			control: boolean,
			controlBtns: Array<string>
		}
	} = reactive({
		options: {
			width: '100%',
			height: '100%',
			src: props.src,
			autoPlay: false,
			control: true,
			controlBtns: ['speedRate', 'volume']
		}
	})

	const { options } = toRefs(data)
</script>

<style scoped>

</style>