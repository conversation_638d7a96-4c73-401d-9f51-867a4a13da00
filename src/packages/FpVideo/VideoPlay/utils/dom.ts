export const on = function (
    element: Element | HTMLElement | Document | Window,
    event: string,
    handler: EventListenerOrEventListenerObject,
    useCapture = false,
): void {
    if (element && event && handler) {
        element.addEventListener(event, handler, useCapture)
    }
}
/* istanbul ignore next */
export const off = function (
    element: Element | HTMLElement | Document | Window,
    event: string,
    handler: EventListenerOrEventListenerObject,
    useCapture = false,
): void {
    if (element && event && handler) {
        element.removeEventListener(event, handler, useCapture)
    }
}
