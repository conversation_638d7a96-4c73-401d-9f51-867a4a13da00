<template>
  <div class="auto-font-size">
    <span ref="innerSpan">{{ text }}</span>
  </div>
</template>

<script lang="ts">
import { defineComponent, withDefaults, defineProps, onMounted, ref, getCurrentInstance } from 'vue'
  interface Props {
    text: string
  }
  export default defineComponent({
    name: 'AutoFontSize'
  })
</script>
<script lang="ts" setup>
  withDefaults(defineProps<Props>(), {
    text: ''
  })
  const innerSpan = ref()
  // eslint-disable-next-line
  const { proxy: _this } = getCurrentInstance() as any
  onMounted(() => {
    const outW = _this.$el.offsetWidth
    const innerW = innerSpan.value.offsetWidth
    const z = (outW < innerW) ? outW / innerW : 1
    innerSpan.value.style.transform = `scale(${z})`
    _this.$el.style.opacity = 1
  })
</script>

<style lang='scss' scoped >
.auto-font-size {
  text-align: center;
  opacity: 0;
  span {
    display: inline-block;
    white-space: nowrap;
    transform-origin: left center;
  }
}
.lang_ar{
  .auto-font-size{
    span{
      transform-origin: right center;
    }
  }
}
</style>
