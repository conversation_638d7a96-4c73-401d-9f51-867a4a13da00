<template>
  <div class="img-wrap">
    <div class="img-container" v-for="(img, index) in imgList" :key="index">
      <img :src="img.url" alt="" @click="previewImg(img)" />
      <div class="delete" @click="removeUploadedImg(index)"></div>
    </div>
    <div v-if="failed" class="img-failed">
      <van-icon name="warning-o" />
      <div>{{ $t("text_upload_failed") }}</div>
      <div class="delete" @click="removeFailedImg"></div>
    </div>
    <div v-if="uploading" class="img-uploading">
      {{ $t("text_uploading") }}...
    </div>
    <!-- SDK上传 -->
    <div
      v-if="!uploading && imgList.length < 20 && !isH5Upload && !browserIsPC"
      class="img-upload"
      @click="uploadImg"
    >
    </div>
    <!-- H5上传 -->
    <div v-if="(isH5Upload && !uploading && imgList.length < 20) || browserIsPC" class="pc-box">
      <van-uploader :after-read="afterRead" :result-type="'file'" accept="image/*" class="pc-upload" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, getCurrentInstance, computed, onBeforeUnmount, defineProps, withDefaults } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { ImagePreview, Toast } from 'vant'
import { uploadSever } from '@/utils/upload'
import { browser } from '@/utils'
export default defineComponent({
  name: 'ImgUpload'
})
</script>
<script lang="ts" setup>
// eslint-disable-next-line
const { proxy: _this, appContext } = getCurrentInstance() as any
const { commit, state } = useStore()
const { t: $t } = useI18n()
const browserIsPC = browser.version.isWindows || false // todo, 先写死，后续接入sdk时再处理
console.log('ImgUpload browserIsPC', browserIsPC)
// 上传状态机
const isUploading = computed(() => state.uploading)
const props = withDefaults(defineProps<{
  isH5Upload: boolean
}>(), {
  isH5Upload: false
})
const data: {
  uploading: boolean,
  failed: boolean,
  imgList: Array<Record<string, string>>
} = reactive({
  uploading: false,
  failed: false,
  imgList: []
})
// SDK交互初始化
const initSDKCallback = () => {
  window.chooseFinish = params => {
    chooseFinish(params)
  }
  window.backImgUrl = (params: string): void => {
    uploadFinish(params)
  }
}
// 组件销毁要更改状态机
onBeforeUnmount(() => {
  data.uploading = false
  commit('setUploadingType', false) // 同步上传状态机
})
const uploadUrl: Record<string, string> = {
  'fpcs-web-test.funplus.com': 'https://upload-api-test.funplus.com',
  'fpcs-web-stage.funplus.com': 'https://upload-api-test.funplus.com',
  'fpcs-web.funplus.com': 'https://upload-global.funplus.com',
  'fpcs-web-test.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
  'fpcs-web-stage.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
  'fpcs-web.funplus.com.cn': 'https://upload-api.funplus.com.cn',
  'fpcs-web-tx.kingsgroup.cn': 'https://upload-global.funplus.com',
  'fpcs-web-tx.yoo-mei.cn': 'https://upload-global.funplus.com',
  'fpcs-web.nenglianghe.cn': 'https://upload-api.funplus.com.cn',
}
// H5上传
const afterRead = (file: Record<string, unknown>) => {
  data.uploading = true
  commit('setUploadingType', true)
  uploadSever(file.file as string, 'https://upload-global.funplus.com/api/storage/put').then((res: []) => {
    data.imgList.push(...res)
    _this.$forceUpdate()
    _this.$emit('success', res)
    // 重置上传状态机
    data.uploading = false
    commit('setUploadingType', false)
  }, err => {
    console.log('failed', err)
    data.failed = true
    // 重置上传状态机
    data.uploading = false
    commit('setUploadingType', false)
  })
}
// 上传图片(SDK)
const uploadImg = () => {
  // 如果上传流程被占用，则提示上传中
  if (isUploading.value) {
    Toast($t('text_uploading'))
  } else {
    initSDKCallback()
    const path = 'openFile'
    const count = 20 - data.imgList.length
    const param = {
      count: count < 5 ? count : 5,
      albumType: 1,
      storage: 1,
      // fileUploadUrl: location.host === '**********:9108' ? 'http://**********:8074' : 'https://upload-api.funplus.com'
      fileUploadUrl: uploadUrl[location.host] ? uploadUrl[location.host] : 'https://upload-global.funplus.com'
    }
    appContext.config.globalProperties.$utils.jsBridge(path, param)
  }
}
// SDK选择图片后callback
const chooseFinish = (params: unknown): void => {
  if (params) {
    commit('setUploadingType', true) // 同步上传状态机
    data.uploading = true
  }
}
// SDK上传完成callback
const uploadFinish = (params: string): void => {
  if (JSON.parse(params).code === 0) {
    data.imgList.push(...JSON.parse(params).data)
    _this.$forceUpdate()
    _this.$emit('success', JSON.parse(params).data)
  } else {
    data.failed = true
  }
  data.uploading = false
  commit('setUploadingType', false) // 同步上传状态机
}
// 图片预览
const previewImg = (img: Record<string, string>): void => {
  const parentDom = document.getElementById('t-container')
  ImagePreview({
    images: [img.url],
    teleport: 'body',
    closeable: props.isH5Upload,
    showIndex: false
  })
}
// 删除图片
const removeUploadedImg = (index: number): void => {
  data.imgList.splice(index, 1)
  _this.$emit('remove', index)
}
// 删除上传失败的图片
const removeFailedImg = () => {
  data.failed = false
}
const { uploading, failed, imgList } = toRefs(data)
</script>

<style lang="scss" scoped>
.img-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  .img-container {
    position: relative;
    margin: 0 21.344px 21.344px 0;
  }
  img {
    width: 266.8px;
    height: 266.8px;
    object-fit: cover;
    display: block;
  }
  .delete {
    cursor: pointer;
    width: 64.032px;
    height: 64.032px;
    background: url("~@/assets/img/delete.png") no-repeat;
    background-size: cover;
    background-position: center;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4Px;
    position: absolute;
    top: 10.672px;
    right: 10.672px;
  }
  .img-failed {
    width: 266.8px;
    height: 266.8px;
    background: url("~@/assets/img/uploading.png") no-repeat;
    background-size: 100% 100%;
    text-align: center;
    margin: 0 21.344px 21.344px 0;
    padding-top: 66.7px;
    position: relative;
  }
  .img-uploading {
    width: 266.8px;
    height: 266.8px;
    background: url("~@/assets/img/uploading.png") no-repeat;
    background-size: 100% 100%;
    text-align: center;
    line-height: 266.8px;
  }
  .img-upload {
    width: 266.8px;
    height: 266.8px;
		box-sizing: border-box;
		border: 2Px solid #817042;
    background: rgba(103, 89, 58, 0.3) url("~@/assets/img/plus.png") no-repeat center;
    background-size: 133.4px;
  }
}
.pc-box {
  width: 266.8px;
  height: 266.8px;
  border: 2Px solid #817042;
  background: rgba(103, 89, 58, 0.3) url("~@/assets/img/plus.png") no-repeat center;
  background-size: 133.4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  .pc-upload {
    cursor: pointer;
    width: 266.8px;
    height: 266.8px;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 6;
    opacity: 0;
    filter: alpha(opacity=0);
    & ::v-deep .van-uploader__input {
      width: 266.8px;
      height: 266.8px;
    }
  }
}
@media all and (orientation : portrait) {
  .img-wrap {
    .img-container{
      margin: 0 0.286rem 0.286rem 0;
    }
    img {
      width: 3.57rem;
      height: 3.57rem;
    }
    .delete {
      width: 0.86rem;
      height: 0.86rem;
      top: 0.143rem;
      right: 0.143rem;
    }
    .img-failed {
      width: 3.57rem;
      height: 3.57rem;
      margin: 0 0.286rem 0.286rem 0;
      padding-top: 0.893rem;
    }
    .img-uploading {
      width: 3.57rem;
      height: 3.57rem;
      line-height: 3.57rem;
    }
    .img-upload {
      width: 3.57rem;
      height: 3.57rem;
      background-size: 1.786rem;
    }
  }
  .pc-box {
    width: 3.57rem;
    height: 3.57rem;
    border: 2Px solid #bea55f;
    background: rgba(103, 89, 58, 0.3) url("~@/assets/img/plus.png") no-repeat center;
    background-size: 1.6rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    .pc-upload {
      cursor: pointer;
      width: 3.57rem;
      height: 3.57rem;
      position: absolute;
      top: 0px;
      left: 0px;
      z-index: 6;
      opacity: 0;
      filter: alpha(opacity=0);
      & ::v-deep .van-uploader__input {
        width: 3.57rem;
        height: 3.57rem;
      }
    }
  }
}
</style>
