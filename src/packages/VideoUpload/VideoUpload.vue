<template>
  <div class="video-wrap">
    <div v-if="videoList.length !== 0" class="video-preview">
			<fp-video
        class="video-pre"
				:src="videoList[0].url"
			></fp-video>
      <div class="delete" @click="removeUploadedVideo"></div>
    </div>
    <div v-if="failed" class="video-failed">
      <van-icon name="warning-o" />
      <div>{{ $t("text_upload_failed") }}</div>
      <div class="delete" @click="removeFailedVideo"></div>
    </div>
    <div v-if="uploading" class="video-uploading">
      {{ $t("text_uploading") }}...
    </div>
		<!-- SDK上传 -->
    <div
      v-if="!uploading && videoList.length === 0 && !isH5Upload && !browserIsPC"
      class="video-upload"
      @click="uploadVideo"
    >
    </div>
		<!-- H5上传 -->
		<div v-if="(isH5Upload && !uploading && videoList.length === 0) || browserIsPC" class="pc-box">
      <van-uploader :max-size="50 * 1024 * 1024" @oversize="onOversize" :after-read="afterRead" :result-type="'file'" accept="video/*" class="pc-upload" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, getCurrentInstance, computed, onBeforeUnmount, defineProps, withDefaults } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { Toast } from 'vant'
import { uploadSever } from '@/utils/upload'
import { browser } from '@/utils'

const browserIsPC = browser.version.isWindows || false // todo, 先写死，后续接入sdk时再处理
// console.log('VideoUpload browserIsPC', browserIsPC)
interface Props {
  isH5Upload: boolean
}
export default defineComponent({
	name: 'VideoUpload'
})
</script>
<script lang="ts" setup>
// eslint-disable-next-line
const { proxy: _this, appContext } = getCurrentInstance() as any
const { commit, state } = useStore()
const { t: $t } = useI18n()
withDefaults(defineProps<Props>(), {
  isH5Upload: false
})
const data: {
	uploading: boolean,
	failed: boolean,
	videoList: Array<Record<string, string>>
} = reactive({
	uploading: false,
	failed: false,
	videoList: []
})
// 上传状态机
const isUploading = computed(() => state.uploading)
// SDK交互初始化
const initSDKCallback = () => {
	window.chooseFinish = (params: string) => {
		chooseFinish(params)
	}
	window.backImgUrl = (params: string): void => {
		uploadFinish(params)
	}
}
// 组件销毁要更改状态机
onBeforeUnmount(() => {
	data.uploading = false
	commit('setUploadingType', false) // 同步上传状态机
})
const uploadUrl: Record<string, string> = {
	'fpcs-web-test.funplus.com': 'https://upload-api-test.funplus.com',
	'fpcs-web-stage.funplus.com': 'https://upload-api-test.funplus.com',
	'fpcs-web.funplus.com': 'https://upload-global.funplus.com',
	'fpcs-web-test.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
	'fpcs-web-stage.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
	'fpcs-web.funplus.com.cn': 'https://upload-api.funplus.com.cn',
	'fpcs-web-tx.kingsgroup.cn': 'https://upload-global.funplus.com',
	'fpcs-web-tx.yoo-mei.cn': 'https://upload-global.funplus.com',
	'fpcs-web.nenglianghe.cn': 'https://upload-api.funplus.com.cn',
}
// 上传视频
const uploadVideo = () => {
	// 如果上传流程被占用，则提示上传中
	if (isUploading.value) {
		Toast($t('text_uploading'))
	} else {
		initSDKCallback()
		const path = 'openFile'
		const param = {
			count: 1,
			albumType: 2,
			storage: 1,
			fileUploadUrl: uploadUrl[location.host] ? uploadUrl[location.host] : 'https://upload-global.funplus.com',
		}
		appContext.config.globalProperties.$utils.jsBridge(path, param)
	}
}
// SDK选择视频后callback
const chooseFinish = (params: string): void => {
	if (JSON.parse(params).code === 0) {
		commit('setUploadingType', true) // 同步上传状态机
		data.uploading = true
	} else if (JSON.parse(params).code === 11000) {
		Toast($t('text_video_exceed_50M'))
	}
}
// SDK上传完成callback
const uploadFinish = (params: string): void => {
	if (JSON.parse(params).code === 0) {
		data.videoList.push(...JSON.parse(params).data)
		_this.$forceUpdate()
		_this.$emit('success', JSON.parse(params).data)
	} else {
		data.failed = true
	}
	data.uploading = false
	commit('setUploadingType', false) // 同步上传状态机
}
// 删除视频
const removeUploadedVideo = (): void => {
	data.videoList.splice(0, 1)
	_this.$emit('remove')
}
// 删除上传失败的视频
const removeFailedVideo = () => {
	data.failed = false
}
// H5上传超出适配大小限制
const onOversize = () => {
	Toast($t('text_video_exceed_50M'))
}
// H5上传视频
const afterRead = (file: Record<string, unknown>) => {
	data.uploading = true
	commit('setUploadingType', true)
	uploadSever(file.file as string, 'https://upload-global.funplus.com/api/storage/put').then(res => {
		data.videoList.push(...res)
		_this.$forceUpdate()
		_this.$emit('success', res)
		// 重置上传状态机
		data.uploading = false
		commit('setUploadingType', false)
	}, err => {
		console.log('failed', err)
		data.failed = true
		// 重置上传状态机
		data.uploading = false
		commit('setUploadingType', false)
	})
}
const { uploading, failed, videoList } = toRefs(data)
</script>

<style lang="scss" scoped>
.video-wrap {
  display: flex;
  .video-preview {
    width: 40vw;
    // height: 40vh;
    margin: 0 85.376px 0px 0;
    position: relative;
    .video {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
  .delete {
    width: 64.032px;
    height: 64.032px;
		cursor: pointer;
    background: url("~@/assets/img/delete.png") no-repeat;
    background-size: cover;
    background-position: center;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4Px;
    position: absolute;
    top: 0px;
    right: -66.7px;
    z-index: 99;
  }
  .video-failed {
    width: 266.8px;
    height: 266.8px;
    background: url("~@/assets/img/uploading.png") no-repeat;
    background-size: 100% 100%;
    text-align: center;
    margin: 0 85.376px 21.334px 0;
    padding-top: 66.7px;
    position: relative;
  }
  .video-uploading {
    width: 266.8px;
    height: 266.8px;
    background: url("~@/assets/img/uploading.png") no-repeat;
    background-size: 100% 100%;
    text-align: center;
    line-height: 266.8px;
  }
  .video-upload {
    width: 266.8px;
    height: 266.8px;
		box-sizing: border-box;
		border: 2Px solid #817042;
    background: rgba(103, 89, 58, 0.3) url("~@/assets/img/plus.png") no-repeat center;
    background-size: 133.4px;
  }
}
.pc-box {
  width: 266.8px;
  height: 266.8px;
	border: 2Px solid #817042;
	background: rgba(103, 89, 58, 0.3) url("~@/assets/img/plus.png") no-repeat center;
  background-size: 133.4px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  .pc-upload {
    cursor: pointer;
    width: 266.8px;
    height: 266.8px;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 6;
    opacity: 0;
    filter: alpha(opacity=0);
    & ::v-deep .van-uploader__input {
      width: 266.8px;
      height: 266.8px;
    }
  }
}
@media all and (orientation : portrait) {
	.video-wrap {
		.delete {
			width: 0.86rem;
			height: 0.86rem;
			right: -0.89rem;
		}
		.video-failed {
			width: 3.57rem;
			height: 3.57rem;
			margin: 0 1.14rem 0.286rem 0;
			padding-top: 0.893rem;
		}
		.video-uploading{
			width: 3.57rem;
			height: 3.57rem;
			line-height: 3.57rem;
		}
		.video-upload{
			width: 3.57rem;
			height: 3.57rem;
			background-size: 1.786rem;
		}
	}
}
</style>
