/*
 * @Author: we<PERSON><PERSON>.wang 
 * @Date: 2022-05-09 11:19:59 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-11-27 19:04:46
 */

import { App as Application } from 'vue'
import AutoFontSize from './AutoFontSize'
import ImgUpload from './ImgUpload'
import FpVideo from './FpVideo'
import VideoUpload from './VideoUpload'
import DialogWrapper from './DialogWrapper'
import PcWrapper from './PcWrapper'

import {
  Form,
  Field,
  Button,
  Picker,
  DatetimePicker,
  Popup,
  Icon,
  Loading,
  Uploader,
  Collapse,
  CollapseItem,
  Overlay,
  Swipe,
  SwipeItem,
  Lazyload,
  RadioGroup,
  Radio,
  Rate,
  NavBar
} from 'vant'

const comps = [
  Rate,
  Overlay,
  Form,
  Field,
  Button,
  Picker,
  DatetimePicker,
  Popup,
  Icon,
	Loading,
  Uploader,
  Collapse,
  CollapseItem,
	FpVideo,
  DialogWrapper,
  AutoFontSize,
  ImgUpload,
  VideoUpload,
  Pc<PERSON>rapper,
  Swipe,
  SwipeItem,
  Lazyload,
  RadioGroup,
  Radio,
  NavBar
]

export default {
  install(app: Application): void {
    comps.forEach(item => {
      app.use(item)
    })
  }
}
