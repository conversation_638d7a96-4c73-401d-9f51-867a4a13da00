/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2022-05-09 12:02:10
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-12-09 15:58:13
 */
import FpAxios from './interceptor'
import store from '@/store'
import { Method, AxiosPromise } from 'axios'
import { crypto } from '../utils/crypto'
import { getVisitorId } from '../utils'

/**
 *  Uniform interface request
 * @param url
 * @param method
 * @param options Header options
 * @param params Request information
 * @returns Promise
 */
const WXTimestamp = new Date().getTime()
const isWorldX = location.hostname === 'fpcs-web.kingsglorygames.com' || location.hostname === 'localhost'
const visitorIdFunc = getVisitorId();
const visitorId = visitorIdFunc();

class FpRequest extends FpAxios {
  private static http (url: string, method: Method = 'get', options: Record<string, unknown> = {}, params: Record<string, unknown> = {}) {
    const _S = 'Uc9Ud64Uf6Uc517354797809Uc27Ue6Ud69Uc70Ub053UaUd3Uc03608UdUa522Ub7160174'
    const apiKey = 'UcUd0893Uf053UaUd3Uc03608UdUa522Ub71601744UdUa9UfUf4855226U'
    // global params
    const { userInfo } = store.state
    let json_data = null
    try {
      json_data = userInfo.json_data && decodeURIComponent(userInfo.json_data) && JSON.parse(decodeURIComponent(userInfo.json_data))
    } catch (error) {
      console.log('error', error)
      json_data = {}
    }
    // console.log('visitorId', visitorId)
    // console.log('isWorldX', isWorldX)
		params = {
			json_data: JSON.stringify(json_data),
			scene: userInfo.scene ? Number(userInfo.scene) : 0, // scene含义：0：定制版游戏内，3：通用版游戏内，1：加载，2：封号；定制版可能不带scene字段 传0，通用版必带scene字段
			uuid: userInfo.device_id || (isWorldX ? visitorId : ''),
			device_type: userInfo.device_type ?? decodeURIComponent(userInfo.device_type),
			os_version: userInfo.os_version,
			rom_gb: userInfo.rom_gb,
			remain_rom: userInfo.remain_rom,
			app_version: userInfo.app_version,
			ram_mb: userInfo.ram_mb,
			network_info: userInfo.network_info,
			subchannel: userInfo.subchannel,
			pkgChannel: userInfo.pkgChannel,
			role_id: userInfo.role_id ? decodeURIComponent(userInfo.role_id) : '',
			nickname: userInfo.role_name ? decodeURIComponent(userInfo.role_name) : userInfo.name ? decodeURIComponent(userInfo.name) : '', //改 2022 11 24
      total_pay: userInfo.pay_amount ? Number(userInfo.pay_amount) : json_data && json_data.total_pay ? Number(json_data.total_pay) : 0, // 改 2022 11 24

			ts: userInfo.openid ? WXTimestamp : userInfo.ts ? Number(userInfo.ts) : 0,
			game_id: userInfo.gameid ? Number(userInfo.gameid) : 0,
			sid: userInfo.sid,
			fpid: userInfo.fpid ? Number(userInfo.fpid) : 0,
			uid: userInfo.uid ? Number(userInfo.uid) : 0,
			os: userInfo.os,
			channel: userInfo.channel,
			lang: userInfo.lang,
			sdk_version: userInfo.sdk_version,
			game_token: userInfo.game_token,
			track_key: userInfo.openid ? '' + userInfo.uid + WXTimestamp : userInfo.track_key,
			country_code: userInfo.country_code ? userInfo.country_code : '',
			// fpx兼容字段
			fpx_app_id: userInfo.fpx_app_id ? userInfo.fpx_app_id : '',
			account_id: userInfo.account_id ? decodeURIComponent(userInfo.account_id) : '',
			fp_uid: userInfo.fp_uid ? userInfo.fp_uid : '',
      packageId: userInfo.packageId, //fpx新增渠道号 2025 2 17
			// 微信下游戏兼容字段
			openid: userInfo.openid ? userInfo.openid : '',
      // 私域打开客服字段
      zone_token: userInfo.zone_token,
      zone_from: userInfo.zone_from,
      log_source: userInfo.log_source,
      funplus_id: userInfo.funplus_id,
      properties: userInfo.properties,
      // 调研问卷字段
      survey_token: userInfo.survey_token,
			...params
    }
    // encrypt
    const sign = crypto(apiKey, JSON.stringify(params), _S)
    // header options
    const headers = {
      sign,
      'api-key': apiKey,
      lang: userInfo.lang,
      ...options
    }
		let reqData = {}
		if (method === 'get') reqData = { params }
		if (method === 'post') reqData = { data: params }
		return this.server({
      url,
      method: method,
			...reqData,
      headers
    })
  }
  public static get(url: string, params: Record<string, unknown> = {}, options: Record<string, unknown> = {}): AxiosPromise {
		return this.http(url, 'get', options, params)
  }

  public static post(url: string, params: Record<string, unknown> = {}, options: Record<string, unknown> = {}): AxiosPromise {
		return this.http(url, 'post', options, params)
  }
}

export default FpRequest
