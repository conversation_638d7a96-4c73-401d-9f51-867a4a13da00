/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2022-05-09 11:34:45
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2022-05-14 17:34:56
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosStatic, AxiosError, AxiosResponse } from 'axios'
import { ERROR_CODES, reportRequestError } from '../utils/feishuLog'

// 防止重复上报的错误记录集合
const reportedNetworkErrors = new Set<string>();
const MAX_ERRORS = 50; // 最多记录50个错误

// 需要排除的URL集合
const EXCLUDED_URLS = [
  'backend/v1/report/log', // 日志上报接口
  'acs-go-test.funplus.com' // 飞书告警接口
];

// 检查URL是否需要排除
function shouldExcludeUrl(url?: string): boolean {
  if (!url) return false;

  return EXCLUDED_URLS.some(excludedUrl => url.includes(excludedUrl));
}

// 简单防抖函数，限制短时间内重复调用
function debounceNetworkReport(fn: Function, delay = 5000) {
  const cooldowns = new Map<string, number>();

  return function(data: any) {
    const url = data?.url || '';
    const now = Date.now();
    const key = `${url}`;
    const lastTime = cooldowns.get(key) || 0;

    if (now - lastTime < delay) {
      return; // 冷却时间内不执行
    }

    cooldowns.set(key, now);
    if (cooldowns.size > 100) { // 防止内存泄漏
      const oldestKey = cooldowns.keys().next().value;
      if (oldestKey !== undefined) {
        cooldowns.delete(oldestKey);
      }
    }

    fn(data);
  };
}

// 对UBT上报进行防抖处理
export const debouncedEventReport = debounceNetworkReport((data: any) => {
  window.ubt?.eventReport("NETWORK", data);
}, 5000);

export default class FpAxios {
  public static server: AxiosInstance

  public static init (): AxiosStatic {
    this.server = axios.create({
      timeout: 60000
    })
    this.serverInterceptors()
    return axios
  }

  // interceptors
  public static serverInterceptors (): void {
    this.server.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded'
    // request
    this.server.interceptors.request.use(
      (config: AxiosRequestConfig) => {
        return config
      },
      (error: AxiosError) => {
        // 请求错误处理
        // 避免被排除的接口错误反复上报
        if (shouldExcludeUrl(error.config?.url)) {
          console.log('排除URL的请求错误，不上报', error.message);
          return Promise.reject(error);
        }

        const errorKey = `${error.config?.method || ''}-${error.config?.url || ''}-${error.message || ''}`;
        if (!reportedNetworkErrors.has(errorKey)) {
          if (reportedNetworkErrors.size >= MAX_ERRORS) {
            reportedNetworkErrors.clear(); // 防止内存泄漏
          }
          reportedNetworkErrors.add(errorKey);

          reportRequestError({
            errorCode: ERROR_CODES.API_ERROR,
            errorType: '请求配置错误',
            errorMessage: error.message || '发送请求前发生错误',
            requestConfig: error.config,
            errorObject: error
          });
        }

        console.log(error) // for debug
        return Promise.reject(error)
      }
    )

    // response
    this.server.interceptors.response.use(
      (response: AxiosResponse) => {
        const headers = response.headers || {};
        // 排除特定接口
        if (!shouldExcludeUrl(response.config.url)) {
          debouncedEventReport({
            traceId: headers["trace-id"],
            res: response?.data,
            req: response?.config?.data,
            headers: response?.config?.headers,
            url: response?.config?.url
          });
        }
        if (response.status !== 200) {
          // 排除特定接口
          if (shouldExcludeUrl(response.config.url)) {
            console.log('排除URL的非200状态码:', response.status);
            return Promise.reject(response.status);
          }

          // 上报非200状态码错误
          const errorKey = `status-${response.config.url || ''}-${response.status}`;
          if (!reportedNetworkErrors.has(errorKey)) {
            if (reportedNetworkErrors.size >= MAX_ERRORS) {
              reportedNetworkErrors.clear();
            }
            reportedNetworkErrors.add(errorKey);

            reportRequestError({
              errorCode: ERROR_CODES.API_ERROR,
              errorType: 'HTTP状态码错误',
              errorMessage: `状态码：${response.status}`,
              requestConfig: response.config,
              responseObject: response
            });
          }
          return Promise.reject(response.status)
        }

        if (response.config.url?.includes('egress/conversation')) {
          // 检查egress/conversation接口是否返回错误
          if (response.data && typeof response.data === 'object') {
            // 假设该接口也有某种错误指示，如status或success字段
            if (response.data.code !== 0 || response.data.error || response.data.status === 'error' || response.data.success === false) {
              const errorMsg = response.data.message || response.data.msg || '对话接口错误';
              const errorKey = `conversation-${response.config.url || ''}-${response.data.code || ''}`;

              if (!reportedNetworkErrors.has(errorKey)) {
                if (reportedNetworkErrors.size >= MAX_ERRORS) {
                  reportedNetworkErrors.clear();
                }
                reportedNetworkErrors.add(errorKey);

                reportRequestError({
                  errorCode: ERROR_CODES.API_ERROR,
                  errorType: '对话接口错误',
                  errorMessage: errorMsg,
                  requestConfig: response.config,
                  errorObject: {
                    responseError: response.data.error || response.data.status,
                    businessCode: response.data.code
                  },
                  responseObject: response
                });
              }
            }
          }
          return response.data
        }

        if (response.data.code !== 0 && response.data.code !== 10) {
          // 上报业务逻辑错误
          const errorMsg = response.data.msg || '业务逻辑错误';

          // 接口路径为/v1/egress/msg/notice，且code为1102时，不进行上报
          let whiteMark = false

          if (response.config.url?.includes('/v1/egress/msg/notice') && response.data.code === 1102) {
            whiteMark = true
          }

          if (!whiteMark) {
            // 排除特定接口错误
            if (shouldExcludeUrl(response.config.url)) {
              // console.log('排除URL的业务错误:', response.data.code, errorMsg);
              return Promise.reject(errorMsg);
            }

            const errorKey = `business-${response.config.url || ''}-${response.data.code || ''}`;
            if (!reportedNetworkErrors.has(errorKey)) {
              if (reportedNetworkErrors.size >= MAX_ERRORS) {
                reportedNetworkErrors.clear();
              }
              reportedNetworkErrors.add(errorKey);

              reportRequestError({
                errorCode: ERROR_CODES.API_ERROR,
                errorType: '业务逻辑错误',
                errorMessage: errorMsg,
                requestConfig: response.config,
                errorObject: { businessCode: response.data.code },
                responseObject: response
              });
            }
          }

          return Promise.reject(errorMsg)
        }

        // console.log('response.config.url', response.config.url)
        // eslint-disable-next-line
        if (['/backend/v2/question/self_check'].includes(response.config.url!)) return response.data
        return response.data.data
      },
      (error: AxiosError) => {
        // 排除特定接口的错误处理，避免无限循环
        if (shouldExcludeUrl(error.response?.config?.url)) {
          console.log('排除URL的响应错误，不上报', error.message);
          return Promise.reject(error);
        }

        const headers = error.response?.headers || {};
        // 使用防抖来避免短时间内大量相同错误上报
        debouncedEventReport({
          traceId: headers["trace-id"],
          res: error?.response?.data,
          req: error?.response?.config?.data,
          url: error?.response?.config?.url
        });

        // 错误去重处理
        const errorKey = `${error.code || ''}-${error.response?.config?.url || ''}-${error.message || ''}`;
        if (reportedNetworkErrors.has(errorKey)) {
          return Promise.reject(error);
        }

        if (reportedNetworkErrors.size >= MAX_ERRORS) {
          reportedNetworkErrors.clear();
        }
        reportedNetworkErrors.add(errorKey);

        // 超时错误特殊处理
        if (error.code === 'ECONNABORTED' && error.message && error.message.includes('timeout')) {
          reportRequestError({
            errorCode: ERROR_CODES.API_TIMEOUT,
            errorType: '请求超时',
            errorMessage: error.message,
            requestConfig: error.config,
            errorObject: error
          });
        } else {
          // 其他响应错误
          reportRequestError({
            errorCode: ERROR_CODES.API_ERROR,
            errorType: '请求失败',
            errorMessage: error.message || '未知网络错误',
            requestConfig: error.config,
            errorObject: error
          });
        }

        return Promise.reject(error)
      }
    )
  }
}

