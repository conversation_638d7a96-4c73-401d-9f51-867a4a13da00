# 智能客服系统文档中心

## 文档导航

### 1. 项目基础文档
- [项目概述](./project-overview.md) - 项目简介、技术架构和目录结构
- [初始化流程](./init.md) - 项目启动和初始化流程详解

### 2. 页面文档
- **智能客服页面**
  - [旧版客服首页](./pages/smart/Smart.md) - 旧版智能客服页面功能和实现
  - [新版客服首页](./pages/new/NewSmart.md) - 新版智能客服页面功能和实现

- **工单系统页面**
  - [移动端工单页面](./pages/tickets/Tickets.md) - 移动端工单创建和管理
  - [PC端工单页面](./pages/tickets/PcTickets.md) - PC端工单创建和管理

- **其他页面**
  - [首页](./pages/HomePage.md) - 系统首页功能说明
  - [其他页面](./pages/OthersPages.md) - 其他辅助页面说明

### 3. 组件文档
- [组件概览](./components/README.md) - 组件体系介绍
- [聊天消息组件](./components/ChatItem.md) - 聊天消息展示组件
- [底部输入组件](./components/BottomWrap.md) - 底部输入区域组件
- [卡片组件](./components/Card.md) - 自助查询卡片组件

### 4. 接口文档
- [API接口文档](./api-interfaces.md) - 后端接口说明

## 开发指南

### 环境准备
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve          # 开发环境
npm run serve:test     # 测试环境
```

### 构建部署
```bash
# 测试环境构建
npm run build:test

# 生产环境构建
npm run build
```

### 访问地址
- **测试环境**：
  - Global: fpcs-web-test.funplus.com
  - CN: fpcs-web-test.funplus.com.cn

- **生产环境**：
  - Global: fpcs-web.funplus.com, fpcs-web-tx.kingsgroup.cn
  - CN: fpcs-web.funplus.com.cn, fpcs-web.nenglianghe.cn

## 开发规范

1. **代码规范**
   - 使用TypeScript进行类型检查
   - 遵循ESLint规则
   - 组件使用Vue 3 Composition API

2. **文档规范**
   - 新增功能需同步更新文档
   - 文档使用Markdown格式
   - 页面文档需包含业务流程图、代码实现和API说明

3. **Git工作流**
   - 功能开发使用feature分支
   - 提交前进行代码自测
   - 提交信息需清晰描述变更内容

## 文档更新记录

| 日期 | 更新内容 | 更新人 |
|------|----------|--------|
| 2023-03-06 | 更新域名和部署信息 | 开发团队 |
| 2023-03-05 | 新增工单页面文档 | 开发团队 |
| 2023-03-05 | 新增智能客服页面文档 | 开发团队 |
| 2023-03-05 | 初始化文档 | 开发团队 |

## 注意事项

1. 开发前请仔细阅读相关文档
2. 遵循代码规范和组件开发规范
3. 及时更新文档，保持文档的时效性
4. 有问题及时反馈和更新
