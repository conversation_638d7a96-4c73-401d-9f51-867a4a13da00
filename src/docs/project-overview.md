# 智能客服系统项目概述

## 项目简介

智能客服系统是一个基于Vue 3的前端项目，为用户提供多层次的客服解决方案。系统集成了智能问答、工单处理和知识库管理等功能，支持移动端和PC端多种设备访问。通过模块化设计，系统能够根据用户需求和场景智能切换不同的服务模式，提高问题解决效率。

## 业务架构

### 解决方案层级

1. **智能问答层**
   - 基于AI的自动问答系统
   - 热门问题推荐
   - 用户反馈收集与分析
   - 多种回答类型支持（文本、卡片、富媒体）

2. **工单系统层**
   - 分类树智能引导
   - 动态表单生成
   - 工单生命周期管理
   - 多媒体附件支持

3. **灰度发布机制**
   - 基于用户属性的灰度策略
   - 新旧版本平滑切换
   - 灰度数据收集与分析

### 用户场景

1. **普通用户**
   - 通过智能问答快速解决常见问题
   - 当问题无法自动解决时，创建工单获取人工支持
   - 查看历史会话和工单记录

2. **VIP用户**
   - 享受优先级更高的服务
   - 专属VIP界面和功能
   - 更丰富的自助服务选项

## 技术架构

### 前端技术栈
- **核心框架**：Vue 3 + TypeScript
- **状态管理**：Vuex 4
- **路由管理**：Vue Router 4
- **UI组件库**：Vant 3（移动端）
- **HTTP请求**：Axios + 请求拦截器
- **国际化**：Vue I18n
- **构建工具**：Vue CLI 4
- **CSS预处理器**：SCSS/SASS

### 项目结构
```
src/
├── @types/             # TypeScript类型定义
├── api/                # API接口定义
├── assets/             # 静态资源
├── components/         # 公共组件
├── customConfig/       # 自定义配置
├── docs/               # 项目文档
├── enum/               # 枚举定义
├── layout/             # 布局组件
├── packages/           # 自定义功能包
├── plugins/            # 插件配置
├── router/             # 路由配置
├── server/             # 服务端相关
├── store/              # Vuex状态管理
├── styles/             # 全局样式
├── utils/              # 工具函数
├── views/              # 页面组件
│   ├── New/            # 新版客服系统页面
│   ├── PC/             # PC端页面
│   └── QN/             # 问卷调查页面
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 核心功能模块

### 1. 智能客服模块
- **智能问答**：基于AI的自动问答系统
- **热门问题**：根据用户行为推荐热门问题
- **反馈系统**：收集用户对回答的评价和反馈
- **自动跳转**：多次未解决问题自动引导至工单系统

### 2. 工单系统模块
- **分类树**：多级问题分类，引导用户精确描述问题
- **动态表单**：根据问题类型动态生成表单字段
- **附件上传**：支持图片和视频上传
- **工单跟踪**：工单状态跟踪和历史记录查看

### 3. 用户体验模块
- **多端适配**：支持移动端和PC端，自适应不同屏幕尺寸
- **国际化**：多语言支持
- **主题定制**：支持不同游戏主题的UI定制
- **灰度发布**：支持新功能的灰度发布和测试

## 部署环境

### 测试环境
- **Global**: fpcs-web-test.funplus.com
- **CN**: fpcs-web-test.funplus.com.cn

### 预发布环境
- **Global**: fpcs-web-stage.funplus.com
- **CN**: fpcs-web-stage.funplus.com.cn

### 生产环境
- **Global**:
  - fpcs-web.funplus.com
  - fpcs-web-tx.kingsgroup.cn
  - fpcs-web-tx.yoo-mei.cn
  - fpcs-web.lightning-piggy.com

- **CN**:
  - fpcs-web.funplus.com.cn
  - fpcs-web.nenglianghe.cn
  - fpcs-web.yoo-mei.cn
  - fpcs-web.zmgames.cn

## DevOps部署

- **Global**: kg.global.prod.general-cs-web
  - https://devops-tx.kingsgroup.cn/sprinkle/k8s_deployment/512/history

- **CN**: funplus.cn.prod.general-cs-web
  - https://devops-tx.kingsgroup.cn/sprinkle/k8s_deployment/511/history

## 相关文档

- [初始化流程](./init.md) - 项目启动和初始化流程详解
- [页面功能文档](./page-functions.md) - 详细的页面功能说明
- [API接口文档](./api-interfaces.md) - API接口清单和说明
- [组件文档](./components.md) - 公共组件使用说明
