# 智能客服系统页面功能说明文档

## 系统页面架构

智能客服系统采用模块化页面设计，根据用户需求和场景提供不同的功能入口。系统主要包含以下页面模块：

### 核心页面
1. **首页（HomePage.vue）** - 系统主入口，提供功能导航
2. **智能客服页面（Smart.vue）** - 旧版智能问答界面
3. **新版智能客服页面（NewSmart.vue）** - 新版智能问答界面
4. **工单页面（Tickets.vue）** - 移动端工单创建界面
5. **PC工单页面（PcTickets.vue）** - PC端工单创建界面

### 辅助页面
6. **工单详情页（TicketDetail.vue）** - 查看工单详情和进度
7. **历史记录页（History.vue）** - 查看历史工单和会话
8. **工单评价页（TicketAppraise.vue）** - 对已完成工单进行评价
9. **工单补充页（TicketComplete.vue）** - 向已提交工单添加信息

## 页面详细说明

### 首页（HomePage.vue）

#### 功能概述
首页是客服系统的统一入口，根据用户类型和灰度配置展示不同内容，提供智能客服和工单系统的访问入口。

#### 页面结构
1. **VIP用户模式**
   - 专属VIP标题和描述
   - 历史记录快捷入口
   - 卡片/列表模式内容展示
   - 专属客服入口

2. **普通用户模式**
   - 顶部导航栏（首页/AI智能客服）
   - 轮播图组件（HomeSwiper）
   - 功能卡片组件（HomeCards）
   - 热门问题列表（HomeHotQuestions）
   - 底部搜索栏

#### 关键交互
- 导航切换：点击"AI智能客服"跳转到智能客服页面
- 搜索功能：输入问题跳转到智能客服页面并自动提问
- 卡片点击：根据配置跳转到对应功能页面
- 灰度控制：根据用户ID和配置决定展示新版或旧版界面

#### 相关文档
- [首页详细文档](./pages/new/HomePage.md)

### 智能客服页面（Smart.vue）

#### 功能概述
旧版智能客服页面，提供基于AI的智能问答功能，支持用户与AI助手进行对话。

#### 页面结构
- 顶部导航栏（带返回按钮）
- 热门问题推荐区
- 聊天记录区域（ChatItem组件）
- 底部输入框和快捷操作区（BottomWrap组件）

#### 关键交互
- 问题输入：支持文本输入和热门问题选择
- 对话展示：显示用户问题和AI回答，支持打字机效果
- 评价功能：支持对AI回答进行点赞/点踩评价
- 自动工单：多次未解决问题自动引导至工单系统

#### 相关文档
- [智能客服页面详细文档](./pages/smart/Smart.md)

### 新版智能客服页面（NewSmart.vue）

#### 功能概述
新版智能客服页面，在旧版基础上优化了UI和交互，增加了更多功能。

#### 页面结构
- 顶部导航栏（带主题色）
- 智能推荐区（根据用户历史推荐问题）
- 聊天记录区域（增强版ChatItem）
- 底部输入区（支持语音输入）

#### 关键交互
- 增强问答：支持多轮对话和上下文理解
- 富媒体回答：支持图片、视频、链接等富媒体内容
- 智能推荐：基于用户历史和行为推荐相关问题
- 会话保存：自动保存会话历史，支持继续对话

#### 相关文档
- [新版智能客服页面详细文档](./pages/new/NewSmart.md)

### 工单页面（Tickets.vue）

#### 功能概述
移动端工单创建界面，引导用户选择问题类型并填写工单信息。

#### 页面结构
- 顶部导航栏
- 分类选择区（CategoryTree组件）
- 动态表单区（根据分类动态生成）
- 附件上传区（ImgUpload和VideoUpload组件）
- 提交按钮

#### 关键交互
- 分类选择：多级分类树，引导用户精确描述问题
- 动态表单：根据所选分类动态生成不同字段的表单
- 附件上传：支持图片和视频上传，带预览功能
- 表单验证：提交前进行字段验证，确保信息完整

#### 相关文档
- [工单页面详细文档](./pages/tickets/Tickets.md)

### PC工单页面（PcTickets.vue）

#### 功能概述
PC端工单创建界面，提供更丰富的表单功能和更好的大屏幕体验。

#### 页面结构
- 左侧分类树（固定展示）
- 右侧表单区（宽屏布局）
- 富文本编辑器（支持格式化和图片插入）
- 多文件上传区

#### 关键交互
- 分屏操作：左侧选择分类，右侧填写表单
- 富文本编辑：支持文本格式化、列表、图片插入等
- 批量上传：支持多文件同时上传
- 历史工单：可查看历史工单列表和状态

#### 相关文档
- [PC工单页面详细文档](./pages/tickets/PcTickets.md)

### 工单详情页（TicketDetail.vue）

#### 功能概述
查看工单详细信息和处理进度，支持与客服进行沟通。

#### 页面结构
- 工单基本信息区
- 工单状态展示
- 对话记录区
- 附件查看区
- 补充信息区

#### 关键交互
- 状态跟踪：实时显示工单处理状态和进度
- 消息通知：新回复提醒
- 信息补充：允许用户补充工单信息
- 评价功能：工单解决后进行服务评价

#### 相关文档
- [工单详情页详细文档](./pages/ticket/TicketDetail.md)

## 页面流程图

```mermaid
graph TD
    A[首页] --> B[智能客服]
    A --> C[新版智能客服]
    A --> D[工单页面]
    A --> E[PC工单页面]
    B --> F[工单页面]
    C --> F
    D --> G[工单详情]
    E --> G
    G --> H[工单评价]
    G --> I[工单补充]
    A --> J[历史记录]
    J --> G
```

## 业务流程说明

### 智能问答流程
1. 用户在首页或智能客服页面输入问题
2. 系统分析问题并生成回答
3. 用户查看回答并可进行评价
4. 如问题未解决，系统推荐相关问题或引导创建工单

### 工单处理流程
1. 用户选择问题分类并填写工单信息
2. 系统创建工单并分配给相应客服
3. 客服处理工单并回复
4. 用户查看回复，必要时补充信息
5. 问题解决后，用户进行评价

### 灰度发布流程
1. 系统根据用户ID和配置判断是否命中灰度
2. 命中灰度的用户进入新版界面
3. 未命中灰度的用户继续使用旧版界面
4. 收集用户反馈和数据，调整灰度策略

## 页面开发指南

### 页面命名规范
- 页面文件名使用 PascalCase（如 HomePage.vue）
- 页面组件名与文件名保持一致
- 页面路由使用 kebab-case（如 /home-page）

### 页面结构规范
- 每个页面应包含 template、script、style 三部分
- script 部分使用 TypeScript
- style 部分使用 SCSS，并添加 scoped 属性

### 页面性能优化
- 使用异步组件和懒加载
- 合理使用缓存机制
- 优化大列表渲染
- 减少不必要的网络请求
