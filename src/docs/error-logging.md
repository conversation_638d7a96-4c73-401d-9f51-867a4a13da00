# 客服系统错误上报文档

## 概述

客服系统集成了完善的错误上报机制，用于捕获和上报各类错误，包括JavaScript运行时错误、Promise未捕获异常以及API请求错误。上报的错误信息会通过飞书机器人进行通知，帮助开发团队快速定位和解决问题。

## 错误码定义

系统定义了以下错误码用于区分不同类型的错误：

```typescript
export const ERROR_CODES = {
  API_ERROR: '7001',       // 接口请求错误
  JS_ERROR: '7002',        // JavaScript运行时错误
  PROMISE_ERROR: '7003',   // Promise未捕获异常
  API_TIMEOUT: '7004',     // 接口请求超时
}
```

## 核心组件

错误上报逻辑主要涉及以下三个文件：

1. `src/utils/feishuLog.ts` - 核心错误处理和上报功能
2. `src/server/interceptor.ts` - API请求错误拦截和上报
3. `src/main.ts` - 初始化错误上报环境

## 错误捕获机制

### 1. JavaScript运行时错误

使用全局的`window.onerror`事件处理器捕获JavaScript运行时错误：

```typescript
window.onerror = function(errorMsg, url, lineNumber, column, errorObj) {
  // 错误处理逻辑
  sendErrorToFeishu(ERROR_CODES.JS_ERROR, fullErrorMsg);
  return false; // 允许错误冒泡
};
```

### 2. Promise未捕获异常

使用`unhandledrejection`事件监听器捕获Promise中未处理的异常：

```typescript
window.addEventListener('unhandledrejection', function(event) {
  const errorMsg = event.reason?.message || String(event.reason) || '未知Promise错误';
  sendErrorToFeishu(ERROR_CODES.PROMISE_ERROR, `Promise未捕获异常: ${errorMsg}`);
});
```

### 3. API请求错误

使用Axios拦截器捕获API请求过程中的各类错误：

- 请求配置错误
- HTTP状态码错误
- 业务逻辑错误
- 请求超时错误
- 其他网络错误

## 错误上报流程

1. **错误捕获**：通过不同机制捕获各类错误
2. **错误信息格式化**：提取错误相关信息并格式化
3. **生成CURL命令**：对于API错误，生成可复现的CURL命令
4. **错误上报**：将格式化后的错误信息发送到飞书机器人

## 错误信息格式

上报的错误信息包含以下部分：

```
错误类型: [错误码] 错误消息
**【CURL】**:
`curl命令`
```

例如：
```
业务逻辑错误: [业务 500] 服务器内部错误
**【CURL】**:
`curl 'http://api.example.com/endpoint' \
  -H 'Content-Type: application/json' \
  --data-raw '{"key":"value"}'`
```

## CURL命令生成

系统会根据API请求的配置自动生成CURL命令，方便开发人员快速复现问题：

```typescript
export function formatRequestAsCurl(config: any): string {
  // 构建基础URL
  // 添加查询参数
  // 添加请求头
  // 添加请求体
  return curlCommand;
}
```

生成的CURL命令格式示例：

```bash
curl 'http://api.example.com/endpoint?param=value' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer token' \
  --data-raw '{"key":"value"}'
```

## 初始化与配置

系统在启动时会保存初始页面URL用于错误上报，便于了解用户从哪个入口进入系统：

```typescript
// 保存初始页面URL，用于错误上报
const saveInitHomeUrl = () => {
  if (!localStorage.getItem('init_home_url')) {
    localStorage.setItem('init_home_url', window.location.href)
  }
}

// 在应用启动时立即保存初始URL
saveInitHomeUrl()
```

## 统一错误处理接口

系统提供了统一的错误上报接口，简化了错误处理逻辑：

```typescript
export interface RequestErrorReportOptions {
  errorCode: string;       // 错误码
  errorType: string;       // 错误类型
  errorMessage: string;    // 错误消息
  requestConfig?: any;     // 请求配置
  errorObject?: any;       // 错误对象
  responseObject?: any;    // 响应对象
}

export function reportRequestError(options: RequestErrorReportOptions): void {
  // 错误处理与上报逻辑
}
```

## 错误优先级处理

系统对错误码有明确的优先级处理逻辑：

1. 非默认错误码 - 优先使用明确传入的错误码
2. 业务错误码 - 其次使用响应中的业务错误码
3. HTTP状态码 - 最后才使用HTTP状态码

## 错误过滤

系统会过滤某些已知的无害错误，避免不必要的上报：

```typescript
if (errorMsg.includes(`SyntaxError: Document.querySelectorAll: \'div:has(> iframe[id=`)) {
  return true; // 忽略特定类型的错误
}

if (errorMsg.includes('Script error')) {
  return false; // 不处理跨域脚本错误
}
```

## 注意事项

1. 确保敏感信息不会被上报到飞书机器人
2. 对于特定API错误，考虑添加更详细的错误类型
3. 错误信息中的CURL命令可能包含敏感数据，注意安全问题

## 自定义错误上报

可以使用`reportApiError`函数手动上报特定错误：

```typescript
import { ERROR_CODES, reportApiError } from '@/utils/feishuLog';

// 上报自定义错误
reportApiError(ERROR_CODES.API_ERROR, '自定义错误消息');
```

## 常见问题

1. **错误没有被捕获**：检查错误是否在异步上下文中产生，确保Promise错误有被正确处理
2. **CURL命令无法复现**：检查请求头和认证信息是否完整
3. **错误信息不完整**：检查错误对象是否包含所有必要信息
