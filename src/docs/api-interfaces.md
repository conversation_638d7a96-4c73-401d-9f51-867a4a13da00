# 智能客服系统API接口文档

## 接口概览

智能客服系统提供了一系列API接口，用于支持前端功能实现。这些接口按功能模块分为以下几类：

1. **智能客服接口** - 提供AI问答、热门问题、自助查询等功能
2. **工单系统接口** - 提供工单创建、查询、操作等功能
3. **用户管理接口** - 提供用户信息、权限验证等功能
4. **文件上传接口** - 提供图片、视频等文件上传功能
5. **系统配置接口** - 提供系统配置、灰度控制等功能

## 接口基础信息

### 请求基础信息
- **基础URL**：
  - 测试环境：`https://fpcs-web-test.funplus.com/api`
  - 生产环境：`https://fpcs-web.funplus.com/api`
- **请求方法**：主要使用POST方法
- **请求格式**：application/json
- **认证方式**：Token认证（header中携带Authorization）

### 响应格式
```typescript
interface ApiResponse<T> {
  code: number;      // 状态码，0表示成功
  message: string;   // 提示信息
  data: T;           // 响应数据，泛型类型
}
```

## 智能客服接口

### 对话接口

#### 获取AI回答
```typescript
// 获取AI回答
getAnswer: ApiT<AnswerResponse> = params =>
  FpRequest.post('/backend/v2/question/answer', params)

// 请求参数
interface AnswerRequest {
  question: string;           // 用户问题
  context?: string;           // 上下文信息（可选）
  sessionId?: string;         // 会话ID（可选）
  userId: string;             // 用户ID
  language?: string;          // 语言（可选，默认en）
}

// 响应数据
interface AnswerResponse {
  answer: string;             // AI回答内容
  messageId: string;          // 消息ID
  recommendQuestions?: Array<{// 推荐问题列表
    question: string;         // 问题内容
    id: string;               // 问题ID
  }>;
  cardInfo?: any;             // 卡片信息（如有）
}
```

#### 获取热门问题
```typescript
// 获取热门问题
getHotQuestions: ApiT<HotQuestionsResponse> = params =>
  FpRequest.post('/backend/v1/question/hot_questions', params)

// 请求参数
interface HotQuestionsRequest {
  userId: string;             // 用户ID
  language?: string;          // 语言（可选，默认en）
  limit?: number;             // 返回数量（可选，默认5）
}

// 响应数据
interface HotQuestionsResponse {
  questions: Array<{
    question: string;         // 问题内容
    id: string;               // 问题ID
    count: number;            // 问题热度
  }>;
}
```

#### 问题评价
```typescript
// 问题评价
sendFeedback: ApiT<void> = params =>
  FpRequest.post('/backend/v1/question/score', params)

// 请求参数
interface FeedbackRequest {
  messageId: string;          // 消息ID
  score: 1 | 0;               // 评分（1:满意，0:不满意）
  userId: string;             // 用户ID
  reason?: string;            // 不满意原因（score为0时必填）
}
```

### 自助查询接口

#### 获取自助查询结果
```typescript
// 自助查询
getSelfQuery: ApiT<SelfQueryResponse> = params =>
  FpRequest.post('/backend/v2/question/self_check', params)

// 请求参数
interface SelfQueryRequest {
  query: string;              // 查询内容
  userId: string;             // 用户ID
  type?: string;              // 查询类型（可选）
}

// 响应数据
interface SelfQueryResponse {
  cardType: string;           // 卡片类型
  cardData: any;              // 卡片数据
}
```

## 工单系统接口

### 工单分类接口

#### 获取工单分类树
```typescript
// 获取工单分类树
getCategoryTree: ApiT<CategoryTreeResponse> = params =>
  FpRequest.post('/backend/v1/egress/scene/cats', params)

// 请求参数
interface CategoryTreeRequest {
  userId: string;             // 用户ID
  language?: string;          // 语言（可选，默认en）
}

// 响应数据
interface CategoryTreeResponse {
  categories: Array<{
    id: string;               // 分类ID
    name: string;             // 分类名称
    children?: Array<any>;    // 子分类
    templateId?: string;      // 模板ID
  }>;
}
```

#### 获取工单表单模板
```typescript
// 获取工单表单模板
getTicketTemplate: ApiT<TicketTemplateResponse> = params =>
  FpRequest.post('/backend/v1/egress/tpl/info', params)

// 请求参数
interface TicketTemplateRequest {
  templateId: string;         // 模板ID
  userId: string;             // 用户ID
  language?: string;          // 语言（可选，默认en）
}

// 响应数据
interface TicketTemplateResponse {
  fields: Array<{
    id: string;               // 字段ID
    name: string;             // 字段名称
    type: string;             // 字段类型
    required: boolean;        // 是否必填
    options?: Array<any>;     // 选项（如有）
  }>;
}
```

### 工单操作接口

#### 创建工单
```typescript
// 创建工单
createTicket: ApiT<CreateTicketResponse> = params =>
  FpRequest.post('/backend/v1/egress/ticket/create', params)

// 请求参数
interface CreateTicketRequest {
  categoryId: string;         // 分类ID
  userId: string;             // 用户ID
  fields: Record<string, any>;// 表单字段
  attachments?: Array<{       // 附件（可选）
    url: string;              // 附件URL
    type: string;             // 附件类型
    name?: string;            // 附件名称
  }>;
}

// 响应数据
interface CreateTicketResponse {
  ticketId: string;           // 工单ID
  ticketNo: string;           // 工单编号
}
```

#### 查询工单详情
```typescript
// 查询工单详情
getTicketDetail: ApiT<TicketDetailResponse> = params =>
  FpRequest.post('/backend/v1/egress/ticket/detail', params)

// 请求参数
interface TicketDetailRequest {
  ticketId: string;           // 工单ID
  userId: string;             // 用户ID
}

// 响应数据
interface TicketDetailResponse {
  ticketInfo: {
    id: string;               // 工单ID
    no: string;               // 工单编号
    status: string;           // 工单状态
    category: string;         // 工单分类
    createTime: string;       // 创建时间
    fields: Record<string, any>;// 表单字段
    attachments?: Array<any>; // 附件
  };
  communications: Array<{     // 沟通记录
    id: string;               // 记录ID
    content: string;          // 内容
    sender: string;           // 发送者
    time: string;             // 时间
    attachments?: Array<any>; // 附件
  }>;
}
```

#### 工单补充
```typescript
// 工单补充
completeTicket: ApiT<void> = params =>
  FpRequest.post('/backend/v1/egress/ticket/replenish', params)

// 请求参数
interface CompleteTicketRequest {
  ticketId: string;           // 工单ID
  userId: string;             // 用户ID
  content: string;            // 补充内容
  attachments?: Array<{       // 附件（可选）
    url: string;              // 附件URL
    type: string;             // 附件类型
    name?: string;            // 附件名称
  }>;
}
```

#### 工单评价
```typescript
// 工单评价
appraiseTicket: ApiT<void> = params =>
  FpRequest.post('/backend/v1/egress/ticket/appraise', params)

// 请求参数
interface AppraiseTicketRequest {
  ticketId: string;           // 工单ID
  userId: string;             // 用户ID
  score: number;              // 评分（1-5）
  comment?: string;           // 评价内容（可选）
}
```

## 文件上传接口

### 图片上传
```typescript
// 图片上传
uploadImage: ApiT<UploadResponse> = (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  return FpRequest.post('/backend/v1/upload/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: onProgress
  });
}

// 响应数据
interface UploadResponse {
  url: string;                // 文件URL
  name: string;               // 文件名
  size: number;               // 文件大小
  type: string;               // 文件类型
}
```

### 视频上传
```typescript
// 视频上传
uploadVideo: ApiT<UploadResponse> = (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);
  return FpRequest.post('/backend/v1/upload/video', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: onProgress
  });
}
```

## 系统配置接口

### 获取系统配置
```typescript
// 获取系统配置
getSystemConfig: ApiT<SystemConfigResponse> = params =>
  FpRequest.post('/backend/v1/system/config', params)

// 请求参数
interface SystemConfigRequest {
  userId: string;             // 用户ID
}

// 响应数据
interface SystemConfigResponse {
  isGray: boolean;            // 是否命中灰度
  isVip: boolean;             // 是否VIP用户
  theme: string;              // 主题配置
  features: Array<string>;    // 功能开关
}
```

### 获取灰度配置
```typescript
// 获取灰度配置
getGrayConfig: ApiT<GrayConfigResponse> = params =>
  FpRequest.post('/backend/v1/system/gray', params)

// 请求参数
interface GrayConfigRequest {
  userId: string;             // 用户ID
  feature: string;            // 功能标识
}

// 响应数据
interface GrayConfigResponse {
  isGray: boolean;            // 是否命中灰度
  config: Record<string, any>;// 灰度配置
}
```

## 接口调用示例

### 智能客服对话示例
```typescript
// 调用AI回答接口
async function askQuestion(question: string) {
  try {
    const response = await getAnswer({
      question,
      userId: getUserId(),
      sessionId: getSessionId()
    });

    if (response.code === 0) {
      // 处理AI回答
      displayAnswer(response.data.answer);

      // 处理推荐问题
      if (response.data.recommendQuestions?.length) {
        displayRecommendQuestions(response.data.recommendQuestions);
      }

      // 处理卡片信息
      if (response.data.cardInfo) {
        displayCard(response.data.cardInfo);
      }
    } else {
      // 处理错误
      handleError(response.message);
    }
  } catch (error) {
    // 处理异常
    handleException(error);
  }
}
```

### 工单创建示例
```typescript
// 创建工单
async function submitTicket(categoryId: string, fields: Record<string, any>, attachments: Array<any>) {
  try {
    const response = await createTicket({
      categoryId,
      userId: getUserId(),
      fields,
      attachments
    });

    if (response.code === 0) {
      // 工单创建成功
      showSuccess(`工单创建成功，工单编号：${response.data.ticketNo}`);
      // 跳转到工单详情页
      navigateToTicketDetail(response.data.ticketId);
    } else {
      // 处理错误
      handleError(response.message);
    }
  } catch (error) {
    // 处理异常
    handleException(error);
  }
}
```

## 错误处理

### 错误码说明
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 0 | 成功 | 正常处理响应数据 |
| 401 | 未授权 | 跳转登录页面 |
| 403 | 权限不足 | 提示用户权限不足 |
| 404 | 资源不存在 | 提示资源不存在 |
| 500 | 服务器错误 | 提示系统异常，请稍后重试 |

### 全局错误处理
```typescript
// 全局错误处理
function handleApiError(error: any) {
  if (error.response) {
    // 服务器返回错误
    const { status, data } = error.response;

    switch (status) {
      case 401:
        // 未授权，跳转登录
        redirectToLogin();
        break;
      case 403:
        // 权限不足
        showToast('权限不足，无法执行此操作');
        break;
      case 404:
        // 资源不存在
        showToast('请求的资源不存在');
        break;
      case 500:
        // 服务器错误
        showToast('系统异常，请稍后重试');
        break;
      default:
        // 其他错误
        showToast(data.message || '请求失败');
    }
  } else if (error.request) {
    // 请求发出但未收到响应
    showToast('网络异常，请检查网络连接');
  } else {
    // 请求配置错误
    showToast('请求配置错误');
  }
}
```
