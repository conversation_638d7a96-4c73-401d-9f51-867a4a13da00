# Card 组件分析

## 组件概述

Card 组件是智能客服系统中用于展示自助查询结果的组件。它支持两种主要展示形式：卡片形式和列表形式，以及特殊的三级数据表格展示。该组件主要用于结构化数据的可视化展示，提高用户体验。

## 组件结构

```vue
<template>
  <!-- 一级二级卡片 -->
  <div class="card_box">
    <!-- 卡片提示语 -->
    <div v-if="props.msgItem.card_prompt_title" class="card_prompt">{{ props.msgItem.card_prompt_title }}</div>

    <!-- 卡片形式展示 (show_type === 1) -->
    <div v-if="props.msgItem.show_type === 1" class="cards_wraper">
      <div :class="['card_item', animateMark === index ? 'rubberBand' : '']"
        v-for="(item, index) in props.msgItem.children" :key="index"
        @click.stop.prevent="cardClick(item, index)">
        <div style="position:relative;height:100%;width:100%">
          <div class="card_img" :style="{ 'background-image': `url(${item.card_image})` }"></div>
          <div class="card_text">
            <auto-font-size :text="item.card_title"></auto-font-size>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表形式展示 -->
    <div class="card_list_wraper" v-else>
      <p class="list_item" v-for="(item, index) in props.msgItem.children" :key="index"
        @click="cardClick(item)">
        {{ index + 1 }}.
        {{ item.card_title }}
      </p>
    </div>
  </div>

  <!-- 三级数据表格展示 -->
  <div v-if="props.msgItem.code === 0" class="content-box">
    <table class="content" cellspacing="0" v-if="props.msgItem.data.detail?.length">
      <tr class="row head">
        <th class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ item.desc }}</th>
      </tr>
      <tr class="row" v-for="(v, k) in props.msgItem.data.detail" :key="k">
        <td class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ v[item.key.substring(20, item.key.length-4)] }}</td>
      </tr>
    </table>
    <span v-else class="title">{{ $t('text_no_last_day_record', { num: filterDay }) }}</span>
  </div>
</template>
```

## 组件属性

```typescript
const props = defineProps<{
  msgItem: any  // 卡片数据对象
}>()
```

`msgItem` 对象结构：
- `card_prompt_title`: 卡片提示标题
- `show_type`: 展示类型（1: 卡片形式，其他: 列表形式）
- `children`: 子项数组
  - `card_image`: 卡片图片URL
  - `card_title`: 卡片标题
- `code`: 状态码（用于判断三级数据）
- `data`: 三级数据
  - `meta`: 表头元数据
  - `detail`: 表格数据

## 核心功能

### 1. 多种展示形式

Card 组件支持三种主要展示形式：

1. **卡片形式**：当 `show_type === 1` 时，以图文卡片形式展示数据
   ```vue
   <div v-if="props.msgItem.show_type === 1" class="cards_wraper">
     <div :class="['card_item', animateMark === index ? 'rubberBand' : '']"
       v-for="(item, index) in props.msgItem.children" :key="index"
       @click.stop.prevent="cardClick(item, index)">
       <div class="card_img" :style="{ 'background-image': `url(${item.card_image})` }"></div>
       <div class="card_text">
         <auto-font-size :text="item.card_title"></auto-font-size>
       </div>
     </div>
   </div>
   ```

2. **列表形式**：当 `show_type !== 1` 时，以编号列表形式展示数据
   ```vue
   <div class="card_list_wraper" v-else>
     <p class="list_item" v-for="(item, index) in props.msgItem.children" :key="index"
       @click="cardClick(item)">
       {{ index + 1 }}.
       {{ item.card_title }}
     </p>
   </div>
   ```

3. **表格形式**：用于展示三级数据查询结果
   ```vue
   <div v-if="props.msgItem.code === 0" class="content-box">
     <table class="content" cellspacing="0" v-if="props.msgItem.data.detail?.length">
       <tr class="row head">
         <th class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ item.desc }}</th>
       </tr>
       <tr class="row" v-for="(v, k) in props.msgItem.data.detail" :key="k">
         <td class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ v[item.key.substring(20, item.key.length-4)] }}</td>
       </tr>
     </table>
     <span v-else class="title">{{ $t('text_no_last_day_record', { num: filterDay }) }}</span>
   </div>
   ```

### 2. 交互动画

组件为卡片点击添加了动画效果，提升用户体验：

```typescript
const data: dataT = reactive({
  animateMark: -1
})

const cardClick = (item: Record<string, unknown>, index?: number) => {
  if (data.animateMark !== -1) return false
  if (index !== undefined) {
    data.animateMark = index
  }
  emit('item-click', item)
  setTimeout(() => {
    data.animateMark = -1
  }, 600)
}
```

当用户点击卡片时，会触发 `rubberBand` 动画效果，并在600ms后恢复正常状态。

### 3. 事件通信

组件通过 `item-click` 事件与父组件通信：

```typescript
const emit = defineEmits<{
  (event: 'item-click', itemInfo: Record<string, unknown>): void
}>()
```

当用户点击卡片或列表项时，会触发 `item-click` 事件，并传递被点击项的数据。

## 数据处理

### 1. 三级数据处理

组件对三级数据表格的字段进行了特殊处理：

```vue
<td class="item" v-for="item in props.msgItem.data.meta" :key="item.key">
  {{ v[item.key.substring(20, item.key.length-4)] }}
</td>
```

这里使用了字符串截取操作 `item.key.substring(20, item.key.length-4)`，这是为了处理后端返回的特殊字段格式。

### 2. 查询天数处理

组件从 sessionStorage 中获取查询天数，用于显示无数据提示：

```typescript
const filterDay = sessionStorage.getItem('filterDay')
```

```vue
<span v-else class="title">{{ $t('text_no_last_day_record', { num: filterDay }) }}</span>
```

## 交互流程

1. **一级/二级卡片交互**：
   - 用户查询某个主题
   - 系统返回相关卡片或列表
   - 用户点击卡片/列表项
   - 组件触发 `item-click` 事件
   - 父组件接收事件并处理下一级查询

2. **三级数据展示**：
   - 用户点击二级卡片/列表项
   - 系统查询详细数据
   - 组件以表格形式展示查询结果
   - 如果没有数据，显示无数据提示

## 样式特点

1. **卡片样式**：
   - 图片+文字组合
   - 点击时有动画效果
   - 自适应文字大小

2. **列表样式**：
   - 编号+文字组合
   - 简洁清晰的展示方式

3. **表格样式**：
   - 表头与内容区分明显
   - 整齐的行列布局
   - 适合展示结构化数据

## 注意事项

1. 组件依赖于特定的数据结构，使用前需确保数据格式正确
2. 三级数据表格的字段处理使用了特定的截取逻辑，如果后端数据结构变化需要相应调整
3. 卡片点击有防抖处理，避免用户快速多次点击
4. 组件使用了 `auto-font-size` 组件自适应文字大小，确保在不同设备上显示效果一致
5. 无数据时的提示信息依赖于 i18n 国际化配置
