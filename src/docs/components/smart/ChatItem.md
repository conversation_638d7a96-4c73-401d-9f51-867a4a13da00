# ChatItem 聊天消息组件

## 组件说明
ChatItem 组件是智能客服系统中用于展示聊天消息的核心组件。它支持多种消息类型的展示，包括文本消息、图片消息、推荐问题等。

## 组件位置
```typescript
import ChatItem from '@/components/SmartComp/ChatItem.vue'
```

## 组件属性（Props）

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| chatItem | Object | 是 | - | 聊天消息对象 |
| finish | Boolean | 否 | false | 消息是否已完成 |
| isWelcome | Boolean | 否 | false | 是否为欢迎消息 |
| avatar | String | 否 | '' | 头像URL |
| isTyping | Boolean | 否 | false | 是否正在输入 |

### chatItem 对象结构
```typescript
interface ChatItem {
  type: 'text' | 'image' | 'recommend' | 'article';
  content: string;
  timestamp: number;
  sender: 'user' | 'ai';
  messageId: string;
  recommendations?: Array<{
    id: string;
    title: string;
    type: string;
  }>;
}
```

## 组件事件（Events）

| 事件名称 | 参数 | 说明 |
|----------|------|------|
| recommClick | item: Object | 推荐问题被点击 |
| dislikeOptList | messageId: string | 请求不满意原因列表 |
| problemUnres | messageId: string | 问题未解决反馈 |
| imagePreview | url: string | 图片预览 |
| retry | messageId: string | 重试发送消息 |

## 插槽说明（Slots）

| 插槽名称 | 说明 |
|----------|------|
| avatar | 自定义头像内容 |
| content | 自定义消息内容 |
| actions | 自定义操作按钮 |

## 使用示例

### 基础用法
```vue
<template>
  <ChatItem
    :chat-item="messageData"
    :finish="true"
    :avatar="userAvatar"
    @recommClick="handleRecommClick"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const messageData = ref({
  type: 'text',
  content: '您好，请问有什么可以帮您？',
  timestamp: Date.now(),
  sender: 'ai',
  messageId: '1'
})

const userAvatar = ref('https://example.com/avatar.png')

const handleRecommClick = (item) => {
  console.log('推荐问题点击：', item)
}
</script>
```

### 带推荐问题的消息
```vue
<template>
  <ChatItem
    :chat-item="messageWithRecomm"
    :finish="true"
    @recommClick="handleRecommClick"
  />
</template>

<script setup lang="ts">
const messageWithRecomm = {
  type: 'text',
  content: '以下是相关的问题：',
  timestamp: Date.now(),
  sender: 'ai',
  messageId: '2',
  recommendations: [
    {
      id: '1',
      title: '如何修改密码？',
      type: 'faq'
    },
    {
      id: '2',
      title: '账号被锁定怎么办？',
      type: 'faq'
    }
  ]
}
</script>
```

## 样式定制

组件提供以下CSS变量用于样式定制：

```css
:root {
  --chat-item-bg-color: #f5f5f5;
  --chat-item-text-color: #333;
  --chat-item-padding: 12px;
  --chat-item-border-radius: 8px;
  --chat-item-avatar-size: 40px;
}
```

## 注意事项

1. 消息类型（type）必须为有效值：'text'、'image'、'recommend'、'article'
2. 图片消息需要确保图片URL的有效性
3. 推荐问题列表建议不超过5个
4. 长文本消息会自动换行
5. 移动端需要注意消息气泡的最大宽度

## 最佳实践

1. 使用 `v-memo` 优化大量消息的渲染性能
2. 图片消息建议使用懒加载
3. 推荐问题建议使用防抖处理点击事件
4. 根据设备类型调整消息气泡的最大宽度

## 相关组件

- [MessageInput](./MessageInput.md) - 消息输入组件
- [QuickReply](./QuickReply.md) - 快速回复组件
