# ChatItem 组件分析

## 组件概述

ChatItem 是智能客服系统中的核心组件，负责展示聊天消息，包括用户问题和AI回答。该组件支持多种回答类型的展示，包括普通文本回答、自助查询卡片、推荐问题、评价反馈等功能。

## 组件结构

```vue
<template>
  <!-- 用户问题 -->
  <div class="chat-item user-q" v-if="!props.isWelcome && props.chatItem.question">
    <!-- 用户头像 -->
    <div class="avatar" :class="{ default: !props.avatar }" :style="{ backgroundImage: `url(${props.avatar})` }"></div>
    <!-- 用户问题内容 -->
    <div class="msg">{{ props.chatItem.question }}</div>
  </div>

  <!-- AI回答 -->
  <div class="chat-item gpt-a">
    <!-- AI头像 -->
    <div class="avatar">
      <!-- 思考中动画 -->
      <div class="icon-thinking" v-if="props.chatItem.type === 'gpt' && !props.finish">
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
      </div>
    </div>

    <!-- 回答内容区 -->
    <div class="rich-wrap">
      <div class="rich-wrap-bg"></div>
      <div class="rich-body">
        <!-- 问题回显 -->
        <div class="question" v-if="!props.isWelcome && props.chatItem.question">[{{ props.chatItem.question }}]</div>

        <!-- 自助查询卡片 -->
        <Card v-if="props.chatItem.from === 'selfService'" :msgItem="JSON.parse(props.chatItem.answer.replace(String(props.chatItem.guideContent) , ''))" @itemClick="compClickHandle"></Card>

        <!-- 点踩反馈 -->
        <Dislike v-else-if="props.chatItem.from === 'Dislike'" :msgItem="props.chatItem" @dislikeSuccess="dislikeSuccess"></Dislike>

        <!-- 普通回答 -->
        <div v-else>
          <!-- GPT回答 -->
          <div v-if="props.chatItem.type === 'gpt'" class="answer gpt-answer" :class="{ finish: props.finish, empty: !state.showContent }" v-html="state.showContent" @click.stop="contentClick"></div>

          <!-- 文章回答 -->
          <div v-if="props.chatItem.type === 'article'" class="answer" ref="richMsgEle">
            <RichText :msg="state.showContent" source-type="chat_rec_question"></RichText>
          </div>

          <!-- 推荐内容 -->
          <div v-if="props.finish && props.chatItem.recomQuestion?.length">
            <div class="divider"></div>
            <div class="question">[{{ $t('text_recommend_content') }}]</div>
            <div class="answer">
              <div v-for="(v, k) in props.chatItem.recomQuestion" :key="k">
                <span class="recommend-item" @click.stop="recommendClick(v)">{{ v }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 点赞点踩 -->
        <template v-if="props.finish && state.showContent && props.chatItem.id && !props.isWelcome && !props.chatItem.noShowLike && props.chatItem.from !== 'Dislike'">
          <div class="divider"></div>
          <LikeOrDislike :id="props.chatItem.id" @click="likeClick"></LikeOrDislike>
        </template>

        <!-- 免责声明 -->
        <div class="disclaimer" v-if="props.finish && props.chatItem.disclaimer">
          <span>{{ $t('text_disclaimer_copy') }}</span>
          <span @click.stop="errCorrect" class="btn_correct">{{ $t('btn_error_correction') }}</span>
        </div>

        <!-- 纠错弹窗 -->
        <CorrectPopup :show="showCorrect" v-if="showCorrect" @close="closeCorrect" :question="props.chatItem.question" :answer="state.showContent" />

        <!-- 问题未解决按钮 -->
        <div class="problem-box" v-if="props.finish && props.chatItem.problemUnresolved">
          <div class="problem" @click.stop="gotoTicket">
            <auto-font-size :text="$t('btn_problem_unresolved')"></auto-font-size>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 组件属性

```typescript
const props = withDefaults(defineProps<{
  chatItem: TChatItem       // 聊天消息对象
  finish: boolean           // 回答是否完成
  showContent?: string      // 显示内容（用于打字机效果）
  avatar?: string,          // 头像URL
  isWelcome?: boolean       // 是否为欢迎语
}>(), {
  finish: true,
  showContent: '',
  isWelcome: false
})
```

## 核心功能

### 1. 消息类型展示

ChatItem组件支持多种消息类型的展示：

- **用户问题**：展示用户头像和问题内容
- **GPT回答**：支持Markdown渲染，展示AI回答内容
- **文章回答**：使用RichText组件展示富文本内容
- **自助查询**：使用Card组件展示查询结果
- **点踩反馈**：使用Dislike组件展示不满意原因列表

### 2. 评价反馈

```typescript
const likeClick = (type: LIKE_TYPES[keyof LIKE_TYPES]) => {
  // 点踩时，如果需要展示原因列表
  if (type === 2 && (props.chatItem.showEval === '1' || props.chatItem.showEval === '3')) {
    emits('dislikeOptList', true)
  }

  // 记录日志
  setLog({
    event: 'elfin_appraise',
    position: 'faq',
    button: type,
    query: props.chatItem.question,
    answer: props.chatItem.answer,
    replied_from: props.chatItem.answerMode,
    message_id: props.chatItem.id,
    isShowLike: props.chatItem.noShowLike ? 1 : 0,
    isDisclaimer: props.chatItem.disclaimer ? 1 : 0
  })

  // 上报评价
  if (props.chatItem.type === 'gpt') {
    pushChatAppraise({
      message_id: props.chatItem.id,
      appraise: type, // 1: 赞, 2: 踩
      from: props.chatItem.from,
      query: props.chatItem.question,
      answer: postAnswer
    })
  }
}
```

### 3. 内容交互

组件支持多种内容交互方式：

```typescript
const contentClick = (event: Event) => {
  const ele = (event.target || event.srcElement) as HTMLElement

  // 处理工单提交链接点击
  if (ele.nodeName && ele.nodeName === 'SPAN') {
    // 命中词库的"提交工单"带有catId属性，跳转到指定分类
    if (ele.getAttribute('catId')) {
      router.push({
        path: '/newcs/answersDetail',
        query: {
          level_id: Number(ele.getAttribute('catId'))
        }
      })
    } else {
      // 两次未命中的"提交工单"跳转到一级总分类
      router.push({
        path: '/newcs/answersDetail'
      })
    }
  }

  // 处理图片点击，支持预览
  if (ele.nodeName && ele.nodeName === 'IMG') {
    const src = ele.getAttribute('src')
    if (src) {
      ImagePreview({
        images: [src],
        showIndex: false
      })
    }
  }
}
```

### 4. 纠错功能

```typescript
// 纠错弹窗
const showCorrect = ref<boolean>(false)
const errCorrect = () => {
  showCorrect.value = true
}
const closeCorrect = () => {
  showCorrect.value = false
}
```

### 5. 问题未解决反馈

```typescript
// 点击未解决，30s内只有一次生效
const gotoTicket = useThrottleFn(() => {
  emits('problemUnres')
}, 30000)
```

## 事件通信

组件通过以下事件与父组件通信：

```typescript
const emits = defineEmits<{
  (e: 'recommClick', question: string, type?: string): void
  (e: 'dislikeOptList', need: boolean, reason?: string, mid?: string): void
  (e: 'problemUnres'): void
}>()

// 推荐问题点击
const recommendClick = (question: string) => {
  emits('recommClick', question, 'chat_rec_question')
}

// 点踩原因提交成功
const dislikeSuccess = (reason: string) => {
  emits('dislikeOptList', false, reason, props.chatItem.id)
}
```

## 内容渲染

组件使用watchEffect监听内容变化，并根据消息类型进行不同的渲染处理：

```typescript
watchEffect(() => {
  let content = ''
  if (!props.finish) {
    content = props.showContent
  } else {
    content = props.chatItem.answer
  }
  // GPT回答使用Markdown渲染，其他类型直接显示
  state.showContent = props.chatItem.type === 'gpt' ? markdownRenderer(content) : content
})
```

## 组件交互流程

1. 用户发送问题，展示用户问题气泡
2. AI开始回答，显示思考中动画
3. AI回答内容逐步显示（打字机效果）
4. 回答完成后，根据回答类型展示不同内容：
   - 普通回答：显示文本内容
   - 自助查询：显示卡片内容
   - 推荐问题：显示推荐列表
5. 用户可以进行评价（点赞/点踩）
6. 点踩后可以选择不满意原因
7. 如果问题未解决，可以点击"问题未解决"按钮

## 样式特点

- 用户问题和AI回答使用不同的气泡样式
- 思考中状态显示三点动画
- 回答内容支持富文本渲染
- 推荐问题使用可点击的样式
- 评价按钮、纠错按钮和问题未解决按钮有明显的交互样式

## 注意事项

1. 组件内部处理了多种特殊情况，如工单提交链接、图片预览等
2. 使用节流函数防止频繁点击"问题未解决"按钮
3. 评价反馈需要上报到服务端
4. GPT回答支持Markdown渲染，需要注意XSS安全问题
5. 组件需要处理多种状态：欢迎语、回答中、回答完成等
