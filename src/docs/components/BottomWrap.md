# BottomWrap 组件分析

## 组件概述

BottomWrap 是智能客服系统中的底部输入区组件，负责用户输入交互，包括输入框提问、联想词提问和热门问题提问功能。该组件是用户与AI系统交互的主要入口。

## 组件结构

```vue
<template>
  <div class="bottom-wrap">
    <div class="container">
      <!-- 热门问题区域 -->
      <div class="hot-words" v-if="baseInfo.bottom_data.length">
        <div v-for="(item, index) in baseInfo.bottom_data" :key="index" class="item" @click.stop="wordClick(item)">
          <div class="bg">
            <div class="bg-1"></div>
            <div class="bg-2"></div>
            <div class="bg-3"></div>
          </div>
          <div class="content"><auto-font-size :text="item"></auto-font-size></div>
        </div>
      </div>

      <!-- 用户输入区域 -->
      <div class="user-input">
        <!-- 自助查询按钮 -->
        <div v-if="showSelfServies" @click.stop="wordClick($t('text_self_service'))" class="btn_self_servies">
          <auto-font-size :text="$t('btn_self_help')"></auto-font-size>
        </div>

        <!-- 搜索表单 -->
        <form class="search-form" @submit="submit">
          <!-- 联想词列表 -->
          <div class="q-list" :style="{ height: qListHeight }">
            <div class="q-item" v-for="(item, i) in qList" :key="`${i}_${item}`" @click.stop="qItemClick(item)">{{ item }}</div>
          </div>

          <!-- 输入框 -->
          <input class="input" ref="inputEle" :placeholder="$t('txt_placeholder')" v-model.trim="searchTxt" />
        </form>

        <!-- 提交按钮 -->
        <div class="btn submit" @click.stop="submit">
          <auto-font-size :text="$t('txt_submit')"></auto-font-size>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 组件功能

BottomWrap 组件提供三种提问方式：

1. **输入框提问**：用户通过输入框输入问题并提交
2. **联想词提问**：用户输入部分内容后，选择系统提供的联想词
3. **热门问题提问**：用户直接点击预设的热门问题

## 核心功能实现

### 1. 事件通信

组件通过以下事件与父组件通信：

```typescript
const emits = defineEmits<{
  (event: 'hotWord-click', txt: string): void
  (event: 'submit', txt: string, type: string): void
}>()
```

- `hotWord-click`：热门问题点击事件，传递热门问题内容
- `submit`：提交事件，传递提问内容和提问类型

### 2. 热门问题处理

热门问题从 Vuex store 中获取，点击后触发 `hotWord-click` 事件：

```typescript
const baseInfo = computed(() => state.baseInfo)

const wordClick = (content: string) => {
  emits('hotWord-click', content)
}
```

### 3. 输入框提交

用户在输入框中输入内容并提交，触发 `submit` 事件：

```typescript
const submit = (event: Event) => {
  event.stopPropagation && event.stopPropagation()
  event.preventDefault && event.preventDefault()
  if (!searchTxt.value) return
  emits('submit', searchTxt.value, 'chat_custom')
  searchTxt.value = ''
  inputEle.value?.blur && inputEle.value?.blur()
}
```

### 4. 联想词功能

当用户输入内容时，系统会根据输入内容提供联想词：

```typescript
watch(searchTxt, useDebounceFn(() => {
  if (!searchTxt.value || searchTxt.value.length < 3) {
    return qList.value = []
  }
  // 联想词接口
  getHotQList({
    query: searchTxt.value
  }).then((res: any) => {
    res = res || []
    if (searchTxt.value) qList.value = res
  })
}, 500))
```

联想词点击处理：

```typescript
const qItemClick = (txt: string) => {
  emits('submit', txt, 'asso_answer')
  searchTxt.value = ''
  inputEle.value?.blur && inputEle.value?.blur()
}
```

### 5. 自助查询功能

根据项目配置决定是否显示自助查询按钮：

```typescript
const showSelfServies = ref<boolean>(['ss', 'gog'].indexOf(sessionStorage.getItem('projectName') as string) > -1)
```

## 交互流程

1. **热门问题交互**：
   - 用户点击热门问题
   - 组件触发 `hotWord-click` 事件
   - 父组件接收事件并处理问题

2. **输入框交互**：
   - 用户在输入框中输入内容
   - 如果输入长度超过3个字符，系统请求联想词
   - 用户可以点击提交按钮或按回车提交问题
   - 组件触发 `submit` 事件，类型为 `chat_custom`
   - 父组件接收事件并处理问题

3. **联想词交互**：
   - 用户输入部分内容后，系统显示联想词列表
   - 用户点击联想词
   - 组件触发 `submit` 事件，类型为 `asso_answer`
   - 父组件接收事件并处理问题

4. **自助查询交互**：
   - 用户点击自助查询按钮
   - 组件触发 `hotWord-click` 事件，内容为自助查询文本
   - 父组件接收事件并处理自助查询请求

## 性能优化

1. **防抖处理**：
   使用 `useDebounceFn` 对输入框内容变化进行防抖处理，避免频繁请求联想词接口：

   ```typescript
   watch(searchTxt, useDebounceFn(() => {
     // 联想词处理逻辑
   }, 500))
   ```

2. **动态高度计算**：
   根据联想词数量动态计算联想词列表高度：

   ```typescript
   const qListHeight = computed(() => {
     const l = qList.value.length
     if (!l) return 0
     const h = (l * 47 + 2) / 133.4
     return `${h}rem`
   })
   ```

## 样式特点

- 热门问题使用特殊背景样式，提高点击率
- 输入框和提交按钮采用直观的布局
- 联想词列表使用下拉样式，方便用户选择
- 自助查询按钮使用醒目样式，引导用户使用

## 注意事项

1. 联想词功能仅在输入内容超过3个字符时触发
2. 自助查询按钮仅在特定项目中显示
3. 提交空内容时不会触发事件
4. 提交后会自动清空输入框并失去焦点
5. 组件使用了响应式API，确保UI与数据同步
