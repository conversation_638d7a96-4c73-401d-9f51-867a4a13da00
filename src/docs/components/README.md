# 智能客服系统组件文档

## 组件概述

智能客服系统采用组件化开发方式，将界面拆分为可复用的独立组件。本文档提供了系统中所有组件的详细说明，包括组件功能、属性、事件和使用示例。

## 组件分类

### 布局组件
- [Layout 基础布局](./layout/Layout.md) - 系统整体布局框架

### 智能客服组件
- [ChatItem 聊天消息](./smart/ChatItem.md) - 展示用户问题和AI回答
- [MessageInput 消息输入](./smart/MessageInput.md) - 用户输入交互组件
- [QuickReply 快速回复](./smart/QuickReply.md) - 快速回复推荐组件
- [Card 卡片组件](./Card.md) - 结构化数据展示组件
- [BottomWrap 底部操作栏](./BottomWrap.md) - 底部输入区组件

### 工单组件
- [TicketForm 工单表单](./ticket/TicketForm.md) - 工单创建表单
- [TicketDetail 工单详情](./ticket/TicketDetail.md) - 工单详情展示
- [TicketList 工单列表](./ticket/TicketList.md) - 工单列表展示
- [TicketStatus 工单状态](./ticket/TicketStatus.md) - 工单状态展示
- [CategoryTree 分类树](./ticket/CategoryTree.md) - 工单分类选择组件

### 通用组件
- [RichText 富文本展示](./common/RichText.md) - 富文本内容渲染
- [LikeOrDislike 评价组件](./common/LikeOrDislike.md) - 用户反馈评价
- [ImgUpload 图片上传](./common/ImgUpload.md) - 图片上传组件
- [VideoUpload 视频上传](./common/VideoUpload.md) - 视频上传组件
- [AutoFontSize 自适应文字](./common/AutoFontSize.md) - 文字大小自适应
- [FpVideo 视频播放器](./common/FpVideo.md) - 自定义视频播放器

## 组件关系图

```mermaid
graph TD
    A[Layout] --> B[智能客服页面]
    A --> C[工单页面]
    B --> D[ChatItem]
    B --> E[BottomWrap]
    D --> F[Card]
    D --> G[LikeOrDislike]
    E --> H[MessageInput]
    E --> I[QuickReply]
    C --> J[TicketForm]
    C --> K[TicketList]
    J --> L[CategoryTree]
    J --> M[ImgUpload]
    J --> N[VideoUpload]
```

## 数据流

```mermaid
sequenceDiagram
    participant User
    participant BottomWrap
    participant SmartPage
    participant API
    participant ChatItem

    User->>BottomWrap: 输入问题
    BottomWrap->>SmartPage: 提交问题
    SmartPage->>API: 请求回答
    API-->>SmartPage: 返回回答
    SmartPage->>ChatItem: 展示回答
    ChatItem-->>User: 查看回答
    User->>ChatItem: 评价回答
    ChatItem->>API: 提交评价
```

## 核心组件详解

### ChatItem 聊天消息组件

ChatItem组件是智能客服系统的核心组件，负责展示用户问题和AI回答。支持多种回答类型，包括文本、卡片、推荐问题等。

#### 主要功能
- 展示用户问题和AI回答
- 支持多种回答类型
- 提供评价反馈功能
- 支持打字机效果

#### 使用示例
```vue
<ChatItem
  :chat-item="item"
  :finish="true"
  :avatar="avatar"
  @recommClick="handleRecommClick"
  @dislikeOptList="handleDislike"
  @problemUnres="handleProblemUnres">
</ChatItem>
```

### BottomWrap 底部操作栏组件

BottomWrap组件是用户输入交互的主要组件，提供问题输入、快速回复等功能。

#### 主要功能
- 提供问题输入框
- 展示联想词和热门问题
- 支持语音输入（部分设备）
- 提供快速回复功能

#### 使用示例
```vue
<BottomWrap
  @submit="handleSubmit"
  @hotWord-click="handleHotWordClick">
</BottomWrap>
```

### Card 卡片组件

Card组件用于展示结构化数据，支持多种展示形式。

#### 主要功能
- 卡片形式展示数据
- 列表形式展示数据
- 表格形式展示数据
- 支持点击交互

#### 使用示例
```vue
<Card
  :msgItem="cardData"
  @item-click="handleItemClick">
</Card>
```

## 组件开发规范

### 命名规范
1. 组件文件名：使用 PascalCase（如 ChatItem.vue）
2. 组件名称：使用 PascalCase（如 ChatItem）
3. Props 属性名：使用 camelCase（如 chatItem）
4. 事件名称：使用 kebab-case（如 item-click）

### 目录结构
```
components/
├── layout/          # 布局组件
├── smart/           # 智能客服组件
├── ticket/          # 工单组件
└── common/          # 通用组件
```

### 文档编写规范
每个组件文档必须包含以下内容：
1. 组件说明
2. 组件属性（Props）
3. 组件事件（Events）
4. 插槽说明（Slots）
5. 使用示例
6. 注意事项

### 组件设计原则
1. 单一职责 - 每个组件只负责一个功能
2. 可复用性 - 组件设计应考虑复用场景
3. 可测试性 - 组件应易于测试
4. 松耦合 - 组件之间应尽量减少依赖
5. 高内聚 - 相关功能应集中在同一组件

## 最佳实践

### 性能优化
1. 使用 v-show 代替 v-if（频繁切换场景）
2. 合理使用计算属性和缓存
3. 大列表使用虚拟滚动
4. 懒加载组件和资源

### 兼容性处理
1. 移动端适配使用 rem 或 vw/vh
2. 考虑不同设备的触控体验
3. 处理 iOS 和 Android 的差异
4. 优雅降级和渐进增强

### 无障碍支持
1. 提供适当的 aria 属性
2. 确保键盘可访问性
3. 提供足够的颜色对比度
4. 支持屏幕阅读器
