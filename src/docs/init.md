# 智能客服系统初始化页面逻辑

## 1. 项目启动流程

### 1.1 入口文件 (main.ts)

项目启动时，首先执行 `main.ts` 中的 `startApp` 函数：

```typescript
async function startApp() {
  await loadStyles() // 等待loadStyles函数执行完成

  // 调试模式
  // if (process.env.NODE_ENV === 'test' || process.env.NODE_ENV === 'development' || location.host === 'fpcs-web-test.funplus.com' || location.host === 'fpcs-web-test.funplus.com.cn') {
  // 	new VConsole()
  // }

  const app = createApp(<RendererElement>App)
  // setupI18n(app)
  app
    .use(store)
    .use(router)
    .use(VueAxios, FpAxios.init())
    .mount('#app')
  app
    .use(packages)
    .use(fpUtils)
  setupPlugins(app)
}
startApp()
```

`loadStyles` 函数会根据项目配置动态加载样式：

```typescript
async function loadStyles() {
  try {
    const { mark, projectName } = await Upgrade()
    if (mark) {
      await import(`@/styles/${projectName}/style.scss`)
    } else {
      await import('@/styles/style.scss')
    }
  } catch (error) {
    console.error('Error while upgrading:', error)
  }
}
```

### 1.2 灰度检测 (utils/index.ts 中的 Upgrade 函数)

`Upgrade` 函数负责检查用户是否命中灰度测试，决定是否使用新版客服系统：

```typescript
export const Upgrade = async (): Promise<Record<string, unknown>> => {
  if (sessionStorage.getItem('mark') && sessionStorage.getItem('projectName') && sessionStorage.getItem('isPC')) {
    return {
      mark: JSON.parse(sessionStorage.getItem('mark') ?? 'false'),
      projectName: sessionStorage.getItem('projectName'),
      isPC: JSON.parse(sessionStorage.getItem('isPC') ?? 'false'),
    }
  }

  // ... 获取用户参数 ...

  const checkGrayMode = async (): Promise<void> => {
    try {
      // 灰度+跳转精灵接口
      const response = await fetch('/backend/v3/elfin/grayscale', {
        method: 'POST',
        headers: {
          sign,
          'api-key': apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params)
      })

      const data = await response.json()
      // hit：是否命中灰度+是否跳转精灵
      // 判断是否游戏外场景（游戏正在加载过程中点击联系客服，直接跳通用工单）
      // scene含义：0：定制版游戏内，3：通用版游戏内，1：加载，2：封号
      mark = data.data.hit && Number(params.scene) !== 1 && Number(params.scene) !== 2
      projectName = data.data.projectName // 游戏名称 前端用来动态加载对应的样式组
      vipUser = data.data.vipUser // 是否是VIP用户
      sessionStorage.setItem('mark', JSON.stringify(mark))
      sessionStorage.setItem('projectName', projectName)
      sessionStorage.setItem('vipUser', JSON.stringify(vipUser))
    } catch (error) {
      console.error('There has been a problem with your fetch operation: ', error)
    }
  }

  await checkGrayMode()
  const isPC = window.location.href.indexOf('/pc') > -1
  sessionStorage.setItem('isPC', JSON.stringify(isPC))

  return {
    mark,
    projectName,
    isPC
  }
}
```

## 2. 路由初始化与重定向

### 2.1 路由配置 (routes.ts)

路由配置中定义了根路径 `/` 重定向到 `/smart`，以及 `/newcs` 重定向到 `/newcs/homePage`：

```typescript
export const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    redirect: '/smart',
    children: [
      {
        path: '/smart',
        name: 'Smart',
        component: Smart,
      },
      // ...其他路由...

      // newcs路由
      {
        path: '/newcs',
        redirect: '/newcs/homePage',
      },
      {
        path: '/newcs/homePage',
        name: 'HomePage',
        component: HomePage,
      },
      // ...其他newcs路由...
    ],
  },
];
```

### 2.2 Layout 组件初始化 (layout/index.vue)

当用户访问根路径 `/` 时，会被重定向到 `/smart`，并加载 `Layout` 组件。在 `Layout` 组件中，`initCs` 函数会根据灰度测试结果决定是否重定向到新版客服系统：

```typescript
const initCs = (): void => {
  const Properties = getCurrentInstance()?.appContext.config.globalProperties
  const params = Properties && Properties.$utils.getParamsFromUrl()
  const url = window.location.href

  // ... 处理用户参数和语言设置 ...

  const mark = JSON.parse(sessionStorage.getItem('mark') ?? 'false')
  // 判断是否游戏外场景（游戏正在加载过程中点击联系客服，直接跳工单）
  // scene含义：0：定制版游戏内，3：通用版游戏内，1：加载，2：封号
  if (mark) {
    commit('setLoadingCount', 1)
    // index接口
    homePageIndex({}).then((res: TindexInfo) => {
      commit('setBaseInfo', res)
    }).catch((err: string) => {
      console.log(err)
    }).finally(() => {
      commit('setLoadingCount', 0)
    })
    // 重定向到升级版客服首页
    if (route.path.indexOf('/newcs') > -1) return
    router.replace('/newcs')
  } else {
    commit('setLoadingCount', -1)
    const img= require('@/assets/img/csbg.jpg')
    document.getElementById('app')?.setAttribute('style', `background-image: url(${img})`)
    if (params.autoUnMatch === 'true') {
      if (route.path.indexOf('/pc') > -1) {
        router.push('/pc/tickets')
      } else {
        router.push('/tickets')
      }
    }
  }
}
initCs()
```

## 3. 页面组件初始化

### 3.1 Smart 组件 (views/Smart.vue)

如果用户未命中灰度测试，会加载旧版客服系统的 `Smart` 组件：

```typescript
onMounted(() => {
  // 获取热门大卡ID
  hotCard({}).then((res: Record<string, number>) => {
    if (res.card_id !== -1) {
      getCardInfo(res.card_id, true)
    }
  })
  // H5兼容ios12 监听所有input blur
  const inputsElement = [...document.getElementsByTagName('input')]
  inputsElement.forEach(element => {
    if (element) {
      element.onblur = () => {
        setTimeout(() => {
          const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
          window.scrollTo(0, Math.max(scrollHeight - 1, 0))
        }, 300)
      }
    }
  })
})
```

### 3.2 HomePage 组件 (views/New/HomePage.vue)

如果用户命中灰度测试，会被重定向到 `/newcs/homePage`，加载新版客服系统的 `HomePage` 组件：

```typescript
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
setLog({
  event: 'elfin_load_done',
  position: 'homepage',
  result: 'success'
})
const { state } = useStore()
const baseInfo = computed(() => state.baseInfo)
const vipUser = JSON.parse(sessionStorage.getItem('vipUser') || baseInfo.value.vipUser || 'false')
```

对于VIP用户和普通用户，显示不同的界面：

```html
<div class="answers-detail" v-if="vipUser">
  <!-- VIP用户界面 -->
</div>
<div class="home-page" v-else>
  <!-- 普通用户界面 -->
</div>
```

### 3.3 Tickets 组件 (views/Tickets.vue)

如果用户未命中灰度测试，且 `params.autoUnMatch` 为 `'true'`，会直接跳转到工单页面：

```typescript
// 获取入口信息, 判断是否是智能客服点踩后跳转的工单需求
const params = route.query.dislike_tickets && route.query.dislike_tickets.length > 0 ? {
  fork_cat_ids: (route.query.dislike_tickets as Array<string>).map((item: string | number) => {
    return +item
  })
} : {}
// 获取入口树-数据来源是 客服工单系统-问题分类配置
sceneEntrance(params).then((res: ticketDataListT) => {
  data.loading = false
  // 前端处理数据，看返回数据是否只有一个分支，如果只有一个分支，则只要最后一层的对象
  const isOne = (list: Array<ticketDataT>): false | ticketDataT => {
    if (list.length === 1) {
      if (list[0].children) {
        return isOne(list[0].children)
      } else {
        return list[0]
      }
    } else {
      return false
    }
  }
  // 处理后数组则为树状层级筛选，如果为对象则直接渲染表单
  const rdata = isOne(res)
  if (rdata === false) {
    if (res.length > 0) {
      data.ticketDataList.push({
        level: 0,
        index: data.ticketDataList.length,
        children: res
      })
    }
  } else {
    const item = {
      ...rdata,
      index: data.ticketDataList.length
    }
    data.ticketDataList.push(item)
  }
})
```

### 3.4 PC Tickets 组件 (views/PC/Tickets.vue)

如果用户未命中灰度测试，且 `params.autoUnMatch` 为 `'true'`，并且是PC端（URL中包含 `/pc`），会直接跳转到PC端工单页面。PC端工单页面的逻辑与移动端类似，但UI和交互适配了PC端的特点。

## 4. 完整重定向流程

浏览器初始化进入项目根路径 `/` 时，可能会经历以下几种重定向流程：

### 4.1 新版客服系统流程

1. **第一次重定向**：`/` → `/smart`
   - 这是在路由配置 `routes.ts` 中设置的，根路径 `/` 被配置为重定向到 `/smart`

2. **第二次重定向**：`/smart` → `/newcs`
   - 这是在 `Layout` 组件的 `initCs` 函数中实现的
   - 当用户访问 `/smart` 时，会加载 `Layout` 组件
   - 在组件初始化时，会检查 sessionStorage 中的 `mark` 值
   - 如果 `mark` 为 true（表示用户命中灰度测试并且不是在游戏加载或封号场景），则重定向到 `/newcs`
   - `mark` 值是通过调用 `/backend/v3/elfin/grayscale` API 设置的，根据用户信息和场景判断是否应该使用新版客服系统

3. **第三次重定向**：`/newcs` → `/newcs/homePage`
   - 这是在路由配置 `routes.ts` 中设置的，`/newcs` 路径被配置为重定向到 `/newcs/homePage`

### 4.2 旧版客服系统流程

1. **第一次重定向**：`/` → `/smart`
   - 这是在路由配置 `routes.ts` 中设置的，根路径 `/` 被配置为重定向到 `/smart`

2. **停留在 `/smart`**：
   - 如果 `mark` 为 false（表示用户未命中灰度测试），则停留在 `/smart` 页面
   - 加载旧版客服系统的 `Smart` 组件

### 4.3 直接跳转工单流程

1. **第一次重定向**：`/` → `/smart`
   - 这是在路由配置 `routes.ts` 中设置的，根路径 `/` 被配置为重定向到 `/smart`

2. **第二次重定向**：`/smart` → `/tickets` 或 `/pc/tickets`
   - 这是在 `Layout` 组件的 `initCs` 函数中实现的
   - 如果 `mark` 为 false（表示用户未命中灰度测试）且 `params.autoUnMatch` 为 `'true'`
   - 根据是否是PC端（URL中是否包含 `/pc`），跳转到不同的工单页面
   - PC端跳转到 `/pc/tickets`，移动端跳转到 `/tickets`

## 5. 特殊场景处理

### 5.1 自动跳转工单

在某些特殊场景下，系统会自动跳转到工单页面：

```typescript
if (params.autoUnMatch === 'true') {
  if (route.path.indexOf('/pc') > -1) {
    router.push('/pc/tickets')
  } else {
    router.push('/tickets')
  }
}
```

这种情况通常发生在：
1. 用户在游戏加载过程中点击联系客服
2. 用户账号被封禁
3. 其他需要直接提交工单的场景

### 5.2 VIP用户处理

对于VIP用户，系统会显示特殊的VIP界面：

```typescript
const vipUser = JSON.parse(sessionStorage.getItem('vipUser') || baseInfo.value.vipUser || 'false')
```

```html
<div class="answers-detail" v-if="vipUser">
  <!-- VIP用户界面 -->
</div>
```

### 5.3 私域客服处理

对于从私域进入的用户，系统会显示导航栏并提供返回功能：

```typescript
const isPrivZone = computed(() => state.userInfo?.zone_from === 'privZone')
```

```html
<van-nav-bar
  v-if="isPrivZone"
  :title="$t('title_general_my_cs')"
  fixed
  z-index="10"
  safe-area-inset-top
  left-arrow
  @click-left="goBack"
/>
```

## 6. 总结

智能客服系统的初始化逻辑主要包括：

1. 项目启动时，通过 `Upgrade` 函数检查用户是否命中灰度测试
2. 根据灰度测试结果，决定使用旧版客服系统还是新版客服系统
3. 路由重定向流程有三种可能：
   - 新版客服系统流程：`/` → `/smart` → `/newcs` → `/newcs/homePage`
   - 旧版客服系统流程：`/` → `/smart`
   - 直接跳转工单流程：`/` → `/smart` → `/tickets` 或 `/pc/tickets`
4. 对于特殊场景（如游戏加载中、封号等）和VIP用户，有特殊的处理逻辑

这种重定向机制的目的是实现灰度发布，根据用户信息和场景决定使用哪种客服系统。

## 7. 页面具体展示逻辑

为了更好地理解智能客服系统的各个页面和组件，以下是相关文档的索引和简要说明：

### 7.1 页面文档

#### 7.1.1 新版客服首页 (`docs/pages/smart/NewSmart.md`)
- 新版客服系统的入口页面
- 包含智能问答、自助查询和工单提交等核心功能
- 针对VIP和普通用户显示不同的界面布局
- 实现了智能推荐和个性化服务功能

#### 7.1.2 旧版客服首页 (`docs/pages/smart/Smart.md`)
- 旧版客服系统的主页面
- 提供基础的问答和工单功能
- 包含热门问题卡片和常见问题列表
- 支持基本的用户反馈和评价机制

#### 7.1.3 工单页面 (`docs/pages/tickets/Tickets.md`)
- 移动端工单提交和管理页面
- 实现问题分类的树状选择结构
- 支持附件上传和工单状态跟踪
- 包含表单验证和提交逻辑

#### 7.1.4 PC端工单页面 (`docs/pages/tickets/PcTickets.md`)
- 适配PC端的工单系统界面
- 提供更宽屏的布局和交互体验
- 支持更丰富的键盘操作和快捷方式
- 包含与移动端相同的核心功能，但UI和交互有所不同

### 7.2 组件文档

#### 7.2.1 聊天组件 (`docs/components/ChatItem.md`)
- 实现客服对话的消息展示
- 支持多种消息类型：文本、图片、链接等
- 包含消息状态指示和时间戳显示
- 实现了消息加载动画和错误重试功能

#### 7.2.2 底部输入区域 (`docs/components/BottomWrap.md`)
- 用户输入界面的实现
- 支持文本输入、表情选择和附件上传
- 实现了输入区域的动态高度调整
- 包含发送按钮状态管理和输入验证

#### 7.2.3 自助查询卡片 (`docs/components/Card.md`)
- 展示自助服务选项的卡片组件
- 支持多种卡片布局和样式
- 实现了卡片点击统计和热门推荐逻辑
- 包含卡片内容的动态加载和缓存机制

### 7.3 数据流与交互关系

各页面和组件之间的数据流动和交互关系如下：

1. **用户初始进入系统**：
   - 通过初始化逻辑决定加载新版或旧版客服首页
   - 首页加载相关组件（聊天组件、底部输入区域、自助查询卡片等）

2. **用户交互流程**：
   - 用户可在首页进行智能问答（使用聊天组件和底部输入区域）
   - 用户可浏览和选择自助查询卡片获取常见问题解答
   - 用户可选择提交工单，系统跳转至对应的工单页面

3. **工单处理流程**：
   - 用户在工单页面选择问题类型和填写详情
   - 系统验证表单并提交工单
   - 用户可在工单页面查看历史工单和跟踪状态

### 7.4 开发与维护建议

在开发和维护智能客服系统时，建议：

1. 先了解整体初始化流程和路由机制（本文档前6节）
2. 根据需求确定要修改的页面或组件
3. 参考对应的详细文档了解具体实现
4. 遵循现有的数据流和交互模式进行开发
5. 注意新旧版本的兼容性和灰度发布机制

通过参考这些文档，开发人员可以全面了解智能客服系统的架构和实现细节，从而更高效地进行开发和维护工作。
