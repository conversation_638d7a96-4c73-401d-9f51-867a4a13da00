# 组件使用文档

## 组件概览

系统包含以下主要组件类型：
1. 布局组件
2. 功能组件
3. 业务组件
4. UI组件

详细的组件文档请参考 [组件文档目录](./components/README.md)，其中包含：

- 布局组件文档
- 智能客服组件文档
- 工单组件文档
- 知识库组件文档
- 通用组件文档

每个组件都有详细的：
- 组件说明
- 属性（Props）文档
- 事件（Events）文档
- 插槽（Slots）说明
- 使用示例
- 注意事项

## 布局组件

### Layout组件
```typescript
import Layout from '@/layout/index.vue'
```
- **功能**：提供基础页面布局
- **使用场景**：所有页面的基础布局
- **主要特性**：
  - 响应式布局
  - 支持移动端和PC端
  - 统一的页面结构

## 功能组件

### 1. BottomWrap组件
```typescript
import BottomWrap from './components/BottomWrap.vue'
```
- **功能**：底部搜索和操作栏
- **使用场景**：首页和智能客服页面
- **主要特性**：
  - 搜索输入
  - 热门问题展示
  - 快捷操作按钮

### 2. RichText组件
```typescript
import RichText from './components/RichText.vue'
```
- **功能**：富文本内容展示
- **使用场景**：文章详情、工单内容等
- **主要特性**：
  - 支持HTML内容
  - 自动链接识别
  - 图片预览

### 3. LikeOrDislike组件
```typescript
import LikeOrDislike from './components/LikeOrDislike.vue'
```
- **功能**：评价功能组件
- **使用场景**：文章、AI回答评价
- **主要特性**：
  - 点赞/点踩功能
  - 评价原因收集
  - 动画效果

## 业务组件

### 1. 智能客服相关组件

#### ChatItem组件
```typescript
import ChatItem from './components/SmartComp/ChatItem.vue'
```
- **功能**：聊天消息展示
- **属性**：
  ```typescript
  props: {
    chatItem: Object,    // 聊天内容
    finish: Boolean,     // 是否完成
    isWelcome: Boolean,  // 是否欢迎消息
    avatar: String       // 头像
  }
  ```
- **事件**：
  - recommClick：推荐问题点击
  - dislikeOptList：不满意原因列表
  - problemUnres：问题未解决

### 2. 工单相关组件

#### TicketForm组件
```typescript
import TicketForm from './components/AnswerComp/TicketForm.vue'
```
- **功能**：工单表单
- **属性**：
  ```typescript
  props: {
    itemData: Object,    // 表单数据
    fromTicketId: Number // 来源工单ID
  }
  ```
- **事件**：
  - submitSuccess：提交成功
  - updateScroll：更新滚动

#### AutoFlowComp组件
```typescript
import AutoFlowComps from './components/AutoFlowComp'
```
- **功能**：自动化流程组件集
- **子组件**：
  - AutoRichText：富文本展示
  - AutoTickets：工单处理
  - AutoUserSelect：用户选择
  - AutoUserInput：用户输入

### 3. 上传组件

#### ImgUpload组件
```typescript
import ImgUpload from './components/Upload/ImgUpload.vue'
```
- **功能**：图片上传
- **属性**：
  ```typescript
  props: {
    isH5Upload: Boolean,  // 是否H5上传
    maxCount: Number,     // 最大上传数量
    maxSize: Number       // 最大文件大小
  }
  ```
- **事件**：
  - success：上传成功
  - remove：删除文件

#### VideoUpload组件
```typescript
import VideoUpload from './components/Upload/VideoUpload.vue'
```
- **功能**：视频上传
- **属性**：与ImgUpload类似
- **事件**：与ImgUpload类似

## UI组件

### 1. AutoFontSize组件
```typescript
import AutoFontSize from './components/Common/AutoFontSize.vue'
```
- **功能**：文字自适应大小
- **属性**：
  ```typescript
  props: {
    text: String,        // 显示文本
    maxWidth: Number     // 最大宽度
  }
  ```

### 2. FpVideo组件
```typescript
import FpVideo from './components/Common/FpVideo.vue'
```
- **功能**：视频播放器
- **属性**：
  ```typescript
  props: {
    src: String,         // 视频源
    autoplay: Boolean,   // 自动播放
    controls: Boolean    // 显示控制栏
  }
  ```

## 组件使用示例

### 1. 使用布局组件
```vue
<template>
  <Layout>
    <div class="content">
      <!-- 页面内容 -->
    </div>
  </Layout>
</template>
```

### 2. 使用聊天组件
```vue
<template>
  <ChatItem
    :chat-item="messageData"
    :finish="true"
    :avatar="userAvatar"
    @recommClick="handleRecommClick"
  />
</template>
```

### 3. 使用上传组件
```vue
<template>
  <ImgUpload
    :isH5Upload="true"
    :maxCount="5"
    @success="handleUploadSuccess"
    @remove="handleRemove"
  />
</template>
```

## 组件开发规范

1. **命名规范**
   - 组件文件名：PascalCase
   - 组件名称：PascalCase
   - props名称：camelCase
   - 事件名称：kebab-case

2. **目录结构**
   ```
   components/
   ├── layout/          # 布局组件
   ├── smart/           # 智能客服组件
   ├── ticket/          # 工单组件
   ├── article/         # 知识库组件
   └── common/          # 通用组件
   ```

3. **组件设计原则**
   - 单一职责
   - 可复用性
   - 可测试性
   - 松耦合
   - 高内聚

4. **文档要求**
   - 必须包含功能说明
   - 必须列出props和events
   - 必须提供使用示例
   - 必须说明注意事项

5. **代码规范**
   - 使用 TypeScript
   - 使用 Composition API
   - 遵循 ESLint 规则
   - 编写单元测试
