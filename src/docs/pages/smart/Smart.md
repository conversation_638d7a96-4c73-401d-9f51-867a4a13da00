# 旧版客服首页分析

## 业务流程

### 1. 核心流程
```mermaid
graph TD
    A[用户进入] --> B[加载热门问题]
    B --> C[展示欢迎语]
    C --> D[等待用户操作]
    D --> E{用户操作类型}
    E -->|输入问题| F[发送问题]
    E -->|点击热门问题| G[选择热门问题]
    E -->|点击自助查询| H[进入自助查询]
    F --> I[获取回答]
    G --> I
    I --> J[展示回答]
    J --> K[用户评价]
    K --> L{评价类型}
    L -->|满意| M[记录反馈]
    L -->|不满意| N[提供工单入口]
    N --> O[跳转工单]
```

### 2. 关键节点
1. 热门问题加载
   - 获取热门问题卡片
   - 展示常见问题列表
   - 问题点击统计

2. 问答交互
   - 问题输入处理
   - 回答类型判断
   - 回答展示格式化

3. 评价处理
   - 点赞/点踩记录
   - 不满意原因收集
   - 工单跳转处理

## 代码实现

### 1. 页面结构
```vue
<template>
  <div class="smart-page">
    <!-- 头部区域 -->
    <div class="header">
      <div class="title">{{ $t('customer_service') }}</div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 热门问题卡片 -->
      <div v-if="hotCardInfo.id" class="hot-card">
        <div class="card-title">{{ hotCardInfo.title }}</div>
        <div class="card-content" v-html="hotCardInfo.content"></div>
      </div>

      <!-- 聊天记录 -->
      <div class="chat-list">
        <div v-for="(item, index) in chatList" :key="index" class="chat-item">
          <!-- 用户问题 -->
          <div v-if="item.question" class="question">
            <div class="question-content">{{ item.question }}</div>
          </div>

          <!-- 系统回答 -->
          <div class="answer">
            <div class="answer-content" v-html="item.answer"></div>

            <!-- 评价按钮 -->
            <div v-if="!item.noShowLike" class="feedback">
              <div
                class="like"
                :class="{ active: item.like === true }"
                @click="handleLike(item, true)">
                <img :src="item.like === true ? likeActiveIcon : likeIcon" alt="like">
              </div>
              <div
                class="dislike"
                :class="{ active: item.like === false }"
                @click="handleLike(item, false)">
                <img :src="item.like === false ? dislikeActiveIcon : dislikeIcon" alt="dislike">
              </div>
            </div>

            <!-- 不满意原因 -->
            <div v-if="item.like === false && !item.selectReason" class="dislike-reasons">
              <div class="reason-title">{{ $t('why_not_satisfied') }}</div>
              <div class="reason-list">
                <div
                  v-for="(reason, rIndex) in dislikeReasons"
                  :key="rIndex"
                  class="reason-item"
                  @click="selectDislikeReason(item, reason)">
                  {{ reason.text }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="footer">
      <div class="input-area">
        <input
          type="text"
          v-model="inputText"
          :placeholder="$t('input_question')"
          @keyup.enter="sendQuestion"
        >
        <div class="send-btn" @click="sendQuestion">{{ $t('send') }}</div>
      </div>

      <!-- 常见问题列表 -->
      <div class="common-questions">
        <div class="title">{{ $t('common_questions') }}</div>
        <div class="question-list">
          <div
            v-for="(question, index) in commonQuestions"
            :key="index"
            class="question-item"
            @click="selectCommonQuestion(question)">
            {{ question.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 2. 数据结构
```typescript
// 聊天消息类型
interface ChatItem {
  question?: string;        // 用户问题
  answer: string;           // 系统回答
  like?: boolean;           // 评价状态：true-满意，false-不满意，undefined-未评价
  noShowLike?: boolean;     // 是否不显示评价按钮
  selectReason?: string;    // 选择的不满意原因
  answerType?: string;      // 回答类型
  messageId?: string;       // 消息ID
}

// 热门卡片信息
interface CardInfo {
  id: number;
  title: string;
  content: string;
  type: number;
}

// 页面数据
const data = reactive({
  inputText: '',                // 输入框文本
  chatList: [] as ChatItem[],   // 聊天记录
  hotCardInfo: {} as CardInfo,  // 热门卡片信息
  loading: false,               // 加载状态
  commonQuestions: []           // 常见问题列表
});

// 不满意原因列表
const dislikeReasons = [
  { id: 1, text: '回答不准确' },
  { id: 2, text: '回答不完整' },
  { id: 3, text: '回答无法理解' },
  { id: 4, text: '其他原因' }
];
```

### 3. 核心方法

#### 3.1 获取热门卡片
```typescript
// 获取热门卡片
const getHotCard = () => {
  // 调用热门卡片API
  hotCard({}).then((res: Record<string, number>) => {
    if (res.card_id !== -1) {
      getCardInfo(res.card_id, true);
    }
  }).catch(err => {
    console.error('获取热门卡片失败', err);
  });
};

// 获取卡片详情
const getCardInfo = (cardId: number, isHot: boolean) => {
  cardInfo({
    card_id: cardId
  }).then((res: CardInfo) => {
    if (isHot) {
      data.hotCardInfo = res;
    } else {
      // 处理普通卡片点击
      const chatItem: ChatItem = {
        question: res.title,
        answer: res.content,
        answerType: 'card'
      };
      data.chatList.push(chatItem);
      scrollToBottom();
    }
  }).catch(err => {
    console.error('获取卡片详情失败', err);
  });
};
```

#### 3.2 发送问题
```typescript
// 发送问题
const sendQuestion = () => {
  if (!data.inputText.trim() || data.loading) {
    return;
  }

  const question = data.inputText.trim();
  data.inputText = '';
  data.loading = true;

  // 创建聊天项
  const chatItem: ChatItem = {
    question,
    answer: '',
    like: undefined
  };
  data.chatList.push(chatItem);
  scrollToBottom();

  // 调用问答API
  askQuestion({
    question
  }).then(res => {
    // 更新回答
    chatItem.answer = res.answer;
    chatItem.answerType = res.answer_type;
    chatItem.messageId = res.message_id;

    // 根据回答类型处理显示逻辑
    if (res.answer_type === 'unk') {
      chatItem.noShowLike = true;
      // 未知回答，提供工单入口
      chatItem.answer += `<div class="ticket-entry">${i18n.t('create_ticket')}</div>`;
    } else if (['azure', 'openai', 'doubao'].includes(res.answer_type)) {
      // AI回答，添加免责声明
      chatItem.answer += `<div class="disclaimer">${i18n.t('ai_disclaimer')}</div>`;
    }

    scrollToBottom();
  }).catch(err => {
    console.error('发送问题失败', err);
    chatItem.answer = i18n.t('answer_error');
    chatItem.noShowLike = true;
  }).finally(() => {
    data.loading = false;
  });
};
```

#### 3.3 处理评价
```typescript
// 处理点赞/点踩
const handleLike = (item: ChatItem, isLike: boolean) => {
  // 如果已经评价过，不再处理
  if (item.like !== undefined && item.like === isLike) {
    return;
  }

  // 更新评价状态
  item.like = isLike;

  // 调用评价API
  feedback({
    message_id: item.messageId,
    is_like: isLike ? 1 : 0
  }).catch(err => {
    console.error('评价失败', err);
  });
};

// 选择不满意原因
const selectDislikeReason = (item: ChatItem, reason: { id: number, text: string }) => {
  item.selectReason = reason.text;

  // 调用不满意原因API
  dislikeFeedback({
    message_id: item.messageId,
    reason_id: reason.id
  }).then(res => {
    // 如果返回了工单分类，提供工单入口
    if (res.ticket_categories && res.ticket_categories.length > 0) {
      // 跳转到工单页面
      router.push({
        path: '/tickets',
        query: {
          dislike_tickets: res.ticket_categories.join(',')
        }
      });
    }
  }).catch(err => {
    console.error('提交不满意原因失败', err);
  });
};
```

### 4. API接口
```typescript
// 获取热门卡片
const hotCard = (params: any) => {
  return request({
    url: '/backend/v1/egress/card/hot',
    method: 'get',
    params
  });
};

// 获取卡片详情
const cardInfo = (params: { card_id: number }) => {
  return request({
    url: '/backend/v1/egress/card/info',
    method: 'get',
    params
  });
};

// 发送问题
const askQuestion = (params: { question: string }) => {
  return request({
    url: '/backend/v1/egress/chat/ask',
    method: 'post',
    data: params
  });
};

// 提交评价
const feedback = (params: { message_id: string, is_like: number }) => {
  return request({
    url: '/backend/v1/egress/feedback',
    method: 'post',
    data: params
  });
};

// 提交不满意原因
const dislikeFeedback = (params: { message_id: string, reason_id: number }) => {
  return request({
    url: '/backend/v1/egress/feedback/dislike',
    method: 'post',
    data: params
  });
};
```

## 特殊场景处理

### 1. 回答类型处理
根据不同的回答类型，展示不同的内容和交互：

```typescript
// answer_type说明如下:
// azure: 采用azure的GPT回答
// openai: 采用openai的GPT回答
// embedding: faq直接回答
// vague_question: 模糊术语回答
// unk: 不知道术语回答
// words_library: 匹配词库回答

// 处理回答展示
const processAnswer = (item: ChatItem) => {
  switch (item.answerType) {
    case 'azure':
    case 'openai':
    case 'doubao':
      // AI回答，添加免责声明
      return `${item.answer}<div class="disclaimer">${i18n.t('ai_disclaimer')}</div>`;
    case 'embedding':
    case 'words_library':
      // 直接回答，不需要特殊处理
      return item.answer;
    case 'vague_question':
      // 模糊问题，不显示评价
      item.noShowLike = true;
      return item.answer;
    case 'unk':
      // 未知回答，提供工单入口
      item.noShowLike = true;
      return `${item.answer}<div class="ticket-entry">${i18n.t('create_ticket')}</div>`;
    default:
      return item.answer;
  }
};
```

### 2. iOS兼容处理
针对iOS设备，特别是iOS 12，进行特殊处理：

```typescript
// H5兼容ios12 监听所有input blur
const handleIOSCompatibility = () => {
  const inputsElement = [...document.getElementsByTagName('input')];
  inputsElement.forEach(element => {
    if (element) {
      element.onblur = () => {
        setTimeout(() => {
          const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
          window.scrollTo(0, Math.max(scrollHeight - 1, 0));
        }, 300);
      };
    }
  });
};
```

### 3. 自动跳转工单
当用户多次获取未知回答时，自动提示跳转到工单页面：

```typescript
// 未知回答计数
let unMatchCount = 0;

// 处理未知回答
const handleUnknownAnswer = (chatItem: ChatItem) => {
  unMatchCount++;

  // 连续3次未知回答，提示跳转工单
  if (unMatchCount >= 3) {
    chatItem.answer += `
      <div class="ticket-suggestion">
        ${i18n.t('multiple_unknown_answers')}
        <div class="ticket-btn" onclick="goToTicket()">${i18n.t('go_to_ticket')}</div>
      </div>
    `;

    // 重置计数
    unMatchCount = 0;
  }
};

// 跳转工单页面
const goToTicket = () => {
  router.push('/tickets');
};
```

## 开发建议

1. **性能优化**
   - 实现聊天记录的分页加载，避免长对话导致的性能问题
   - 优化图片和富文本内容的加载，使用懒加载技术
   - 减少不必要的API调用，合理使用缓存

2. **用户体验提升**
   - 添加输入提示和自动补全功能
   - 优化移动端键盘弹出时的页面布局
   - 实现打字机效果，提升回答的阅读体验

3. **功能扩展**
   - 支持语音输入和语音播报
   - 添加历史会话管理
   - 实现更丰富的消息类型，如图片、视频等

4. **兼容性处理**
   - 针对不同设备和浏览器进行适配
   - 处理网络波动情况下的用户体验
   - 优化在弱网环境下的加载速度
