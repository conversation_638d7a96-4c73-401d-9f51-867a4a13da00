# 智能客服页面分析

## 业务流程

### 1. 核心流程
```mermaid
graph TD
    A[用户进入] --> B[展示欢迎语]
    B --> C[等待用户输入]
    C --> D[发送问题]
    D --> E[AI分析回答]
    E --> F{匹配类型}
    F -->|精准匹配| G[展示答案]
    F -->|模糊匹配| H[展示推荐]
    F -->|未匹配| I[工单入口]
    G --> J[评价反馈]
    H --> C
    I --> K[提交工单]
```

### 2. 关键节点
1. 用户输入处理
   - 底部输入框（BottomWrap组件）
   - 热门问题快捷选择
   - 自助查询入口

2. AI回答处理
   - 流式输出（打字机效果）
   - 不同类型回答（文本/卡片/自助查询）
   - 推荐问题展示

3. 评价处理
   - 点赞/点踩
   - 不满意原因收集
   - 问题未解决反馈

## 代码实现

### 1. 页面结构
```vue
<template>
  <div class="bot-page">
    <div class="main">
      <div class="main-role"></div>
      <div class="container article-bg">
        <!-- 导航栏 -->
        <div class="nav-box">
          <div class="nav-item" @click="goToHome"><auto-font-size :text="$t('tab_home')"></auto-font-size></div>
          <div class="nav-item active-nav"><auto-font-size :text="$t('tab_ai_new')"></auto-font-size></div>
        </div>

        <!-- 聊天内容区 -->
        <div class="wrap">
          <div class="scroll-box" ref="scrollBox" @touchstart="handleTouchStart">
            <!-- 欢迎语 -->
            <ChatItem :isWelcome="true" :finish="true" :chat-item="{ answer: $t('sim_txt_first'), type: 'gpt' }"></ChatItem>

            <!-- 聊天历史 -->
            <ChatItem
              v-for="(item, index) in chatHistory"
              :key="`${index}_${item.id}`"
              :chat-item="item"
              :finish="true"
              :avatar="chatState.avatar"
              @recommClick="(_, type) => sendGptQuestion(_, type)"
              @dislikeOptList="dislikeOptList"
              @problemUnres="problemUnres">
            </ChatItem>

            <!-- 当前对话 -->
            <ChatItem
              v-if="chatState.crtChat.question"
              :chat-item="chatState.crtChat"
              :finish="chatState.isFinish"
              :show-content="chatState.showContent"
              :avatar="chatState.avatar">
            </ChatItem>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区 -->
    <BottomWrap
      @submit="(_, type) => sendGptQuestion(_, type || 'chat_custom')"
      @hotWordClick="_ => sendGptQuestion(_, 'chat_keyword')">
    </BottomWrap>
  </div>
</template>
```

### 2. 数据结构
```typescript
// 聊天消息类型
interface TChatItem {
  type: 'gpt' | 'article';  // 消息类型：gpt对话或文章
  question?: string;        // 问题内容
  answer: string;           // 回答内容
  like?: undefined | boolean; // 点赞状态
  sourceType?: string;      // 来源类型
  id?: string;              // 消息ID
  answerMode?: string;      // 回答模式
  from?: string;            // 回答来源
  guideMark?: boolean;      // 是否显示引导语标记
  guideContent?: string;    // 引导语内容
  noShowLike?: boolean;     // 是否不显示点赞
  disclaimer?: boolean;     // 是否显示免责声明
  problemUnresolved?: boolean; // 问题是否未解决
  recomQuestion?: string[]; // 推荐问题列表
  showEval?: string;        // 评价显示类型
  catList?: any;            // 工单分类列表
  selectReason?: string;    // 选择的不满意原因
}

// 页面状态
const defaultChatState = {
  crtChat: {} as TChatItem,  // 当前对话
  showContent: '',           // 显示内容（用于打字机效果）
  isFinish: false,           // 是否回答完成
  timer: null as any,        // 定时器
  canScroll: true,           // 是否可以自动滚动
  avatar: '',                // 头像
  isShowBottomBtn: false     // 是否显示底部按钮
}
```

### 3. 核心方法

#### 3.1 发送GPT问题
```typescript
const sendGptQuestion = async (q: string, sourceType = '') => {
  if (loading.value) return
  chatState.canScroll = true

  // 如果当前有未完成的回答，先完成它
  if (!loading.value) {
    if (!chatState.isFinish && chatState.showContent.length > 0) {
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      pushHistory(crtChatHistory.value)
      crtChatHistory.value = {} as any
      chatState.crtChat = { ...defaultChatState.crtChat }
      chatState.showContent = ''
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => smoothToBottom(), 300)
    }
  }

  // 创建新的对话
  chatState.crtChat = {
    type: 'gpt',
    question: q,
    answer: '',
    like: undefined,
    sourceType: sourceType,
    id: 'null',
    answerMode: '',
    guideMark: false,
    guideContent: '',
    noShowLike: false,
    disclaimer: false,
    problemUnresolved: false,
    recomQuestion: []
  }

  await nextTick()
  smoothToBottom('auto')

  chatState.isFinish = false
  loading.value = true

  try {
    // 创建请求中断控制器
    const abortController = new AbortController()
    controller.value = abortController

    // 准备请求参数
    const _S = 'Uc9Ud64Uf6Uc517354797809Uc27Ue6Ud69Uc70Ub053UaUd3Uc03608UdUa522Ub7160174'
    const apiKey = 'UcUd0893Uf053UaUd3Uc03608UdUa522Ub71601744UdUa9UfUf4855226U'
    const WXTimestamp = new Date().getTime()
    const params = {
      query: q,
      json_data: JSON.stringify(json_data),
      // ... 其他参数
    }

    const sign = crypto(apiKey, JSON.stringify(params), _S)

    // 显示引导语
    const randomGuides = 'guides0'
    const guides = `${$t(randomGuides)}\n`
    chatState.crtChat.answer = guides
    chatState.crtChat.guideContent = guides
    chatState.crtChat.guideMark = true

    // 引导语打字机效果
    const guidesTimer = setInterval(() => {
      if (chatState.showContent === guides) {
        clearInterval(guidesTimer)
        chatState.crtChat.guideMark = false
        return
      }
      chatState.showContent = guides.substring(0, chatState.showContent.length + Math.ceil(Math.random() * 3 + 1))
    }, 300)

    // 发送请求
    const response = await fetch('/backend/v3/elfin/chatstream', {
      method: 'POST',
      headers: {
        sign,
        'api-key': apiKey,
        lang: userInfo.lang,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params),
      signal: abortController.signal
    })

    // 获取消息ID
    const Mid = response.headers.get('Message_id') ? response.headers.get('Message_id') : (new Date().getTime()).toString()

    // 处理评价按钮显示逻辑
    if (response.headers.get('Answer_type') === 'vague_question' ||
        response.headers.get('Need_appraise') === '1' ||
        response.headers.get('Answer_rich_show_eval') === '2') {
      chatState.crtChat.noShowLike = true
    }

    // 记录评价显示类型
    chatState.crtChat.showEval = response.headers.get('Answer_rich_show_eval')

    // 处理免责声明显示逻辑
    if (response.headers.get('Answer_type') === 'openai' ||
        response.headers.get('Answer_type') === 'azure' ||
        response.headers.get('Answer_type') === 'doubao') {
      chatState.crtChat.disclaimer = true
    }

    // 处理点踩原因列表
    if (response.headers.get('Answer_rich_show_cat') !== '[]') {
      answerRichcatList = response.headers.get('Answer_rich_show_cat')
    } else answerRichcatList = []

    // 处理未知回答
    if (response.headers.get('Answer_type') === 'unk') {
      chatState.crtChat.noShowLike = true
      if (response.headers.get('if_game_question') === '1') unMatchCount++

      const responseJson = {
        code: 0,
        message: 'Answer',
        data: {
          output_content: unMatchCount === 2 ? $t('text_unmatch') : $t('text_unknown_script'),
          from: 'unk',
          message_id: Mid
        }
      }

      parseJsonResponse(responseJson, 'unk', 'if_game_question')
      if (unMatchCount === 2) unMatchCount = 0
      return
    }

    // 处理推荐问题
    if (response.headers.get('Rec_questions')) {
      const questionsTemp = (decodeURIComponent(response.headers.get('Rec_questions')).slice(1, -1)).replace(/\+/g, ' ')
      chatState.crtChat.recomQuestion = questionsTemp.split('|||')
    }

    // 处理自助查询卡片
    if (response.headers.get('Answer_card_id')) {
      try {
        const res = await getCards({
          card_id: Number(response.headers.get('Answer_card_id'))
        })
        const responseJson = {
          code: 0,
          message: 'Answer',
          data: {
            output_content: JSON.stringify(res),
            from: 'selfService',
            message_id: Mid
          }
        }
        parseJsonResponse(responseJson, 'selfService', 'selfService')
        return
      } catch (error) {
        console.error(error)
      }
    }

    // 处理历史数据查询
    if (response.headers.get('Answer_query_url_desc')) {
      // ... 历史数据查询逻辑
    }

    // 处理请求失败
    const successMark = response.headers.get('Success_mark')
    if (successMark === '0') {
      // ... 处理失败逻辑
      return
    }

    // 根据返回类型处理响应
    const contentType = response.headers.get('Content-Type')
    if (contentType.includes('application/json')) {
      const responseJson = await response.json()
      parseJsonResponse(responseJson, q, sourceType)
    } else if (contentType.includes('text/event-stream')) {
      parseStreamResponse(response, q, sourceType)
    }
  } catch (error) {
    // 处理异常
    resetChatState()
    crtChatHistory.value = {} as any
    chatState.isFinish = true
    isScrolling.value = false
    loading.value = false
    chatState.timer && clearInterval(chatState.timer)
    chatState.timer = null
    setTimeout(() => {
      chatState.canScroll = true
      smoothToBottom()
    }, 300)
    Toast($t('network_err'))
  }
}
```

#### 3.2 处理JSON响应
```typescript
const parseJsonResponse = ({ code, data }: { code: number, message: string, data: any }, q: string, sourceType: string) => {
  if (code !== 0) {
    crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
    resetChatState()
    pushHistory(crtChatHistory.value)
    crtChatHistory.value = {} as any
    chatState.isFinish = true
    isScrolling.value = false
    return
  }

  const res = data

  // 记录日志
  setLog({
    event: 'elfin_ask_ans',
    action: sourceType,
    query: q,
    result: 'success',
    answer: res.output_content,
    is_replied: 1,
    replied_from: res.from,
    message_id: res.message_id,
    isShowLike: chatState.crtChat.noShowLike ? 1 : 0,
    isDisclaimer: chatState.crtChat.disclaimer ? 1 : 0
  })

  // 更新对话内容
  chatState.crtChat.answer += res.output_content
  chatState.crtChat.id = res.message_id
  chatState.crtChat.answerMode = res.from
  chatState.crtChat.from = res.from
  chatState.crtChat.catList = answerRichcatList
  crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))

  // 设置打字机效果
  chatState.timer = setInterval(() => {
    // 若返回结果早于引导语展示，则等待引导语展示完毕
    if (chatState.crtChat.guideMark) return

    // 返回答案以后去除引导语
    if (chatState.showContent.indexOf(chatState.crtChat.guideContent as string) > -1) {
      chatState.crtChat.answer = chatState.crtChat.answer.replace(chatState.crtChat.guideContent as string, '')
      chatState.showContent = chatState.showContent.replace(chatState.crtChat.guideContent as string, '')
      crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
    }

    // 检查是否显示完毕
    if (chatState.showContent === chatState.crtChat.answer) {
      loading.value = false
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      pushHistory(crtChatHistory.value)
      crtChatHistory.value = {} as any
      resetChatState()
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => {
        chatState.canScroll = true
        smoothToBottom()
      }, 300)
      return
    }

    // 更新显示内容
    if (sourceType === 'selfService' || sourceType === 'Dislike' || sourceType === 'problemUnresolved') {
      // 特殊类型直接全部显示
      chatState.showContent = chatState.crtChat.answer
    } else {
      // 普通类型逐字显示
      chatState.showContent = chatState.crtChat.answer.substring(0, chatState.showContent.length + Math.ceil(Math.random() * 3 + 1))
    }

    smoothToBottom()
  }, answerTimeStep)
}
```

#### 3.3 处理流式响应
```typescript
const parseStreamResponse = async (response: any, q: string, sourceType: string) => {
  // 获取各个字段
  chatState.crtChat.id = response.headers.get('Message_id')
  chatState.crtChat.answerMode = response.headers.get('From')
  chatState.crtChat.from = response.headers.get('From')
  chatState.crtChat.answer = ''

  const data = response.body
  // 创建一个可读流，来读取字节流
  const reader = data.getReader()
  // 创建一个解码器，用于将字节流转换为字符串
  const decoder = new TextDecoder('utf-8')
  // 判断是否已经读取完毕
  let done = false

  chatState.timer = setInterval(async () => {
    // 若返回结果早于引导语展示，则等待引导语展示完毕
    if(chatState.crtChat.guideMark) return

    // 返回答案以后去除引导语
    if (chatState.showContent.indexOf(String(chatState.crtChat.guideContent)) > -1) {
      chatState.showContent = chatState.showContent.replace(String(chatState.crtChat.guideContent), '')
    }

    // 读取下一块数据
    try {
      const { value, done: readerDone } = await reader.read()
      if (value && !readerDone) {
        // 将字节流转换为字符串
        const char = decoder.decode(value)
        if (char === '\n' && chatState.showContent.endsWith('\n')) return
        if (char) {
          chatState.showContent += char
          chatState.crtChat.answer += char
        }
        smoothToBottom()
      }
      done = readerDone
    } catch (error) {
      done = true
    }

    // 处理完成
    if (done && chatState.crtChat.answer) {
      // 处理特殊回答类型
      if (chatState.crtChat.answer.indexOf('UNK') !== -1) {
        chatState.crtChat.noShowLike = true
        chatState.crtChat.disclaimer = false

        // 有推荐内容
        if (chatState.crtChat.recomQuestion?.length) {
          chatState.showContent = chatState.crtChat.answer.replace('UNK', $t('text_sorry_suggest'))
          chatState.crtChat.problemUnresolved = true
        } else {
          chatState.showContent = chatState.crtChat.answer.replace('UNK', $t('text_manual_handle')+`<span style="color:#F5C133;text-decoration:underline;">${$t('text_submit_cstickets')}</span>`)
        }
      }

      // 处理词库回答
      if ((response.headers.get('Answer_type') === 'words_library' || response.headers.get('Answer_type') === 'answer_ticket') && response.headers.get('Cat_id') !== '0') {
        const catIdFlag = response.headers.get('Cat_id')
        const catNameFlag: boolean = response.headers.get('Answer_type') === 'answer_ticket'
        chatState.showContent += `<span style="color:#F5C133;text-decoration:underline;" catId="${catIdFlag}">${$t(catNameFlag ? 'auto_flow_tip_text' : 'text_submit_cstickets')}</span>`
      }

      // 完成回答
      loading.value = false
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      chatState.crtChat.answer = chatState.showContent
      crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
      pushHistory(crtChatHistory.value)

      // 记录日志
      setLog({
        event: 'elfin_ask_ans',
        action: sourceType,
        query: q,
        result: 'success',
        answer: chatState.crtChat.answer,
        is_replied: 1,
        replied_from: chatState.crtChat.answerMode,
        message_id: chatState.crtChat.id,
        isShowLike: chatState.crtChat.noShowLike ? 1 : 0,
        isDisclaimer: chatState.crtChat.disclaimer ? 1 : 0,
        isRecommend: chatState.crtChat.recomQuestion?.length ? 1 : 0,
        recommendContent: chatState.crtChat.recomQuestion
      })

      // 重置状态
      crtChatHistory.value = {} as any
      resetChatState()
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => {
        chatState.canScroll = true
        smoothToBottom()
      }, 300)
    }
  }, answerTimeStep)
}
```

#### 3.4 评价处理
```typescript
// 处理点踩反馈
const dislikeOptList = async (need: boolean, reason: string, mid: string = (new Date().getTime()).toString()) => {
  try {
    let contentObj = {}
    if (need) {
      contentObj = await dislikeOpt({})
    }
    const responseJson = {
      code: 0,
      message: 'Dislike',
      data: {
        output_content: JSON.stringify(contentObj),
        from: 'Dislike',
        message_id: mid
      }
    }
    parseJsonResponse(responseJson, 'Dislike', 'Dislike')
    chatState.crtChat.selectReason = reason
    return
  } catch (error) {
    console.error(error)
  }
}

// 处理问题未解决
const problemUnres = () => {
  const responseJson = {
    code: 0,
    message: 'Answer',
    data: {
      output_content: $t('text_unmatch'),
      from: 'problemUnresolved',
      message_id: (new Date().getTime()).toString()
    }
  }
  parseJsonResponse(responseJson, 'problemUnresolved', 'problemUnresolved')
  chatState.crtChat.type = 'gpt'
  chatState.crtChat.noShowLike = true
}
```

### 4. 滚动处理
```typescript
// 平滑滚动到底部
const smoothToBottom = useThrottleFn((behavior: ScrollBehavior = 'smooth') => {
  if (scrollBox.value && chatState.canScroll) {
    scrollToFn(scrollBox.value.scrollHeight + 50, behavior)
  }
}, 50, false, true)

// 处理触摸开始事件
const isScrolling = ref<boolean>(false)
const handleTouchStart = () => {
  isScrolling.value = true
}

// 监听滚动方向
watch(() => directions.top, (v) => {
  if (v && !chatState.isFinish && isScrolling.value) chatState.canScroll = false
})

// 监听是否滚动到底部
watch(() => arrivedState.bottom, (v) => {
  v && (chatState.canScroll = true) && (isScrolling.value = false)
})

// 监听滚动位置，控制底部按钮显示
watch(y, () => {
  if (scrollBox.value && y.value < scrollBox.value.scrollHeight - (scrollBox.value.offsetHeight * 2))
    chatState.isShowBottomBtn = true
  else
    chatState.isShowBottomBtn = false
})
```

### 5. 生命周期处理
```typescript
onMounted(() => {
  chatHistory.value = []
})

onActivated(() => {
  canUseScrollTo.value = !!scrollBox.value?.scrollTo
  initChat()
  window.history.replaceState({
    path: window.location.origin + window.location.pathname
  }, '', window.location.origin + window.location.pathname)
})

onDeactivated(() => {
  clearTimeout(timer)
})

onUnmounted(() => {
  clearTimeout(timer)
})
```

## 特殊功能实现

### 1. 不同类型回答处理

#### 1.1 未知回答(UNK)
当AI无法回答问题时，会返回UNK类型的回答：
- 不显示点赞点踩按钮
- 如果有推荐问题，显示"抱歉，我不太理解您的问题，您可以尝试以下问题"
- 如果没有推荐问题，显示"您的问题需要人工处理"并提供提交工单入口
- 连续两次未回答上，给出提工单的兜底话术

#### 1.2 词库回答
当回答来自预设词库时：
- 可配置是否显示点赞点踩按钮
- 如果词库关联了工单分类，会在回答末尾添加"提交工单"入口
- 支持自动化流程触发

#### 1.3 AI回答(OpenAI/Azure/豆包)
当回答来自AI模型时：
- 显示免责声明
- 提供纠错按钮
- 支持流式输出

#### 1.4 自助查询
当用户查询需要展示卡片信息时：
- 通过getCards接口获取卡片信息
- 直接展示完整内容，不使用打字机效果
- 支持三级查询历史数据

### 2. 流式输出实现
流式输出使用了两种不同的实现方式：

#### 2.1 模拟打字机效果
对于非流式返回的内容，使用定时器模拟打字机效果：
```typescript
chatState.timer = setInterval(() => {
  // 逐字显示内容
  chatState.showContent = chatState.crtChat.answer.substring(
    0,
    chatState.showContent.length + Math.ceil(Math.random() * 3 + 1)
  )
}, answerTimeStep)
```

#### 2.2 真实流式输出
对于流式返回的内容，使用ReadableStream API实时处理：
```typescript
const reader = data.getReader()
const decoder = new TextDecoder('utf-8')

while (true) {
  const { value, done } = await reader.read()
  if (done) break

  const char = decoder.decode(value)
  chatState.showContent += char
  chatState.crtChat.answer += char
}
```

### 3. 评价反馈系统
评价系统包含多种反馈机制：

#### 3.1 点赞/点踩
- 可通过后台配置是否显示点赞点踩按钮
- 点踩后可展示原因列表供用户选择

#### 3.2 问题未解决
- 用户可点击"问题未解决"按钮
- 系统会展示兜底话术并提供工单入口

#### 3.3 工单提交
- 多个入口可触发工单提交
- 支持关联自动化流程

## 注意事项

### 1. 状态管理
- 使用 chatState 管理当前对话状态
- 使用 chatHistory 存储历史消息
- 使用 crtChatHistory 临时存储当前对话

### 2. 性能优化
- 使用 useThrottleFn 节流滚动函数
- 监听滚动状态控制自动滚动行为
- 定时清理历史消息避免内存占用过大

### 3. 异常处理
- 使用 AbortController 支持请求中断
- 网络错误时显示友好提示
- 流式输出中断时优雅降级

### 4. 开发建议
- 使用 TypeScript 做好类型定义
- 抽离复杂逻辑为独立函数
- 使用响应式API优化渲染性能
