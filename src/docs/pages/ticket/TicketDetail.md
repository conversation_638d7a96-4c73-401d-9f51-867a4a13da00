# 工单详情页面分析

## 业务流程

### 1. 核心流程
```mermaid
graph TD
    A[进入页面] --> B[加载工单信息]
    B --> C[展示工单详情]
    C --> D[等待操作]
    D --> E{操作类型}
    E -->|回复| F[发送消息]
    E -->|补充| G[上传资料]
    E -->|评价| H[提交评价]
    E -->|关闭| I[结束工单]
    F --> D
    G --> D
    H --> I
```

### 2. 状态流转
- 待处理 -> 处理中 -> 待确认 -> 已完成
- 可以从任意状态 -> 已关闭
- 已关闭可以 -> 重新打开

## 代码实现

### 1. 页面结构
```vue
<template>
  <div class="ticket-detail">
    <!-- 工单信息 -->
    <ticket-header
      :ticket="ticketInfo"
      @operation="handleOperation"
    />

    <!-- 消息记录 -->
    <message-list
      :messages="messageList"
      :loading="loading"
      @load-more="loadMoreMessages"
    />

    <!-- 操作区域 -->
    <ticket-actions
      v-if="canOperate"
      :status="ticketInfo.status"
      @reply="handleReply"
      @supplement="handleSupplement"
      @close="handleClose"
    />
  </div>
</template>
```

### 2. 数据结构
```typescript
// 工单信息
interface TicketInfo {
  id: string;
  title: string;
  content: string;
  status: TicketStatus;
  priority: number;
  createTime: number;
  updateTime: number;
  category: string;
  attachments: Attachment[];
}

// 消息记录
interface TicketMessage {
  id: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'system';
  sender: {
    id: string;
    name: string;
    role: 'user' | 'staff' | 'system';
  };
  createTime: number;
  attachments?: Attachment[];
}

// 工单状态
enum TicketStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  WAITING = 'waiting',
  COMPLETED = 'completed',
  CLOSED = 'closed'
}
```

### 3. 核心方法
```typescript
// 加载工单信息
async function loadTicketDetail() {
  try {
    const { ticketId } = route.params;
    const response = await getTicketDetail({ ticketId });

    ticketInfo.value = response.data;
    messageList.value = response.messages;

    // 建立消息推送连接
    setupMessagePush(ticketId);
  } catch (error) {
    handleError(error);
  }
}

// 发送消息
async function handleReply(content: string, attachments?: File[]) {
  // 1. 上传附件（如果有）
  let uploadedFiles = [];
  if (attachments?.length) {
    uploadedFiles = await uploadFiles(attachments);
  }

  // 2. 发送消息
  try {
    await sendTicketMessage({
      ticketId: ticketInfo.value.id,
      content,
      attachments: uploadedFiles
    });

    // 3. 更新消息列表
    await loadLatestMessages();
  } catch (error) {
    handleError(error);
  }
}

// 补充信息
async function handleSupplement(data: SupplementData) {
  try {
    await supplementTicket({
      ticketId: ticketInfo.value.id,
      ...data
    });

    // 刷新工单信息
    await loadTicketDetail();
  } catch (error) {
    handleError(error);
  }
}

// 关闭工单
async function handleClose(reason: string) {
  try {
    await closeTicket({
      ticketId: ticketInfo.value.id,
      reason
    });

    // 更新状态
    ticketInfo.value.status = TicketStatus.CLOSED;
  } catch (error) {
    handleError(error);
  }
}
```

### 4. API接口
```typescript
const api = {
  // 获取工单详情
  getTicketDetail: (params: {
    ticketId: string;
  }) => get(`/backend/v1/egress/ticket/detail`, params),

  // 发送消息
  sendTicketMessage: (params: {
    ticketId: string;
    content: string;
    attachments?: string[];
  }) => post('/backend/v1/egress/ticket/communicate', params),

  // 补充信息
  supplementTicket: (params: {
    ticketId: string;
    content: string;
    attachments?: string[];
  }) => post('/backend/v1/egress/ticket/replenish', params),

  // 关闭工单
  closeTicket: (params: {
    ticketId: string;
    reason: string;
  }) => post('/backend/v1/egress/ticket/close', params)
};
```

### 5. 消息推送
```typescript
// WebSocket 连接
function setupMessagePush(ticketId: string) {
  const ws = new WebSocket(WS_URL);

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.ticketId === ticketId) {
      handleNewMessage(data);
    }
  };

  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    // 尝试重连
    setTimeout(() => setupMessagePush(ticketId), 3000);
  };

  return () => ws.close();
}

// 处理新消息
function handleNewMessage(message: TicketMessage) {
  messageList.value.push(message);
  scrollToBottom();
}
```

## 关键点说明

### 1. 状态管理
- 工单状态实时更新
- 消息列表分页加载
- 附件上传进度
- 操作权限控制

### 2. 异常处理
- 网络错误重试
- 文件上传失败
- 并发操作控制
- 会话超时处理

### 3. 开发建议
- 注意消息时序
- 控制附件大小
- 处理长连接异常
- 注意性能优化

### 4. 调试方法
- 查看WebSocket连接
- 监控文件上传
- 检查状态变更
- 测试各类消息类型
