二级页面（SecondLevel.vue）功能逻辑分析
1. 页面概述
二级页面是首页卡片点击后的下一级页面，用于展示更详细的分类或子卡片。
2. 页面结构
标题区域：显示从首页传递过来的卡片标题
内容区域：根据后端返回的数据，以卡片模式或列表模式展示子项
3. 数据来源
通过 /backend/v3/elfin/temp/sub_card 接口获取二级卡片数据
接收首页传递的参数：temp_id（模板ID）、card_idx（卡片索引）和title（标题）
4. 交互逻辑
根据卡片类型有不同的跳转逻辑：
知识卡片（card_group 为 2）：跳转到文章详情页（ArticleDetail.vue）
表单分类卡片（card_group 为 3）：跳转到答案详情页（AnswersDetail.vue）
文章详情页（ArticleDetail.vue）功能逻辑分析
1. 页面概述
文章详情页用于展示知识库中的文章内容，提供用户自助解决问题的参考资料。
2. 页面结构
标题区域：显示文章标题
内容区域：显示文章正文（支持富文本）
底部区域：
如果文章关联了工单分类，显示"提交客服工单"按钮
否则显示点赞/点踩评价组件
3. 数据来源
通过 /backend/v3/elfin/article 接口获取文章详情
接收参数：art_id（文章ID）或 id（根据来源不同使用不同参数）
4. 交互逻辑
点击"提交客服工单"：跳转到答案详情页（AnswersDetail.vue）创建工单
点击点赞/点踩：记录用户对文章的评价，并发送日志
答案详情页（AnswersDetail.vue）功能逻辑分析
1. 页面概述
答案详情页是工单创建的入口，根据用户选择的问题分类，展示相应的表单或自动化流程。
2. 页面结构
动态组件结构，根据后端返回的数据类型渲染不同的组件：
工单表单组件（TicketForm）：用于填写工单信息
工单列表组件（TicketList）：用于选择更具体的问题分类
自动化流程组件：根据module_group渲染不同的自动化流程组件
3. 数据来源
通过 /backend/v1/egress/scene/cats 接口获取问题分类
通过 /backend/v1/egress/cat/info 接口获取流程和工单模板
通过 /backend/v1/egress/process/next 接口获取自动化流程的下一步
4. 交互逻辑
用户选择问题分类后，系统会根据配置展示工单表单或继续展示子分类
如果配置了自动化流程，用户需要按照流程步骤操作
表单提交成功后，跳转到工单完成页面
工单详情页（TicketDetail.vue）功能逻辑分析
1. 页面概述
工单详情页用于查看已提交工单的详细信息，包括表单内容、对话记录和客服回复。
2. 页面结构
使用折叠面板（van-collapse）组织内容：
表单信息区：显示工单提交的表单内容
对话信息区：显示用户与客服的对话记录
客服回复区：显示客服的回复内容
3. 数据来源
通过 /backend/v1/egress/ticket/detail 接口获取工单详情
4. 交互逻辑
用户可以查看工单状态和处理进度
支持查看图片和视频附件
可能包含工单评价或补充信息的入口
历史记录页（History.vue）功能逻辑分析
1. 页面概述
历史记录页用于查看用户之前提交的所有工单记录。
2. 页面结构
表格形式展示工单列表，包含以下列：
创建时间
工单ID
问题分类
处理进度
详情查看按钮
3. 数据来源
通过 /backend/v1/egress/ticket/mine 接口获取历史工单列表
4. 交互逻辑
点击"查看"按钮：跳转到工单详情页
未读工单会显示红点提示
空列表时显示"暂无记录"提示
工单评价页（TicketAppraise.vue）功能逻辑分析
1. 页面概述
工单评价页用于收集用户对已完成工单的满意度评价。
2. 页面结构
表单形式，包含以下评价项：
服务评分（5分制）
推荐度评分（5分制）
其他说明（文本输入）
提交按钮
3. 数据来源
接收工单ID参数
通过 /backend/v1/egress/ticket/appraise 接口提交评价
4. 交互逻辑
用户填写评价后点击提交
提交成功后显示提示，可以返回首页
记录用户评价行为的日志
工单补充页（TicketComplete.vue）功能逻辑分析
1. 页面概述
工单补充页用于向已提交的工单添加补充信息。
2. 页面结构
表单形式，包含以下补充项：
文字补充（文本输入）
图片补充（图片上传）
视频补充（视频上传）
提交按钮
3. 数据来源
接收工单ID参数
通过 /backend/v1/egress/ticket/replenish 接口提交补充信息
4. 交互逻辑
用户可以添加文字、图片或视频补充信息
支持PC端和移动端不同的上传方式
提交成功后可能返回工单详情页或历史记录页
页面间的关系和流程
整个客服系统的页面流程可以概括为：
首页（HomePage.vue）：系统入口，提供智能客服和工单系统的入口
智能客服流程：
首页 → 智能客服页面（NewSmart.vue）：进行AI对话
首页 → 二级页面（SecondLevel.vue）→ 文章详情页（ArticleDetail.vue）：查看知识库文章
工单系统流程：
首页 → 二级页面 → 答案详情页（AnswersDetail.vue）：创建工单
首页 → 历史记录页（History.vue）→ 工单详情页（TicketDetail.vue）：查看历史工单
工单详情页 → 工单评价页（TicketAppraise.vue）：评价已完成工单
工单详情页 → 工单补充页（TicketComplete.vue）：补充工单信息
反馈流程：
智能客服页面：对AI回答进行评价
文章详情页：对知识库文章进行评价
工单评价页：对客服服务进行评价
总结
客服系统采用了模块化的设计，将智能客服、知识库和工单系统有机结合，为用户提供多层次的问题解决方案：
自助解决：通过知识库文章和智能问答系统，用户可以自助查找解决方案
引导式解决：通过自动化流程，引导用户提供更多信息或尝试特定解决方案
人工解决：当自助和引导无法解决问题时，用户可以创建工单，由客服人员处理
系统的页面逻辑清晰，交互流程顺畅，同时通过日志记录用户行为，为系统优化提供数据支持。整体架构既满足了用户快速解决问题的需求，又兼顾了客服系统的效率和可扩展性。
