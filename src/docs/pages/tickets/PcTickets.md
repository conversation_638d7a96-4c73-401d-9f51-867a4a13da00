# PC端工单页面分析

## 业务流程

### 1. 核心流程
```mermaid
graph TD
    A[用户进入] --> B[加载问题分类]
    B --> C[展示分类树]
    C --> D[用户选择分类]
    D --> E{是否最终分类}
    E -->|否| F[展示子分类]
    F --> D
    E -->|是| G[展示工单表单]
    G --> H[用户填写信息]
    H --> I[提交工单]
    I --> J[展示提交结果]
```

### 2. 关键节点
1. 问题分类树处理
   - PC端树状结构展示
   - 分类搜索功能
   - 分类快速导航

2. 工单表单处理
   - 宽屏布局优化
   - 富文本编辑器支持
   - 多文件批量上传

3. 提交处理
   - 表单验证增强
   - 提交状态可视化
   - 历史工单查看

## 代码实现

### 1. 页面结构
```vue
<template>
  <div class="pc-tickets-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">{{ $t('ticket_submit_title') }}</div>
      <div class="header-actions">
        <button class="action-button" @click="viewHistory">
          {{ $t('view_history_tickets') }}
        </button>
      </div>
    </div>

    <div class="page-content">
      <!-- 左侧分类树 -->
      <div class="category-tree">
        <div class="tree-search">
          <input
            type="text"
            v-model="searchKeyword"
            :placeholder="$t('search_category')"
            @input="searchCategories"
          />
        </div>

        <div class="tree-content">
          <category-tree
            :categories="categories"
            :selected-id="selectedCategoryId"
            @select="selectCategory"
          />
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-container">
        <div v-if="!selectedCategory" class="no-selection">
          {{ $t('please_select_category') }}
        </div>

        <pc-form
          v-else
          :form-fields="formFields"
          :category="selectedCategory"
          @submit="submitTicket"
          @cancel="resetSelection"
        />
      </div>
    </div>

    <!-- 加载状态 -->
    <loading-overlay v-if="loading" />
  </div>
</template>
```

### 2. 数据结构
```typescript
// 分类树节点
interface CategoryNode {
  id: number;
  name: string;
  children?: CategoryNode[];
  parent_id?: number;
  level: number;
}

// 表单字段
interface FormField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'file' | 'richtext';
  required: boolean;
  options?: Array<{
    label: string;
    value: string | number;
  }>;
  placeholder?: string;
  maxLength?: number;
  description?: string;
}

// 页面状态
const state = reactive({
  loading: false,
  categories: [] as CategoryNode[],
  flatCategories: {} as Record<number, CategoryNode>,
  selectedCategoryId: null as number | null,
  selectedCategory: null as CategoryNode | null,
  formFields: [] as FormField[],
  searchKeyword: '',
  searchResults: [] as CategoryNode[]
});
```

### 3. 核心方法

#### 3.1 加载分类树
```typescript
// 加载分类树
const loadCategories = async () => {
  state.loading = true;

  try {
    // 获取所有分类
    const response = await getCategoriesApi();

    // 构建树状结构
    state.categories = buildCategoryTree(response.data);

    // 构建扁平结构用于快速查找
    state.flatCategories = flattenCategories(state.categories);
  } catch (error) {
    console.error('Failed to load categories:', error);
    showNotification({
      type: 'error',
      message: i18n.t('failed_to_load_categories')
    });
  } finally {
    state.loading = false;
  }
};

// 构建树状结构
const buildCategoryTree = (categories: any[]): CategoryNode[] => {
  const nodes: Record<number, CategoryNode> = {};
  const roots: CategoryNode[] = [];

  // 创建节点映射
  categories.forEach(category => {
    nodes[category.id] = {
      ...category,
      children: [],
      level: 0
    };
  });

  // 构建树
  categories.forEach(category => {
    if (category.parent_id) {
      const parent = nodes[category.parent_id];
      if (parent) {
        nodes[category.id].level = parent.level + 1;
        parent.children = parent.children || [];
        parent.children.push(nodes[category.id]);
      } else {
        roots.push(nodes[category.id]);
      }
    } else {
      roots.push(nodes[category.id]);
    }
  });

  return roots;
};

// 扁平化分类树用于搜索
const flattenCategories = (categories: CategoryNode[]): Record<number, CategoryNode> => {
  const result: Record<number, CategoryNode> = {};

  const traverse = (nodes: CategoryNode[]) => {
    nodes.forEach(node => {
      result[node.id] = node;
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };

  traverse(categories);
  return result;
};
```

#### 3.2 选择分类
```typescript
// 选择分类
const selectCategory = (categoryId: number) => {
  state.selectedCategoryId = categoryId;
  state.selectedCategory = state.flatCategories[categoryId];

  // 如果是叶子节点，加载表单字段
  if (!state.selectedCategory.children || state.selectedCategory.children.length === 0) {
    loadFormFields(categoryId);
  }
};

// 加载表单字段
const loadFormFields = async (categoryId: number) => {
  state.loading = true;

  try {
    const response = await getFormFieldsApi({ category_id: categoryId });

    // 处理表单字段，增强PC端体验
    state.formFields = response.fields.map(field => {
      // 文本域在PC端转为富文本编辑器
      if (field.type === 'textarea' && field.rich_text_enabled) {
        return {
          ...field,
          type: 'richtext'
        };
      }

      return field;
    });
  } catch (error) {
    console.error('Failed to load form fields:', error);
    showNotification({
      type: 'error',
      message: i18n.t('failed_to_load_form')
    });
  } finally {
    state.loading = false;
  }
};
```

#### 3.3 搜索分类
```typescript
// 搜索分类
const searchCategories = debounce(() => {
  if (!state.searchKeyword.trim()) {
    state.searchResults = [];
    return;
  }

  const keyword = state.searchKeyword.toLowerCase();
  const results: CategoryNode[] = [];

  // 在扁平化的分类中搜索
  Object.values(state.flatCategories).forEach(category => {
    if (category.name.toLowerCase().includes(keyword)) {
      results.push(category);
    }
  });

  state.searchResults = results;
}, 300);

// 从搜索结果中选择分类
const selectFromSearch = (categoryId: number) => {
  selectCategory(categoryId);

  // 清空搜索
  state.searchKeyword = '';
  state.searchResults = [];

  // 展开到选中的分类
  expandToCategory(categoryId);
};

// 展开到指定分类
const expandToCategory = (categoryId: number) => {
  const category = state.flatCategories[categoryId];
  if (!category) return;

  // 获取所有父分类
  const parentIds: number[] = [];
  let currentId = category.parent_id;

  while (currentId) {
    parentIds.push(currentId);
    const parent = state.flatCategories[currentId];
    currentId = parent?.parent_id;
  }

  // 展开所有父分类
  parentIds.reverse().forEach(id => {
    // 通知树组件展开节点
    expandNode(id);
  });
};
```

#### 3.4 提交工单
```typescript
// 提交工单
const submitTicket = async (formData: any) => {
  // 表单验证
  if (!validateForm(formData)) {
    return;
  }

  state.loading = true;

  try {
    // 处理富文本内容
    if (formData.content && formData.content_type === 'richtext') {
      // 将HTML转换为纯文本和HTML两种格式
      formData.content_html = formData.content;
      formData.content = stripHtml(formData.content);
    }

    // 处理附件上传
    if (formData.attachments && formData.attachments.length > 0) {
      const uploadedFiles = await uploadAttachments(formData.attachments);
      formData.file_ids = uploadedFiles.map(file => file.id);
      delete formData.attachments;
    }

    // 添加分类ID
    formData.category_id = state.selectedCategoryId;

    // 提交工单
    const result = await createTicketApi(formData);

    // 显示成功提示
    showNotification({
      type: 'success',
      message: i18n.t('ticket_submit_success'),
      duration: 5000
    });

    // 跳转到工单详情
    router.push({
      path: '/pc/tickets/detail',
      query: { id: result.ticket_id }
    });
  } catch (error) {
    console.error('Failed to submit ticket:', error);
    showNotification({
      type: 'error',
      message: i18n.t('ticket_submit_failed')
    });
  } finally {
    state.loading = false;
  }
};

// 表单验证
const validateForm = (formData: any): boolean => {
  let isValid = true;
  const errors: Record<string, string> = {};

  // 验证必填字段
  state.formFields.forEach(field => {
    if (field.required) {
      if (field.type === 'file') {
        if (!formData.attachments || formData.attachments.length === 0) {
          errors[field.id] = i18n.t('field_required', { field: field.name });
          isValid = false;
        }
      } else if (!formData[field.id]) {
        errors[field.id] = i18n.t('field_required', { field: field.name });
        isValid = false;
      }
    }
  });

  // 显示错误信息
  if (!isValid) {
    showFormErrors(errors);
  }

  return isValid;
};

// 上传附件
const uploadAttachments = async (files: File[]) => {
  // 创建上传进度跟踪
  const uploadProgress = reactive(
    files.reduce((acc, file, index) => {
      acc[index] = 0;
      return acc;
    }, {} as Record<number, number>)
  );

  // 显示上传进度对话框
  showUploadProgress(uploadProgress);

  // 并行上传所有文件
  const uploadPromises = files.map((file, index) => {
    const formData = new FormData();
    formData.append('file', file);

    return uploadFileWithProgress(formData, (progress) => {
      uploadProgress[index] = progress;
    });
  });

  try {
    const results = await Promise.all(uploadPromises);
    hideUploadProgress();
    return results;
  } catch (error) {
    hideUploadProgress();
    throw error;
  }
};
```

### 4. API接口
```typescript
// 获取分类
const getCategoriesApi = () => {
  return request({
    url: '/backend/v1/egress/ticket/categories',
    method: 'get'
  });
};

// 获取表单字段
const getFormFieldsApi = (params: { category_id: number }) => {
  return request({
    url: '/backend/v1/egress/ticket/form',
    method: 'get',
    params
  });
};

// 创建工单
const createTicketApi = (data: any) => {
  return request({
    url: '/backend/v1/egress/ticket/create',
    method: 'post',
    data
  });
};

// 上传文件带进度
const uploadFileWithProgress = (formData: FormData, onProgress: (progress: number) => void) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response.data);
        } catch (e) {
          reject(new Error('Invalid response format'));
        }
      } else {
        reject(new Error(`Upload failed with status ${xhr.status}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error'));
    });

    xhr.open('POST', '/backend/v1/egress/upload');

    // 添加请求头
    const headers = getAuthHeaders();
    Object.keys(headers).forEach(key => {
      xhr.setRequestHeader(key, headers[key]);
    });

    xhr.send(formData);
  });
};
```

## PC端特有功能

### 1. 富文本编辑器
PC端工单表单支持富文本编辑器，提供更丰富的文本编辑功能：

```vue
<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <button @click="formatText('bold')" title="加粗">B</button>
      <button @click="formatText('italic')" title="斜体">I</button>
      <button @click="formatText('underline')" title="下划线">U</button>
      <button @click="insertList('ordered')" title="有序列表">1.</button>
      <button @click="insertList('unordered')" title="无序列表">•</button>
      <button @click="insertImage" title="插入图片">
        <img src="@/assets/img/image-icon.svg" alt="图片" />
      </button>
    </div>

    <div
      class="editor-content"
      contenteditable="true"
      ref="editorContent"
      @input="updateContent"
      @paste="handlePaste"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const editorContent = ref(null);
const emit = defineEmits(['update:modelValue']);

// 格式化文本
const formatText = (command) => {
  document.execCommand(command, false, null);
  editorContent.value.focus();
  updateContent();
};

// 插入列表
const insertList = (type) => {
  const command = type === 'ordered' ? 'insertOrderedList' : 'insertUnorderedList';
  document.execCommand(command, false, null);
  editorContent.value.focus();
  updateContent();
};

// 插入图片
const insertImage = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';

  input.onchange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // 上传图片
        const result = await uploadImage(file);

        // 插入图片
        document.execCommand('insertHTML', false, `<img src="${result.url}" alt="${file.name}" />`);
        updateContent();
      } catch (error) {
        console.error('Failed to upload image:', error);
      }
    }
  };

  input.click();
};

// 更新内容
const updateContent = () => {
  emit('update:modelValue', editorContent.value.innerHTML);
};

// 处理粘贴事件
const handlePaste = (e) => {
  e.preventDefault();

  // 获取纯文本
  const text = e.clipboardData.getData('text/plain');

  // 插入纯文本
  document.execCommand('insertText', false, text);
};
</script>
```

### 2. 分类树组件
PC端使用树状结构展示分类，支持展开/折叠和搜索：

```vue
<template>
  <div class="category-tree-component">
    <div
      v-for="category in categories"
      :key="category.id"
      class="tree-node"
    >
      <div
        class="node-content"
        :class="{ 'selected': selectedId === category.id }"
        @click="handleNodeClick(category)"
      >
        <span
          v-if="category.children && category.children.length > 0"
          class="expand-icon"
          :class="{ 'expanded': expandedNodes.includes(category.id) }"
          @click.stop="toggleNode(category.id)"
        >
          <i class="icon-arrow"></i>
        </span>

        <span class="node-name">{{ category.name }}</span>
      </div>

      <div
        v-if="category.children && category.children.length > 0"
        class="node-children"
        v-show="expandedNodes.includes(category.id)"
      >
        <category-tree
          :categories="category.children"
          :selected-id="selectedId"
          :expanded-nodes="expandedNodes"
          @select="id => $emit('select', id)"
          @toggle="id => $emit('toggle', id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedId: {
    type: Number,
    default: null
  },
  expandedNodes: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['select', 'toggle']);

// 处理节点点击
const handleNodeClick = (category) => {
  emit('select', category.id);

  // 如果有子节点，自动展开
  if (category.children && category.children.length > 0) {
    toggleNode(category.id);
  }
};

// 切换节点展开状态
const toggleNode = (id) => {
  emit('toggle', id);
};
</script>
```

### 3. 历史工单查看
PC端提供历史工单查看功能，方便用户跟踪工单状态：

```vue
<template>
  <div class="history-tickets">
    <div class="history-header">
      <h2>{{ $t('history_tickets') }}</h2>
      <div class="filter-options">
        <select v-model="filter.status">
          <option value="">{{ $t('all_status') }}</option>
          <option value="pending">{{ $t('status_pending') }}</option>
          <option value="processing">{{ $t('status_processing') }}</option>
          <option value="completed">{{ $t('status_completed') }}</option>
          <option value="closed">{{ $t('status_closed') }}</option>
        </select>

        <div class="date-range">
          <date-picker v-model="filter.startDate" :placeholder="$t('start_date')" />
          <span>-</span>
          <date-picker v-model="filter.endDate" :placeholder="$t('end_date')" />
        </div>

        <button class="search-btn" @click="loadTickets">{{ $t('search') }}</button>
      </div>
    </div>

    <div class="ticket-list">
      <table>
        <thead>
          <tr>
            <th>{{ $t('ticket_id') }}</th>
            <th>{{ $t('ticket_title') }}</th>
            <th>{{ $t('ticket_category') }}</th>
            <th>{{ $t('ticket_status') }}</th>
            <th>{{ $t('create_time') }}</th>
            <th>{{ $t('last_update') }}</th>
            <th>{{ $t('actions') }}</th>
          </tr>
        </thead>

        <tbody>
          <tr v-for="ticket in tickets" :key="ticket.id">
            <td>{{ ticket.id }}</td>
            <td class="ticket-title">{{ ticket.title }}</td>
            <td>{{ ticket.category_name }}</td>
            <td>
              <status-badge :status="ticket.status" />
            </td>
            <td>{{ formatDate(ticket.create_time) }}</td>
            <td>{{ formatDate(ticket.update_time) }}</td>
            <td>
              <button class="view-btn" @click="viewTicket(ticket.id)">
                {{ $t('view_detail') }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="tickets.length === 0" class="empty-state">
        {{ $t('no_tickets_found') }}
      </div>

      <pagination
        v-if="total > pageSize"
        :total="total"
        :page="page"
        :page-size="pageSize"
        @change="changePage"
      />
    </div>
  </div>
</template>
```

## 开发建议

1. **PC端布局优化**
   - 利用宽屏空间，采用左侧分类树+右侧表单的布局
   - 考虑使用响应式设计，适应不同屏幕尺寸
   - 优化键盘操作，提供快捷键支持

2. **富文本编辑增强**
   - 集成专业的富文本编辑器，如TinyMCE或CKEditor
   - 支持图片拖拽上传和粘贴上传
   - 提供格式化工具和表格支持

3. **文件上传优化**
   - 实现拖拽上传区域
   - 支持大文件分片上传
   - 提供上传进度可视化

4. **用户体验提升**
   - 添加表单自动保存功能
   - 实现工单历史记录查询和筛选
   - 提供工单模板功能，快速创建常见工单
