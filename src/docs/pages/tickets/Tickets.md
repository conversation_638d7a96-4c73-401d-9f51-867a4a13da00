# 移动端工单页面分析

## 业务流程

### 1. 核心流程
```mermaid
graph TD
    A[用户进入] --> B[加载问题分类]
    B --> C[展示分类树]
    C --> D[用户选择分类]
    D --> E{是否最终分类}
    E -->|否| F[展示子分类]
    F --> D
    E -->|是| G[展示工单表单]
    G --> H[用户填写信息]
    H --> I[提交工单]
    I --> J[展示提交结果]
```

### 2. 关键节点
1. 问题分类树处理
   - 多级分类展示
   - 单分支自动展开
   - 分类搜索功能

2. 工单表单处理
   - 动态表单字段
   - 附件上传功能
   - 表单验证逻辑

3. 提交处理
   - 提交状态管理
   - 错误处理机制
   - 成功后引导

## 代码实现

### 1. 页面结构
```vue
<template>
  <div class="tickets-page">
    <!-- 分类选择区域 -->
    <div v-if="ticketDataList.length > 0" class="ticket-category">
      <div v-for="(item, index) in ticketDataList" :key="index" class="category-level">
        <!-- 分类标题 -->
        <div class="level-title">{{ $t('ticket_select_problem_type') }}</div>

        <!-- 分类列表 -->
        <div class="category-list">
          <div
            v-for="(cate, cIndex) in item.children"
            :key="cIndex"
            class="category-item"
            @click="selectCategory(cate, index)">
            <div class="item-name">{{ cate.name }}</div>
            <div class="item-icon">
              <img src="@/assets/img/arrow-right.png" alt="arrow">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工单表单区域 -->
    <div v-if="showForm" class="ticket-form">
      <form-component
        :form-fields="formFields"
        :category-id="selectedCategory.id"
        @submit="submitTicket"
        @cancel="backToCategories">
      </form-component>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <loading-spinner></loading-spinner>
    </div>
  </div>
</template>
```

### 2. 数据结构
```typescript
// 工单分类数据类型
interface ticketDataT {
  id: number;           // 分类ID
  name: string;         // 分类名称
  children?: ticketDataT[]; // 子分类
  index?: number;       // 索引
  level?: number;       // 层级
}

// 工单分类列表类型
type ticketDataListT = Array<ticketDataT>;

// 表单字段类型
interface FormField {
  id: string;           // 字段ID
  name: string;         // 字段名称
  type: string;         // 字段类型
  required: boolean;    // 是否必填
  options?: string[];   // 选项（用于下拉框等）
  placeholder?: string; // 占位文本
  maxLength?: number;   // 最大长度
}

// 页面数据
const data = reactive({
  loading: true,
  ticketDataList: [] as Array<{
    level: number;
    index: number;
    children: ticketDataListT;
  }>,
  selectedCategory: null as ticketDataT | null,
  showForm: false,
  formFields: [] as FormField[]
});
```

### 3. 核心方法

#### 3.1 获取分类树
```typescript
// 获取入口信息, 判断是否是智能客服点踩后跳转的工单需求
const params = route.query.dislike_tickets && route.query.dislike_tickets.length > 0 ? {
  fork_cat_ids: (route.query.dislike_tickets as Array<string>).map((item: string | number) => {
    return +item
  })
} : {}

// 获取入口树-数据来源是 客服工单系统-问题分类配置
sceneEntrance(params).then((res: ticketDataListT) => {
  data.loading = false

  // 前端处理数据，看返回数据是否只有一个分支，如果只有一个分支，则只要最后一层的对象
  const isOne = (list: Array<ticketDataT>): false | ticketDataT => {
    if (list.length === 1) {
      if (list[0].children) {
        return isOne(list[0].children)
      } else {
        return list[0]
      }
    } else {
      return false
    }
  }

  // 处理后数组则为树状层级筛选，如果为对象则直接渲染表单
  const rdata = isOne(res)
  if (rdata === false) {
    if (res.length > 0) {
      data.ticketDataList.push({
        level: 0,
        index: data.ticketDataList.length,
        children: res
      })
    }
  } else {
    const item = {
      ...rdata,
      index: data.ticketDataList.length
    }
    data.ticketDataList.push(item)
  }
})
```

#### 3.2 选择分类
```typescript
// 选择分类
const selectCategory = (category: ticketDataT, level: number) => {
  // 如果有子分类，展示子分类
  if (category.children && category.children.length > 0) {
    // 移除当前层级之后的所有层级
    data.ticketDataList = data.ticketDataList.slice(0, level + 1)

    // 添加新的层级
    data.ticketDataList.push({
      level: level + 1,
      index: data.ticketDataList.length,
      children: category.children
    })

    // 滚动到新层级
    nextTick(() => {
      const container = document.querySelector('.ticket-category')
      if (container) {
        container.scrollLeft = container.scrollWidth
      }
    })
  } else {
    // 如果是最终分类，显示表单
    data.selectedCategory = category
    data.showForm = true

    // 获取表单字段
    getFormFields(category.id)
  }
}

// 获取表单字段
const getFormFields = (categoryId: number) => {
  data.loading = true

  // 调用API获取表单字段
  getTicketForm({ category_id: categoryId })
    .then((res: { fields: FormField[] }) => {
      data.formFields = res.fields
    })
    .catch((err) => {
      console.error('获取表单字段失败', err)
    })
    .finally(() => {
      data.loading = false
    })
}
```

#### 3.3 提交工单
```typescript
// 提交工单
const submitTicket = async (formData: any) => {
  data.loading = true

  try {
    // 处理附件上传
    if (formData.attachments && formData.attachments.length > 0) {
      const uploadedFiles = await uploadAttachments(formData.attachments)
      formData.file_ids = uploadedFiles.map(file => file.id)
      delete formData.attachments
    }

    // 添加分类ID
    formData.category_id = data.selectedCategory?.id

    // 提交工单
    const result = await createTicket(formData)

    // 显示成功提示
    showToast({
      type: 'success',
      message: i18n.t('ticket_submit_success')
    })

    // 跳转到工单详情或列表
    router.push({
      path: '/tickets/detail',
      query: { id: result.ticket_id }
    })
  } catch (error) {
    // 显示错误提示
    showToast({
      type: 'error',
      message: i18n.t('ticket_submit_failed')
    })
    console.error('提交工单失败', error)
  } finally {
    data.loading = false
  }
}

// 上传附件
const uploadAttachments = async (files: File[]) => {
  const uploadPromises = files.map(file => {
    const formData = new FormData()
    formData.append('file', file)

    return uploadFile(formData)
  })

  return Promise.all(uploadPromises)
}
```

### 4. API接口
```typescript
// 获取问题分类
const sceneEntrance = (params: any) => {
  return request({
    url: '/backend/v1/egress/ticket/category',
    method: 'get',
    params
  })
}

// 获取表单字段
const getTicketForm = (params: { category_id: number }) => {
  return request({
    url: '/backend/v1/egress/ticket/form',
    method: 'get',
    params
  })
}

// 创建工单
const createTicket = (data: any) => {
  return request({
    url: '/backend/v1/egress/ticket/create',
    method: 'post',
    data
  })
}

// 上传文件
const uploadFile = (formData: FormData) => {
  return request({
    url: '/backend/v1/egress/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

## 特殊场景处理

### 1. 从智能客服跳转
当用户从智能客服点踩后跳转到工单页面时，会携带 `dislike_tickets` 参数，此时需要根据该参数获取特定的分类树：

```typescript
// 从智能客服跳转过来的情况
if (route.query.dislike_tickets) {
  // 记录来源
  sessionStorage.setItem('ticketSource', 'smartService')

  // 使用特定参数获取分类树
  const params = {
    fork_cat_ids: (route.query.dislike_tickets as Array<string>).map(item => +item)
  }

  // 获取分类树
  sceneEntrance(params).then(/* ... */)
}
```

### 2. 单分支自动展开
当分类树只有一个分支时，自动展开到最后一级：

```typescript
// 前端处理数据，看返回数据是否只有一个分支，如果只有一个分支，则只要最后一层的对象
const isOne = (list: Array<ticketDataT>): false | ticketDataT => {
  if (list.length === 1) {
    if (list[0].children) {
      return isOne(list[0].children)
    } else {
      return list[0]
    }
  } else {
    return false
  }
}
```

### 3. 表单验证
提交前进行表单验证，确保必填字段已填写：

```typescript
// 表单验证
const validateForm = (formData: any) => {
  for (const field of data.formFields) {
    if (field.required && !formData[field.id]) {
      showToast({
        type: 'warning',
        message: `${field.name}${i18n.t('is_required')}`
      })
      return false
    }
  }
  return true
}

// 提交前验证
const submitTicket = async (formData: any) => {
  if (!validateForm(formData)) {
    return
  }

  // 继续提交流程...
}
```

## 开发建议

1. **分类树优化**
   - 考虑缓存常用分类，减少加载时间
   - 实现分类搜索功能，方便用户快速找到需要的分类
   - 优化多级分类的展示，避免过多层级导致的用户体验问题

2. **表单处理**
   - 支持动态表单字段，根据不同分类显示不同字段
   - 优化附件上传体验，支持多文件上传和进度显示
   - 实现表单数据的本地保存，避免用户意外关闭页面导致数据丢失

3. **性能优化**
   - 实现分类树的懒加载，减少初始加载时间
   - 优化大附件上传，考虑分片上传和断点续传
   - 减少不必要的API调用，合理使用缓存

4. **用户体验**
   - 提供清晰的操作引导，特别是对于复杂的分类选择
   - 优化表单填写体验，提供智能提示和自动补全
   - 完善错误处理和提示，帮助用户解决问题
