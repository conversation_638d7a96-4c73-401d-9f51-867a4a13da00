import { App as Application } from 'vue'
import { createI18n } from 'vue-i18n'
import langConf from '@/assets/lang/index'

// 创建 i18n 实例
export const i18n = createI18n({
  legacy: false, // 取消注释，使用组合式 API
  globalInjection: true, // 添加全局注入
  locale: 'zh-cn', // 默认使用中文
  fallbackLocale: 'en',
  messages: langConf.config
})

// 导出 t 函数，供 Vuex 等非组件文件使用
export const t = (key: string) => i18n.global.t(key)

export default function setupI18n(app: Application): void {
  app.use(i18n)
}
