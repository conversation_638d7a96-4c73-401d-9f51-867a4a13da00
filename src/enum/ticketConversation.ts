// 添加角色枚举类型
export enum RoleType {
  user = 'user',
  system = 'system'
}

// 添加问题类型枚举类型
export enum QuestionKeyType {
  time = 'time', // 问题时间
  event = 'event', // 活动
  description = 'description', // 问题描述
  picture = 'picture', // 问题截图
  suggestion = 'suggestion' // 问题建议
}

// 添加问题类型key对应的语料
export const QuestionKeyLang = {
  // 请选择具体的问题发现时间
  [QuestionKeyType.time]: 'text_info_collection_time',
  // 您在哪个活动/功能里发现的这个问题？
  [QuestionKeyType.event]: 'text_info_collection_event',
  // 请详细描述一下您遇到的问题
  [QuestionKeyType.description]: 'text_info_collection_description',
  // 为了更好的解决您的问题，请提供相关截图
  [QuestionKeyType.picture]: 'text_info_collection_picture',
  // 请详细描述一下您要反馈的建议内容
  [QuestionKeyType.suggestion]: 'text_info_collection_suggestion'
}


// 消息列表
export interface MessageItem {
  role: string;
  content?: string;
  question_key?: string;
  timestamp?: number;
  uuid?: string;
  question_index?: number;
}

// 历史记录中的问题枚举列表
export interface QuestionGetListItem {
  question_key: string;
  question_content: string;
  answer: string;
  has_answer: boolean;
  question_ask_count: number;
  is_required?: boolean;
  field_extend?: string;
}

// 更新会话中的历史回答
export interface HistoryAnswerItem {
  role: string;
  question_key: string;
  content: string;
}

// 提交会话的总结答案
export interface NowAnswerContentItem {
  text: string;
}


