export type LIKE_TYPES = {
  like: 1,
  dislike: 2
}

export interface TUrlParams {
  gameid: number
  sid: number
  lang: string
  os: string
  pkgChannel: string
  sdk_version: number
  country_code: string
  app_version: string

  head_height: number
  device_id: string
  subchannel: string
  role_id: number
  uid: number
  fpid: number
  fpx_app_id: string
  account_id: string
  fp_uid: string
  game_token: string
  channel: string
  track_key: string
}

// 对话
export type TChatType = 'gpt' | 'article'
export type TChatItem = {
  showEval?: string
  catList?: string[]
  selectReason?: string
  type: TChatType
  question: string
  answer: string
  like?: LIKE_TYPES[keyof LIKE_TYPES]
  sourceType: string
  answerMode: string
  id?: number | string
  articleId?: number
  from?: string
  hit?: 0 | 1
  is_default?: number,
  guideMark?: boolean,
  guideContent?: string,
  noShowLike?: boolean,
  disclaimer?: boolean,
  problemUnresolved?: boolean,
  recomQuestion?: string[]
  [key: string]: any
}

export type TArticleDetail = {
  art_id: number
  lang: string
  art_group: string
  art_title: string
  art_content: string
  created_at: number
  updated_at: number
}
export interface TNewCard {
  art_id?: number,
  art_title?: string,
  card_group: number,
  child_card_list?: TNewCard[],
  image_title: string,
  image_url: string,
  ticket_cat_id?: number
}
export interface Tbanner {
  art_id?: number,
  image_title: string,
  image_url: string,
  jump_url?: string,
  image_group: number
}

export interface TArticle {
  art_id: number,
  art_label: number,
  art_title: string
}
