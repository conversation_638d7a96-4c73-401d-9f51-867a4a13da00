// MarkdownRenderer.ts
import MarkdownIt from 'markdown-it';

export default (content: string | (() => string)) => {
  // console.log('createMarkdownRenderer content', content)
  const md = MarkdownIt({
    html: true,
    breaks: true,
  })

  if (typeof content === 'function') {
    return md.render(content());
  } else if (typeof content === 'string') {
    return md.render(content);
  }

  return '';
}
