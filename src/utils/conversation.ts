/**
 * 会话工具函数
 */

/**
 * 格式化日期为字符串
 * @param date 日期对象
 * @param onlyDate 是否只返回日期格式
 * @returns 格式化后的日期字符串，格式为 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
 */
export const formatDate = (date: Date, onlyDate: boolean = false): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  if (onlyDate) {
    return `${year}-${month}-${day}`
  }

  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 滚动到底部
 * @param element 需要滚动的元素
 */
export const scrollToBottom = (element: HTMLElement | null): void => {
  if (element) {
    element.scrollTop = element.scrollHeight
  }
}

/**
 * 检查会话是否完成
 * @param questionList 问题列表
 * @returns 是否所有问题都已回答
 */
export const isConversationCompleted = (questionList: Array<{ has_answer: boolean }>): boolean => {
  return questionList.every(item => item.has_answer)
}

/**
 * 获取下一个未回答的问题
 * @param questionList 问题列表
 * @param currentQuestionKey 当前问题的 key
 * @returns 下一个未回答的问题，如果没有则返回 null
 */
export const getNextUnansweredQuestion = (
  questionList: Array<{ question_key: string; has_answer: boolean }>,
  currentQuestionKey: string
) => {
  return (
    questionList.find(
      item => item.question_key !== currentQuestionKey && !item.has_answer
    ) || null
  )
}

/**
 * 创建会话完成消息内容
 * @param catId 分类 ID
 * @param conversationId 会话 ID
 * @param ticketId 工单 ID
 * @returns 会话完成消息内容
 */
export const createFinishMessageContent = (
  catId: number | string,
  conversationId: string,
  ticketId: string
): string => {
  return `${catId}_${conversationId}_${ticketId}_finish`
}

/**
 * 检查会话是否已提交工单
 * @param lastMessage 最后一条消息
 * @param finishMessageContent 完成消息内容
 * @returns 是否已提交工单
 */
export const isTicketSubmitted = (
  lastMessage: { content?: string } | undefined,
  finishMessageContent: string
): boolean => {
  return !!(lastMessage?.content && lastMessage.content === finishMessageContent)
}
