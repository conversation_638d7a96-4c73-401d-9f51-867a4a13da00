/**
 * UBT 用户行为追踪类型声明文件
 */

declare class UBT {
  constructor(env: string);
  init(): this;
  updateOptions(options: {
    agent?: Record<string, any>;
    geo?: Record<string, any>;
  }): void;
  report(type: string, data: any): Promise<any>;
  error(): void;
  pageView(): void;
  setUser(info: Record<string, any>): void;
  eventReport(key: string, data: any): Promise<any>;
  setUser(info: Record<string, any>): void;
}

export default UBT;
