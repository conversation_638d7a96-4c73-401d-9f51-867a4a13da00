function sendErrorToFeishu(code: string, errorMsg: string): void {
  const app_addr_url = window.location.href;
  const initHomeUrl = localStorage.getItem('init_home_url') || ''
  const initHomeData = initHomeUrl && initHomeUrl != app_addr_url ? `\n**【首页地址】**：[${initHomeUrl}](${initHomeUrl})` : ''
  const msg = `\n**【访问地址】**：[${app_addr_url}](${app_addr_url}) \n**【告警信息】**：${errorMsg}${initHomeData}`;

  const webhookURL = `https://acs-go-test.funplus.com/lua?p1=8&p2=297&appId=aa32f494-737d-42c0-9e53-cc27f9f5e562&code=${code}&msg=${encodeURIComponent(msg)}`;

  try {
    // 使用 Fetch API 发送 POST 请求到飞书机器人 Webhook
    fetch(webhookURL, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    console.log('fetch webhookURL error', error)
  }
}

export const ERROR_CODES = {
  API_ERROR: '7001',       // 接口请求错误
  JS_ERROR: '7002',        // JavaScript运行时错误
  PROMISE_ERROR: '7003',   // Promise未捕获异常
  API_TIMEOUT: '7004',     // 接口请求超时
}

/**
 * 将请求配置转换为CURL命令格式
 * @param config Axios请求配置
 * @returns CURL命令字符串
 */
export function formatRequestAsCurl(config: any): string {
  try {
    if (!config) return 'curl 请求配置为空';

    // 基础URL
    const url = config.url || '';
    const baseUrl = config.baseURL || '';

    // 确保URL是完整的http(s)地址
    let fullUrl = '';
    if (url.startsWith('http')) {
      fullUrl = url;
    } else if (baseUrl) {
      fullUrl = baseUrl + (baseUrl.endsWith('/') || url.startsWith('/') ? '' : '/') + url;
    } else {
      // 如果没有baseURL，尝试使用当前域名
      const origin = window.location.origin;
      fullUrl = origin + (url.startsWith('/') ? '' : '/') + url;
    }

    if (!fullUrl) return 'curl 无效URL';

    // 添加URL参数
    if (config.params && Object.keys(config.params).length > 0) {
      const queryString = Object.entries(config.params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
        .join('&');

      // 将参数直接加到URL中
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
    }

    // 开始构建curl命令
    let curlCommand = `curl '${fullUrl}'`;

    // 添加headers - 每个头部单独一行，更易读
    if (config.headers) {
      Object.entries(config.headers).forEach(([key, value]) => {
        if (value) {
          curlCommand += ` \\\n  -H '${key}: ${value}'`;
        }
      });
    }

    // 添加请求体 - 使用--data-raw代替-d
    if (config.data) {
      // 如果是对象，转为JSON字符串
      const data = typeof config.data === 'object' ? JSON.stringify(config.data) : config.data;
      curlCommand += ` \\\n  --data-raw '${data}'`;
    }

    return curlCommand;
  } catch (error) {
    return `curl格式化错误: ${error}`;
  }
}

// 错误上报参数类型定义
export interface RequestErrorReportOptions {
  errorCode: string;       // 错误码
  errorType: string;       // 错误类型
  errorMessage: string;    // 错误消息
  requestConfig?: any;     // 请求配置
  errorObject?: any;       // 错误对象
  responseObject?: any;    // 响应对象
}

/**
 * 统一请求错误上报处理方法
 * @param options 错误上报参数对象
 */
export function reportRequestError(options: RequestErrorReportOptions): void {
  const {
    errorCode,
    errorType,
    errorMessage,
    requestConfig,
    errorObject,
    responseObject
  } = options;

  // 状态码信息
  let statusCode = '';
  // 使用errorCode作为主要的错误码
  if (errorCode && errorCode !== ERROR_CODES.API_ERROR && errorCode !== ERROR_CODES.API_TIMEOUT) {
    // 如果不是默认错误码，使用传入的errorCode
    statusCode = `[错误码 ${errorCode}] `;
  } else if (responseObject?.data?.code) {
    // 从响应数据中获取业务错误码
    statusCode = `[业务 ${responseObject.data.code}] `;
  } else if (errorObject?.businessCode) {
    // 从错误对象中获取业务错误码
    statusCode = `[业务 ${errorObject.businessCode}] `;
  } else if (responseObject?.status && responseObject.status !== 200) {
    // HTTP状态码
    statusCode = `[HTTP ${responseObject.status}] `;
  } else if (errorObject?.response?.status) {
    // 错误对象中的HTTP状态码
    statusCode = `[HTTP ${errorObject.response.status}] `;
  }

  // 构建CURL命令用于复现
  const curlCommand = requestConfig ? formatRequestAsCurl(requestConfig) : '无法构建CURL命令';

  // 构建简化的错误信息，只包含错误类型、状态码、错误消息和CURL命令
  const fullErrorMessage = `${errorType}: ${statusCode}${errorMessage}
**【CURL】**: \n\`${curlCommand}\`\n`;

  // 上报错误 - 使用传入的errorCode或默认值
  reportApiError(errorCode, fullErrorMessage);
}

// 在页面上捕获错误并调用发送函数
window.onerror = function(errorMsg: string | Event, url?: string, lineNumber?: number, column?: number, errorObj?: Error): boolean {
  // 如果errorMsg是Event对象，则无法处理
  if (typeof errorMsg !== 'string') {
    return false;
  }

  // console.log('window url', url)
  // console.log('window lineNumber', lineNumber)
  // console.log('window column', column)
  // console.log('window errorObj', errorObj)
  console.log('window errorMsg', errorMsg)
  if (errorMsg.includes(`SyntaxError: Document.querySelectorAll: \'div:has(> iframe[id=`)) {
    return true;
  }

  // 组合错误信息
  const fullErrorMsg = errorMsg + (url ? ` Script: ${url}` : '') +
                      (lineNumber ? ` Line: ${lineNumber}` : '') +
                      (column ? ` Column: ${column}` : '');

  // 判断errorMsg 是否包含 'Script error'，如果包含则为通用JavaScript错误
  if (errorMsg.includes('Script error')) {
    return false;
  }

  // 通用JavaScript错误
  sendErrorToFeishu(ERROR_CODES.JS_ERROR, fullErrorMsg);

  return false; // 允许错误冒泡
};

// 添加Promise未捕获异常监听
window.addEventListener('unhandledrejection', function(event: PromiseRejectionEvent) {
  let errorMsg = '未知Promise错误';

  try {
    const reason = event.reason;
    // 处理不同类型的错误对象
    if (reason && typeof reason === 'object') {
      if (reason.message) {
        // 标准Error对象
        errorMsg = reason.message;
        // 如果有堆栈信息，添加到错误消息中
        if (reason.stack) {
          errorMsg += `\n**【堆栈】**: ${reason.stack}`;
        }
      } else if (reason.statusText) {
        // 可能是HTTP错误
        errorMsg = `HTTP错误: ${reason.status || ''} ${reason.statusText}`;
      } else {
        // 尝试转换为JSON字符串
        try {
          errorMsg = `对象错误: ${JSON.stringify(reason)}`;
        } catch (e) {
          // 如果转换失败，至少记录对象的属性名
          errorMsg = `对象错误: ${Object.keys(reason).join(', ')}`;
        }
      }
    } else if (typeof reason === 'string') {
      // 字符串错误
      errorMsg = reason;
    } else {
      // 其他类型错误
      errorMsg = String(reason);
    }
  } catch (e) {
    // 处理提取错误信息过程中的异常
    errorMsg = `错误提取失败: ${e instanceof Error ? e.message : String(e)}`;
  }

  // 上报详细的错误信息
  sendErrorToFeishu(ERROR_CODES.PROMISE_ERROR, `Promise未捕获异常: ${errorMsg}`);
});

// 添加接口请求错误监听的辅助函数
export function reportApiError(code: string, errorMsg: string): void {
  sendErrorToFeishu(code, errorMsg);
}

export default sendErrorToFeishu
