/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2022-07-22 17:08:17
 * @Last Modified by: wenh<PERSON>.wang
 * @Last Modified time: 2022-09-30 16:57:33
 */

import hmacSHA256 from 'crypto-js/hmac-sha256'
import Base64 from 'crypto-js/enc-base64'
import FpAxios from '../server/interceptor'
import store from '@/store'

const i = '\ueb07\ue846\ue844\ueb0a\ueaff\ue860\ue864\ueb06\ue85c\ueb13\ue876\ueb02\ueafd\ue858\ue878\ueb1c'
const k = '\ueb06\ueb00\ue846\ue857\ueb04\ueaf7\ue83f\ue858\ue884\ue84f\ue86f\ue853\ue881\ue88b\ue86b\ue859\ue85d\ue881\ue868\ue8e5\ue876\ue863\ue8ec\ue865\ue8b0\ue896\ue87a\ue86a\ue85f\ue8bb\ue862\ue88c'

const d = (str: string) => {
  for (let PXzQN = 0, pxbZd = 0; PXzQN < str.length; PXzQN++) {
    pxbZd = str.charCodeAt(PXzQN)
    pxbZd -= PXzQN
    pxbZd ^= PXzQN
    pxbZd -= PXzQN
    pxbZd -= 0x5FB0
    pxbZd ^= 0xDD40
    pxbZd += 0xAA62
    pxbZd += PXzQN
    str = str.substr(0, PXzQN) + String.fromCharCode(pxbZd & 0xFFFF) + str.substr(PXzQN + 1)
  }
  return str
}

const encryp = (obj: Record<string, unknown>, key: string) => {
  const m = Object.keys(obj).sort().map(item => `${item}=${decodeURIComponent(obj[item] as string)}`).join('&')
  const hashDigest = hmacSHA256(m, key)
  const hmacDigest = Base64.stringify(hashDigest)
  return hmacDigest
}
// const getCurrentTimeUTC = () => {
//   var tmLoc = new Date()
//   return tmLoc.getTime() + tmLoc.getTimezoneOffset() * 60000
// }

export const uploadSever = (file: string | Array<string>, url: string): Promise<[]> => {
  const t = new Date().getTime()
  return new Promise((resolve, reject) => {
    const { userInfo } = store.state
    const params = {
      appId: d(i),
      storage: 1,
      clientId: 1,
      account_id: userInfo.fpid,
      fpx_app_id: userInfo.gameid,
      lang: userInfo.lang,
      channel: userInfo.channel,
      sdk_version: userInfo.sdk_version,
      os: userInfo.os,
      ts: t
    }
    const headers = {
      'Access-Control-Allow-Origin': '*',
      ...params,
      auth: encryp(params, d(k)),
      'Content-Type': 'multipart/form-data'
    }
    const formData = new FormData()

    // 支持多文件上传
    if (Array.isArray(file)) {
      if (!file.length) return
      file.forEach((f: any) => {
        formData.append('file[]', f)
      })
    } else {
      // 兼容原有单文件上传
      formData.append('file', file)
    }

    FpAxios.server({
      url,
      method: 'post',
      data: formData,
      headers
    }).then((res: unknown) => {
      resolve(res as [])
    }).catch(err => {
      reject(err)
    })
  })
}
