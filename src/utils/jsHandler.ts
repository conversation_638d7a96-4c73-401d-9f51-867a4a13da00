declare global {
  interface Window {
    webkit:
      | {
          messageHandlers: {
            callSdk: {
              postMessage: (message: string) => void;
            };
          };
        }
      | undefined;
    JsHandler?: {
      callSdk: (message: string) => void;
    };
    Native2JS: (data: string) => void;
    chooseFinish: (params: any) => void;
    backImgUrl: (data: string) => void;
  }
}

/*
 * @Description: JsHandler通信 调用原生方法
 * @Param: dataString: string // 原生方法名称
 * @E.g.: callSDK('is_webview') // 判断是否是webview
 */
const callSDK = (dataString: string) => {
  const u = navigator.userAgent;
  const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //判断是否是 android终端
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //判断是否是 iOS终端
  const isIpad = !!u.match(/(iPad)/) || (u.indexOf("Macintosh") > -1 && navigator.maxTouchPoints > 1);
  console.log("call sdk ===>", dataString);
  // console.log("isAndroid", isAndroid);
  // console.log("isIOS", isIOS);
  // console.log("isIpad", isIpad);
  // 检查是否支持 JsHandler 或 webkit.messageHandlers
  if (!window.JsHandler && !window.webkit?.messageHandlers?.callSdk) {
    console.warn("SDK methods not supported in the current environment");
    return false;
  }
  try {
    if (isAndroid) {
      window?.JsHandler?.callSdk(dataString);
    } else if (isIOS || isIpad) {
      window?.webkit?.messageHandlers.callSdk?.postMessage(dataString);
    } else {
      window?.JsHandler?.callSdk(dataString);
    }
    return true;
  } catch (error) {
    console.error("call sdk error", error);
    return false;
  }
};

/*
 * @Description: JSBridge通信 调用原生方法
 * @Param: path: string // 原生方法名称
 * @Param: params: object // 参数
 */
const jsBridge = (path: string, params: { [key: string]: any }) => {
  const SDKUrl = "http://kingsgroup.game/";
  let url = SDKUrl + path;
  if (params) {
    const paramArr = [] as string[];
    Object.keys(params).forEach((key) => {
      paramArr.push(`${key}=${params[key]}`);
    });
    url += `?${paramArr.join("&")}`;
  }
  // 调用 ubt 的 eventReport 方法
  window.ubt?.eventReport("USE_PAGE_REDIRECT3", { url });
  window.location.href = url;
};

/*
    统一Jshandler对象，用于调用原生方法，
    JsHandler.IS_WEBVIEW()  // 判断是否是webview
    JsHandler.UPLOAD_IMAGE()  // 上传图片
    JsHandler.SHARE()  // 分享
*/
interface JsHandler {
  IS_WEBVIEW: (callback?: (data: any) => void) => void;
  UPLOAD_IMAGE: (
    count: number,
    actionId: number,
    callbacks: {
      onUploadComplete?: (data: any) => void;
      onChooseFinish?: (data: any) => void;
    }
  ) => void;
  SHARE: (data: any) => void;
  CHANGE_CLOSE_MODE: (mode: string) => void;
  OPEN_SYSTEM_BROWSER: (url: string) => void;
}

const JsHandler: JsHandler = {
  IS_WEBVIEW: (callback) => {
    const res = callSDK("is_webview");
    if (!res) {
      return callback && callback(false);
    }
    window.Native2JS = (data: any) => {
      console.log("Native2JS", data);
      try {
        const transData = JSON.parse(data);
        if (transData.action === "is_webview") {
          const isWebview = transData.data?.is_webview == 1 ? true : false;
          callback && callback(isWebview);
        } else {
          callback && callback(false);
        }
      } catch (error) {
        console.log("window.Native2JS 解析错误", error);
      }
    };
  },
  UPLOAD_IMAGE: (count, actionId, callbacks) => {
    const _S = "4b0ce2a5f5b661049988eb69cf25c305e69ee1f7f22bc0ea4410dde0674cfb0f";
    const apiKey = "$2y$10$Lr1AFIk0Li2/pcyjMaMNn.JzvhfsgLVwd4Y4xSrKGVM2jSiZhYkAG";
    jsBridge("openFile", {
      count,
      actionId: actionId || Date.now(),
      apiKey: encodeURIComponent(apiKey),
      apiSecret: encodeURIComponent(_S)
    });
    /*
     * @Description: 选择完成通知
     * @param {*} 返回参数(data为string类型)
     * 成功：{"code":1,"count":3,"msg":""}
     * 失败：{"code":0,"msg":""}
     */
    window.chooseFinish = callbacks.onChooseFinish || function (data) {};

    /*
     * @Description: 上传完成通知
     * @param {*} 返回参数(data为string类型)
     * @param {*} 成功：{"result":1,"data":"[{\"type\":0,\"img_url\":\"\\\/fp-admin-center\\\/dev\\\/ss_global\\\/0a9ed0f1f005acb353e38b48ff86a418.png\"}]","actionId":"1631258204376"}
     * @param {*} 失败：{"result":0,"actionId":"1631257502890"}
     */

    window.backImgUrl = callbacks.onUploadComplete || function (data) {};
  },
  SHARE: (data) => {
    jsBridge("share", data);
  },
  /*
   * @Description: 修改关闭按钮展示或者颜色样式
   * @Param: mode: string
   * 是否展示关闭按钮 show | hide, 默认show, show为显示，hide为隐藏
   * 颜色模式  light | dark, 默认dark, light为浅色，dark为深色
   */
  CHANGE_CLOSE_MODE(mode) {
    jsBridge("changeCloseMode", { mode });
  },
  OPEN_SYSTEM_BROWSER: (url) => {
    jsBridge("fp_systemBrowser", { fp_url: encodeURIComponent(url) });
  }
};

export { JsHandler };
