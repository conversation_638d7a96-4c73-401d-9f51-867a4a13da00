import hmacSHA256 from 'crypto-js/hmac-sha256'
import Base64 from 'crypto-js/enc-base64'

export const crypto = (str1: string, str2: string, key: string): string => {
  const hashDigest = hmacSHA256(str1 + str2 + key, key)
  const hmacDigest = Base64.stringify(hashDigest)
  return hmacDigest
}

export const pcDetailCrypto = (obj: any, key: string): string => {
  const m = JSON.stringify(obj)
  const hashDigest = hmacSHA256(m, key)
  const hmacDigest = Base64.stringify(hashDigest)
  return hmacDigest
}
