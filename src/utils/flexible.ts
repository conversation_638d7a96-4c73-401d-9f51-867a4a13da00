/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2022-05-09 11:11:38
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-08-26 11:39:47
 */
import { Upgrade } from '@/utils'

// 直接使用Upgrade函数，它已经在utils/index.ts中实现了缓存
async function getMark() {
  const { mark } = await Upgrade()
  const params = new URLSearchParams(window.location.search)
  const gameName = params.get('game')
  console.log('gameName', gameName)
  const isPcTicketDetail = location.href.includes('pc/ticket-detail') && gameName
  if (mark || isPcTicketDetail) {
    (function flexible(window, document) {
      const docEl = document.documentElement
      const dpr = window.devicePixelRatio || 1
      let height = docEl.offsetHeight
      let width = docEl.offsetWidth
      const root = <HTMLElement>document.querySelector(':root')
      root.style.setProperty('--bg-height', height + 'px')
      root.style.setProperty('--bg-width', width + 'px')

      // function isMobile() {
      //   return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      // }

      // adjust body font size
      function setBodyFontSize() {
        if (document.body) {
          document.body.style.fontSize = (12 * dpr) + 'px'
        } else {
          document.addEventListener('DOMContentLoaded', setBodyFontSize)
        }
      }
      setBodyFontSize()

      function setRemUnit() {
        height = Math.max(docEl.offsetHeight, 320)
        width = Math.max(docEl.offsetWidth, 480)
        // height = docEl.offsetHeight
        // width = docEl.offsetWidth
        const rem = width / 10
        docEl.style.fontSize = rem + 'px'
        const ratio = width / height
        if (height > 10 && ratio > 1.8) {
          docEl.style.fontSize = (rem * (1.8 / ratio)) + 'px'
        }
      }
      setRemUnit()

      // reset rem unit on page resize
      window.addEventListener('resize', setRemUnit)
      window.addEventListener('pageshow', function (e) {
        if (e.persisted) {
          setRemUnit()
        }
      })

      // detect 0.5px supports
      if (dpr >= 2) {
        const fakeBody = document.createElement('body')
        const testElement = document.createElement('div')
        testElement.style.border = '.5px solid transparent'
        fakeBody.appendChild(testElement)
        docEl.appendChild(fakeBody)
        if (testElement.offsetHeight === 1) {
          // docEl.classList.add('hairlines')
        }
        docEl.removeChild(fakeBody)
      }

      // 计算最终html font-size
      function modifileRootRem() {
        const root = window.document.documentElement
        const fontSize = parseFloat(root.style.fontSize)
        const finalFontSize = parseFloat(window.getComputedStyle(root).getPropertyValue('font-size'))
        if (finalFontSize === fontSize) return
        root.style.fontSize = fontSize + (fontSize - finalFontSize) + 'px'
      }
      if (typeof window.onload === 'function') {
        const oldFun = window.onload
        window.onload = function (ev: Event) {
          oldFun.call(window, ev)
          modifileRootRem()
        }
      } else {
        window.onload = modifileRootRem
      }
    }(window, document))
  } else {
    (function (designWidth: number, maxWidth: number) {
      const doc = <Document>document
      const win = window
      const docEl = <HTMLElement>doc.documentElement
      const remStyle = <HTMLStyleElement>document.createElement('style')
      let tid: number | undefined

      function refreshRem() {
        let width = docEl.getBoundingClientRect().width
        maxWidth = maxWidth || 540
        width > maxWidth && (width = maxWidth)
        const rem = width * 100 / designWidth
        remStyle.innerHTML = 'html{font-size:' + rem + 'px !important}'
      }

      if (docEl.firstElementChild) {
        docEl.firstElementChild.appendChild(remStyle)
      } else {
        let wrap: HTMLDivElement | null = doc.createElement('div')
        wrap.appendChild(remStyle)
        doc.write(wrap.innerHTML)
        wrap = null
      }
      // 要等 wiewport 设置好后才能执行 refreshRem，不然 refreshRem 会执行2次
      refreshRem()

      win.addEventListener('resize', () => {
        clearTimeout(tid) // 防止执行两次
        tid = window.setTimeout(refreshRem, 300)
      }, false)

      win.addEventListener('pageshow', (e: PageTransitionEvent) => {
        if (e.persisted) { // 浏览器后退的时候重新计算
          clearTimeout(tid)
          tid = window.setTimeout(refreshRem, 300)
        }
      }, false)

      if (doc.readyState === 'complete') {
        doc.body.style.fontSize = '16px'
      } else {
        doc.addEventListener('DOMContentLoaded', () => {
          doc.body.style.fontSize = '16px'
        }, false)
      }
    })(1334, 1334)
  }
}
getMark()
