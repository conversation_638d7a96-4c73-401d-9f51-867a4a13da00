import { setLog } from '@/api'

// 唯一访客ID生成和存储
const generateVisitorId = function() {
  let cachedVid = "";

  return function() {
    // 如果已有缓存的VID，直接返回
    if (cachedVid) return cachedVid;

    // 尝试从localStorage获取
    let vid = window.localStorage.getItem("EVENT_VID");
    if (vid) {
      cachedVid = vid;
      return vid;
    }

    // 生成新的VID
    let d = new Date().getTime();
    if (window.performance && typeof window.performance.now === "function") {
      d += performance.now();
    }

    // 生成UUID格式的VID
    vid = "xxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      const r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == "x" ? r : (r & 0x3 | 0x8)).toString(16);
    });

    // 存储到localStorage
    window.localStorage.setItem("EVENT_VID", vid);
    return vid;
  };
}();

// 会话ID生成和存储
const generateSessionId = function() {
  let sessionCache = {};

  return function() {
    // 检查缓存的会话是否有效
    if (sessionCache.sid && sessionCache.time > Date.now()) {
      return sessionCache.sid;
    }

    // 尝试从sessionStorage获取
    const storedSession = window.sessionStorage.getItem("EVENT_SID");
    if (storedSession) {
      const sessionData = JSON.parse(storedSession);
      if (sessionData.time > Date.now()) {
        sessionCache = sessionData;
        return sessionCache.sid;
      }
    }

    // 生成新的会话ID，基于日期时间和随机数
    const generateTimestamp = function() {
      const now = new Date();
      let year = String(now.getFullYear());
      let month = String(now.getMonth() + 1);
      month = +month < 10 ? "0" + month : month;

      let day = String(now.getDate());
      day = +day < 10 ? "0" + day : day;

      return year + month + day + now.getHours() + now.getMinutes() + now.getSeconds();
    };

    sessionCache.sid = generateTimestamp() + Math.random().toString(36).substr(2);
    sessionCache.time = Date.now() + (30 * 60 * 1000); // 30分钟过期

    window.sessionStorage.setItem("EVENT_SID", JSON.stringify(sessionCache));
    return sessionCache.sid;
  };
}();

// 页面访问ID生成
const generatePageViewId = function() {
  function s4() {
    return (1 + Math.random() * 0x10000 | 0).toString(16).substring(1);
  }

  let cachedPvid;

  return function(forceNew = false) {
    if (cachedPvid && !forceNew) {
      return cachedPvid;
    }

    // 生成UUID格式的PVID
    cachedPvid = s4() + s4() + "-" + s4() + "-" + s4() + "-" + s4() + "-" + s4() + s4() + s4();
    return cachedPvid;
  };
}();

// 上下文信息
const contextInfo = {
  vid: generateVisitorId(),
  sid: generateSessionId(),
  pvid: generatePageViewId(),
  path: window.location.pathname,
  url: window.location.href,
  userAgent: window.navigator.userAgent
};

// 环境枚举
const Env = {
  dev: "dev",
  pre: "pre",
  pro: "pro"
};

// 事件类型枚举
const EventType = {
  PAGEVIEW: "PAGEVIEW",
  Performance: "PERFORMANCE",
  ACTION: "ACTION",
  CUSTOM: "CUSTOM",
  ERROR: "ERROR",
  EXPOSURE: "EXPOSURE",
  NETWORK: "NETWORK"
};

// 元数据
const metaInfo = {};

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器名称和版本
 */
function getBrowserInfo() {
  const ua = navigator.userAgent;
  const browserPatterns = [
    { name: 'Firefox', regex: /firefox|fxios/i, versionRegex: /(?:firefox|fxios)\/(\d+\.\d+)/i },
    { name: 'Edge', regex: /edg/i, versionRegex: /edg\/(\d+\.\d+)/i },
    { name: 'Chrome', regex: /chrome|crios|crmo/i, versionRegex: /(?:chrome|crios|crmo)\/(\d+\.\d+)/i },
    { name: 'Safari', regex: /safari/i, versionRegex: /version\/(\d+\.\d+)/i },
    { name: 'Internet Explorer', regex: /msie|trident/i, versionRegex: /(?:msie |rv:)(\d+\.\d+)/i }
  ];

  // 按优先级检查浏览器类型
  for (const browser of browserPatterns) {
    if (browser.regex.test(ua) && !(browser.name === 'Safari' && /chrome|crios|crmo/i.test(ua)) &&
        !(browser.name === 'Chrome' && /edg/i.test(ua))) {
      try {
        const match = ua.match(browser.versionRegex);
        return {
          name: browser.name,
          version: match ? match[1] : 'Unknown'
        };
      } catch (e) {
        console.warn(`解析浏览器版本出错:`, e);
      }
    }
  }

  return { name: 'Unknown', version: 'Unknown' };
}

// 设备信息
const browserInfo = getBrowserInfo();

// 设备信息
const deviceInfo = {
  screen: window.screen.width + "x" + window.screen.height,
  network: "other",
  browserName: browserInfo?.name || "Unknown",
  browserVer: browserInfo?.version || "Unknown"
};

// 地理位置信息
const geoInfo = {};

// 用户信息
const userInfo = {
  id: "",
  token: ""
};

// 设置用户信息
function setUserInfo(info) {
  if (info) {
    Object.assign(userInfo, info);
  }
}

// 事件属性枚举
const EventAttr = {
  network: "network",
  performance: "performance",
  action: "action",
  custom: "custom",
  error: "error"
};

// 页面视图类
class PageView {
  constructor() {
    this._pageview = {
      meta: metaInfo,
      context: contextInfo,
      agent: deviceInfo,
      geo: geoInfo,
      user: userInfo
    };
  }

  get pageview() {
    return this._pageview;
  }

  set pageview(value) {
    this._pageview = value;
  }

  updateOther(attrName, attrValue) {
    // 删除上一个属性
    if (this.lastAttr) {
      delete this._pageview[this.lastAttr];
    }

    this.lastAttr = attrName;

    // 添加新属性
    if (attrName) {
      this._pageview[attrName] = attrValue;
    }
  }
}

// 上报事件数据
function reportEvent(type, data) {
  return new Promise(function(resolve) {
    const timestamp = Date.now();

    setTimeout(() => {
      // 更新上下文信息
      contextInfo.sid = generateSessionId();
      contextInfo.pvid = generatePageViewId();
      contextInfo.url = window.location.href;
      contextInfo.path = window.location.pathname;
      contextInfo.ts = Date.now();

      // 更新元数据
      function updateMeta(eventType, sendTime) {
        metaInfo.sendTime = sendTime;
        metaInfo.type = eventType;
      }

      updateMeta(type, timestamp);

      // 根据事件类型更新页面视图
      switch(type) {
        case "PAGEVIEW":
          this.pageviewIntance.updateOther("", "");
          break;
        case "PERFORMANCE":
          this.pageviewIntance.updateOther("performance", data);
          break;
        case "ACTION":
          this.pageviewIntance.updateOther("action", data);
          break;
        case "CUSTOM":
          this.pageviewIntance.updateOther("custom", data);
          break;
        case "ERROR":
          this.pageviewIntance.updateOther("error", data);
          break;
        case "NETWORK":
          this.pageviewIntance.updateOther("network", data);
          break;
      }

      // 发送数据
      function sendData(options) {
        return new Promise((resolve, reject) => {
          const localParams = sessionStorage.getItem("p") || {};
          // console.log("ubt params", localParams);
          const appId = localParams.gameid;
          const endpoint = location.origin;

          const info = {
            event: 'current_faq', // 打点区分: 定制版
            user_id: localParams.fpid,
            app_id: appId,
            event: "ubt",
            event_ts: new Date().getTime(),
            event_tag: "custom",
            data_version: "1.0",
            log_source: "wp",
            details: {
              sys_name: 'current_wo',
              track_key: localParams.track_key,
              datafrom: 'web',
              ...options.data
            }
          }

          // navigator.sendBeacon(`${endpoint}/backend/v1/report/log`, JSON.stringify(payload));
          const params = {
            log_info: info,
            log: JSON.stringify(info)
          }
          setLog(params).then(res => {
            // console.log("ubt res", res);
            resolve(res);
          }).catch(error => {
            console.log("ubt error", error);
            reject(error);
          });
        });
      }

      sendData({ data: this.pageviewIntance.pageview })
        .then(result => resolve(result))
        .catch(error => console.log("report type error", error));
    }, 0);
  }.bind(this));
}

// 错误监控
function setupErrorMonitoring() {
  // 监听资源加载错误
  window.addEventListener("error", function(event) {
    const errorData = {};

    // 区分资源加载错误和JS执行错误
    if (event.target !== window) {
      errorData.type = "LOAD";
      const target = event.target;
      errorData.message = `{"message": ${event.message || ""}, "src": ${target.src}}`;
      this.report("ERROR", errorData);
    }
  }.bind(this), true);

  // 监听JS执行错误
  window.onerror = function(message, source, lineno, colno, error) {
    const errorData = { type: "ERROR" };
    errorData.message = message;
    errorData.filename = source;
    errorData.lineno = lineno;
    errorData.colno = colno;
    errorData.error = error;

    this.report("ERROR", errorData);
  }.bind(this);

  // 监听未处理的Promise错误
  window.addEventListener("unhandledrejection", function(event) {
    const errorData = { type: "ERROR" };

    try {
      const reason = event.reason;
      let name = "UnhandledRejection";
      let message = "未知Promise错误";
      let stack = "";

      // 处理不同类型的错误对象
      if (reason && typeof reason === 'object') {
        if (reason.name) name = reason.name;

        if (reason.message) {
          // 标准Error对象
          message = reason.message;
        } else if (reason.statusText) {
          // 可能是HTTP错误
          message = `HTTP错误: ${reason.status || ''} ${reason.statusText}`;
        } else {
          // 尝试转换为JSON字符串
          try {
            message = `对象错误: ${JSON.stringify(reason)}`;
          } catch (e) {
            // 如果转换失败，至少记录对象的属性名
            message = `对象错误: ${Object.keys(reason).join(', ')}`;
          }
        }

        // 提取堆栈信息
        if (reason.stack) stack = reason.stack;
      } else if (typeof reason === 'string') {
        // 字符串错误
        message = reason;
      } else {
        // 其他类型错误
        message = String(reason);
      }

      this.report("ERROR", Object.assign({}, errorData, {
        name: name,
        message: message,
        stack: stack
      }));

    } catch (e) {
      // 处理提取错误信息过程中的异常
      this.report("ERROR", Object.assign({}, errorData, {
        name: "ErrorExtractionFailure",
        message: `错误提取失败: ${e instanceof Error ? e.message : String(e)}`,
        stack: e instanceof Error ? e.stack : ""
      }));
    }

    console.log("Unhandled promise rejection:", event.reason);
  }.bind(this), true);
}

// 自定义事件上报
function reportCustomEvent(key, data) {
  return this.report("CUSTOM", {
    key: key,
    data: data ? JSON.stringify(data) : "{}"
  });
}

// 页面浏览监听
function setupPageViewMonitoring() {
  // 保存原始的历史方法
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  // 页面浏览上报函数
  const reportPageView = function() {
    console.log("PAGEVIEW", generatePageViewId(true));
    this.report("PAGEVIEW", "");
  }.bind(this);

  // 重写历史方法，增加上报
  history.pushState = function() {
    originalPushState.apply(history, arguments);
    reportPageView();
  };

  history.replaceState = function() {
    originalReplaceState.apply(history, arguments);
    reportPageView();
  };

  // 监听页面加载
  window.addEventListener("load", reportPageView);
}

// UBT主类
class UBT {
  constructor(...args) {
    this.report = reportEvent;
    this.error = setupErrorMonitoring;
    this.pageView = setupPageViewMonitoring;
    this.setUser = setUserInfo;
    this.eventReport = reportCustomEvent;

    // 检查环境参数
    if (!args[0]) {
      throw Error("UBT constructor need env");
    }

    // 设置环境
    function setEnv(env) {
      metaInfo.env = env;
    }

    setEnv(args[0]);
    this.init();
  }

  init() {
    const self = this;

    try {
      // 避免重复初始化
      if (self.initialized) return;

      self.initialized = true;
      this.pageviewIntance = new PageView();
      self.pageView();
      self.error();
    } catch (error) {
      console.log(error);
    }

    return self;
  }

  // 更新配置选项
  updateOptions(options) {
    if (!options) return;

    // 更新设备信息
    if (options.agent) {
      function updateAgentInfo(info) {
        if (info) {
          Object.assign(deviceInfo, info);
        }
      }

      updateAgentInfo(options.agent);
    }

    // 更新地理位置信息
    if (options.geo) {
      function updateGeoInfo(info) {
        Object.assign(geoInfo, info);
      }

      updateGeoInfo(options.geo);
    }
  }
}

// 暴露给全局
window.UBT = UBT;
