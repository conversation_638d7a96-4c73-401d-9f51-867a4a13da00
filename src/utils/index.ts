/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2022-05-11 11:27:56
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-12-09 12:02:55
 */

import { App as Application } from 'vue'
import setLog from './log'
// import projectMap from '@/customConfig/projectMap'
// import grayUidList from '@/customConfig/grayList.json'
import store from '@/store'
import { crypto } from './crypto'
// import { getCurrentInstance } from 'vue'
interface ObjAnyT {
  // eslint-disable-next-line
  [key: string]: any
}
// eslint-disable-next-line
type ArrayAny = Array<any>

/**
 * 存储管理器，封装本地存储操作
 */
class StorageManager {
  /**
   * 从SessionStorage获取JSON数据
   * @param key 存储键名
   * @param defaultValue 默认值
   */
  static getJSON<T>(key: string, defaultValue: T): T {
    const value = sessionStorage.getItem(key)
    if (!value) return defaultValue
    try {
      return JSON.parse(value) as T
    } catch (e) {
      console.error(`解析存储值失败: ${key}`, e)
      return defaultValue
    }
  }

  /**
   * 获取字符串值
   * @param key 存储键名
   * @param defaultValue 默认值
   */
  static getString(key: string, defaultValue: string = ''): string {
    return sessionStorage.getItem(key) || defaultValue
  }

  /**
   * 存储JSON数据
   * @param key 存储键名
   * @param value 值
   */
  static setJSON(key: string, value: any): void {
    try {
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (e) {
      console.error(`存储JSON值失败: ${key}`, e)
    }
  }

  /**
   * 存储字符串
   * @param key 存储键名
   * @param value 值
   */
  static setString(key: string, value: string): void {
    sessionStorage.setItem(key, value)
  }

  /**
   * 批量存储多个值
   * @param items 键值对象
   */
  static setBatch(items: Record<string, any>): void {
    Object.entries(items).forEach(([key, value]) => {
      if (typeof value === 'string') {
        this.setString(key, value)
      } else {
        this.setJSON(key, value)
      }
    })
  }
}

export const getParamsFromUrl = (): ObjAnyT => {
  const lowercase = ['os', 'channel', 'lang', 'sdk_version']
  const params: ObjAnyT = {}
  const searchParams = new URLSearchParams(window.location.search)

  for (const [key, value] of searchParams.entries()) {
    if (!key) continue
    params[key] = lowercase.includes(key) ? value.toLowerCase() : value
  }

  return params
}

// 临时：判断是否启用升级版智能客服
// 缓存Upgrade的结果，避免重复调用API
let upgradeCache: Promise<Record<string, unknown>> | null = null

export const Upgrade = async (): Promise<Record<string, unknown>> => {
  // 获取当前URL参数
  const urlParams = await getParamsFromUrl()

  // 判断URL中是否包含scene参数，表示需要重新获取灰度数据
  const shouldRefreshData = !!urlParams.scene

  // 如果需要重新获取数据或者没有缓存，则重置缓存
  if (shouldRefreshData || !upgradeCache) {
    // 重置缓存并创建新的Promise
    upgradeCache = _doUpgrade(urlParams, shouldRefreshData)
  }

  // 返回缓存的Promise结果
  return upgradeCache
}

// 实际执行Upgrade逻辑的内部函数
const _doUpgrade = async (urlParams: ObjAnyT, shouldRefreshData: boolean): Promise<Record<string, unknown>> => {
  console.log('执行Upgrade, shouldRefreshData:', shouldRefreshData)

  // 如果需要刷新数据，先清除相关缓存
  if (shouldRefreshData) {
    console.log('检测到新的scene，清除旧的灰度数据缓存')
    StorageManager.setBatch({
      mark: '',
      projectName: '',
      isPC: '',
      vipUser: '',
      isShowChat: ''
    })
  }

  // 判断是否已有存储的配置数据且不需要刷新
  const cachedMark = StorageManager.getJSON<boolean>('mark', false)
  const cachedProjectName = StorageManager.getString('projectName')
  const cachedIsPC = StorageManager.getJSON<boolean>('isPC', false)

  console.log('cachedMark, cachedProjectName, cachedIsPC', cachedMark, cachedProjectName, cachedIsPC)

  // 仅当有完整的缓存数据且不需要刷新时，才使用缓存
  if (!shouldRefreshData && cachedMark !== false && cachedProjectName) {
    console.log('utils 使用缓存数据')
    return {
      mark: cachedMark,
      projectName: cachedProjectName,
      isPC: cachedIsPC
    }
  }

  try {
    const _S = 'Uc9Ud64Uf6Uc517354797809Uc27Ue6Ud69Uc70Ub053UaUd3Uc03608UdUa522Ub7160174'
    const apiKey = 'UcUd0893Uf053UaUd3Uc03608UdUa522Ub71601744UdUa9UfUf4855226U'

    // 获取URL参数或缓存参数
    const sessionParams = StorageManager.getString('p')
    // 如果URL中有scene，优先使用URL参数，否则遵循原有逻辑
    const paramsObj =
      shouldRefreshData || JSON.stringify(urlParams) !== '{}' ? urlParams : sessionParams && JSON.parse(sessionParams)

    // 如果有新的scene，更新sessionstorage中的p参数
    if (shouldRefreshData && paramsObj) {
      StorageManager.setJSON('p', paramsObj)
    }

    const WXTimestamp = new Date().getTime()

    // 解析json_data
    let json_data: ObjAnyT = {}
    try {
      if (paramsObj.json_data) {
        const decodedData = decodeURIComponent(paramsObj.json_data)
        if (decodedData) {
          json_data = JSON.parse(decodedData) as ObjAnyT
        }
      }
    } catch (error) {
      console.log('json_data error', error)
    }

    const isWorldX = location.hostname === 'fpcs-web.kingsglorygames.com' || location.hostname === 'localhost'
    const visitorIdFunc = getVisitorId();
    const visitorId = visitorIdFunc();
    // 构建请求参数
    const params = {
      json_data: JSON.stringify(json_data),
      scene: paramsObj.scene ? Number(paramsObj.scene) : 0,
      uuid: paramsObj.device_id || (isWorldX ? visitorId : ''),
      device_type: paramsObj.device_type ?? decodeURIComponent(paramsObj.device_type),
      os_version: paramsObj.os_version,
      rom_gb: paramsObj.rom_gb,
      remain_rom: paramsObj.remain_rom,
      app_version: paramsObj.app_version,
      ram_mb: paramsObj.ram_mb,
      network_info: paramsObj.network_info,
      subchannel: paramsObj.subchannel,
      pkgChannel: paramsObj.pkgChannel,
      role_id: paramsObj.role_id ? decodeURIComponent(paramsObj.role_id) : '',
      nickname: paramsObj.role_name
        ? decodeURIComponent(paramsObj.role_name)
        : paramsObj.name
        ? decodeURIComponent(paramsObj.name)
        : '',
      total_pay: paramsObj.pay_amount
        ? Number(paramsObj.pay_amount)
        : json_data && json_data.total_pay
        ? Number(json_data.total_pay)
        : 0,
      ts: paramsObj.openid ? WXTimestamp : paramsObj.ts ? Number(paramsObj.ts) : 0,
      game_id: paramsObj.gameid ? Number(paramsObj.gameid) : 0,
      sid: paramsObj.sid,
      fpid: paramsObj.fpid ? Number(paramsObj.fpid) : 0,
      uid: paramsObj.uid ? Number(paramsObj.uid) : 0,
      os: paramsObj.os,
      channel: paramsObj.channel,
      lang: paramsObj.lang,
      sdk_version: paramsObj.sdk_version,
      game_token: paramsObj.game_token,
      track_key: paramsObj.openid ? '' + paramsObj.uid + WXTimestamp : paramsObj.track_key,
      country_code: paramsObj.country_code ? paramsObj.country_code : '',
      // fpx兼容字段
      fpx_app_id: paramsObj.fpx_app_id ? paramsObj.fpx_app_id : '',
      account_id: paramsObj.account_id ? decodeURIComponent(paramsObj.account_id) : '',
      fp_uid: paramsObj.fp_uid ? paramsObj.fp_uid : '',
      // 微信下游戏兼容字段
      openid: paramsObj.openid ? paramsObj.openid : '',
      // 私域打开客服字段
      zone_token: paramsObj.zone_token,
      zone_from: paramsObj.zone_from,
      log_source: paramsObj.log_source,
      funplus_id: paramsObj.funplus_id,
      properties: paramsObj.properties
    }
    const sign = crypto(apiKey, JSON.stringify(params), _S)
    let mark = false,
      projectName = '',
      vipUser = false,
      isShowChat = false

    const checkGrayMode = async (): Promise<void> => {
      try {
        // 灰度+跳转精灵接口
        const response = await fetch('/backend/v3/elfin/grayscale', {
          method: 'POST',
          headers: {
            sign,
            'api-key': apiKey,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(params)
        })
        if (!response.ok) {
          throw new Error('Network response was not ok')
        }
        const data = await response.json() // 将响应数据解析为JSON格式
        // hit：是否命中灰度+是否跳转精灵
        // 判断是否游戏外场景（游戏正在加载过程中点击联系客服，直接跳通用工单）
        // scene含义：0：定制版游戏内，3：通用版游戏内，1：加载，2：封号
        mark = (data?.data?.hit && Number(params.scene) !== 1 && Number(params.scene) !== 2) || false
        projectName = data?.data?.projectName || '' // 游戏名称 前端用来动态加载对应的样式组
        vipUser = data?.data?.vipUser || false // 是否是VIP用户
        isShowChat = data?.data?.is_show_chat || false // 是否显示工单对话

        // 批量存储配置数据
        StorageManager.setBatch({
          mark: mark,
          projectName: projectName,
          vipUser: vipUser,
          isShowChat: isShowChat
        })

        store.commit('global/SET_IS_SHOW_CHAT', isShowChat)
      } catch (error) {
        console.error('There has been a problem with your fetch operation: ', error)
      }
    }

    await checkGrayMode() // 等待checkGrayMode函数的完成
    const isPC = window.location.href.indexOf('/pc') > -1
    StorageManager.setJSON('isPC', isPC)

    return {
      mark,
      projectName,
      isPC,
      isShowChat
    }
  } catch (error) {
    console.error('Error in Upgrade:', error)
    return {
      mark: false,
      projectName: '',
      isPC: false,
      isShowChat: false
    }
  }
}

function padLeftZero(str: string) {
  return ('00' + str).substr(str.length)
}

export const formatDate = (timestemp: string | number | Date, fmt?: string): string => {
  if (!timestemp) return ''

  fmt = fmt || 'YYYY-MM-DD hh:mm:ss'

  if (timestemp.toString().length === 10 && typeof timestemp === 'number') {
    timestemp *= 1e3
  }

  const date = new Date(timestemp)
  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  const o: { [key: string]: number } = {
    'M+': date.getMonth() + 1,
    'D+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + ''
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
    }
  }
  return fmt
}

export const jsBridge = (path: string, params?: ObjAnyT): void => {
  const SDKUrl = 'http://kingsgroup.game/'
  let url = SDKUrl + path
  if (params) {
    const paramArr: ArrayAny = []
    Object.keys(params).forEach(key => {
      paramArr.push(`${key}=${params[key]}`)
    })
    url += `?${paramArr.join('&')}`
  }
  window.location.href = url
}

/**
 * 解析URL中经过base64加密的参数
 * @param paramName 参数名称，默认为'data'
 * @returns 解析后的参数对象，如果解析失败则返回空对象
 *
 * 使用示例：
 * // URL: https://example.com?data=eyJpZCI6MTIzLCJuYW1lIjoi5ZOI5ZOIIn0=
 * const params = parseBase64Params()
 * console.log(params) // 输出: { id: 123, name: "张三" }
 */
export const parseBase64Params = (paramName: string = 'data'): ObjAnyT => {
  try {
    const url = new URL(window.location.href)
    const encodedData = url.searchParams.get(paramName)

    if (!encodedData) {
      console.warn(`URL中不存在参数: ${paramName}`)
      return {}
    }

    // 解码base64
    const decodedString = window.atob(encodedData)

    // 尝试解析JSON
    return JSON.parse(decodedString) as ObjAnyT
  } catch (error) {
    console.error('解析base64加密参数失败:', error)
    return {}
  }
}

export const browser = (function () {
  const ua = navigator.userAgent
  const version = {
    isIos: !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios 终端
    isWindows: ua.indexOf('Windows') > -1 || ua.indexOf('Macintosh') > -1, // windows终端 或 mac终端
    isMobile: !!ua.match(/AppleWebKit.*Mobile.*/), // 是否移动终端
    isIpad: !!ua.match(/(iPad)/) || (ua.indexOf('Macintosh') > -1 && navigator.maxTouchPoints > 1),
    isWebApp: ua.indexOf('Safari') === -1, // 是否为Webapp，没有头部和底部
    isTrident: ua.indexOf('Trident') > -1, // IE内核
    isPresto: ua.indexOf('Presto') > -1, // opera 内核
    isWebKit: ua.indexOf('AppleWebKit') > -1, // 苹果谷歌内核
    isGecko: ua.indexOf('Gecko') > -1 && ua.indexOf('KHTML') === -1, // 火狐内核
    isWechat: !!ua.match(/micromessenger/gi),
    isWeiBo: !!ua.match(/weibo/gi),
    isQQ: !!ua.match(/qq/gi)
  }
  const language = navigator?.language?.toLowerCase() || 'en'
  const screen = { clientWidth: document.documentElement.clientWidth, height: document.documentElement.clientHeight }
  // console.error('main docEl.clientWidth ', document.documentElement.clientWidth)
  // console.error('main docEl.clientHeight ', )
  return {
    version,
    language,
    screen
  }
})()

/**
 * 检查i18n语料键是否存在
 * @param key 需要检查的语料键
 * @param i18n i18n实例(可选)
 * @returns 返回布尔值，表示语料是否存在
 *
 * 使用示例：
 * 1. 在setup中使用：
 *    import { useI18n } from 'vue-i18n'
 *    import { hasI18nKey } from '@/utils'
 *
 *    const { t, messages, locale } = useI18n()
 *    // 检查语料是否存在
 *    if (hasI18nKey('text_unknown_script_in_dc.global.prod_1', messages.value, locale.value)) {
 *      // 存在，可以安全使用
 *      const text = t('text_unknown_script_in_dc.global.prod_1')
 *    } else {
 *      // 不存在，使用默认语料
 *      const text = t('text_unknown_script')
 *    }
 *
 * 2. 在setup外使用：
 *    import { hasI18nKey } from '@/utils'
 *    import i18n from '@/i18n' // 假设i18n实例位于此路径
 *
 *    // 检查语料是否存在
 *    if (hasI18nKey('text_unknown_script_in_dc.global.prod_1')) {
 *      // 存在，可以安全使用
 *      const text = i18n.global.t('text_unknown_script_in_dc.global.prod_1')
 *    } else {
 *      // 不存在，使用默认语料
 *      const text = i18n.global.t('text_unknown_script')
 *    }
 */
export const hasI18nKey = (
  key: string,
  messages?: Record<string, Record<string, string>>,
  locale?: string
): boolean => {
  if (!key) return false

  // 如果提供了messages和locale，直接使用
  if (messages && locale && messages[locale]) {
    return key in messages[locale]
  }

  // 尝试从sessionStorage获取当前语言
  const currentLocale = sessionStorage.getItem('locale') || 'zh'

  try {
    // 检查是否存在全局i18n对象
    // 注意：这种方法依赖于具体的i18n配置，可能需要根据项目实际情况调整
    const i18nInstance = (window as any).$i18n
    if (i18nInstance && i18nInstance.global && i18nInstance.global.messages) {
      const localeToUse = i18nInstance.global.locale.value || currentLocale
      return key in i18nInstance.global.messages[localeToUse]
    }
  } catch (e) {
    console.warn('检查i18n键时出错:', e)
  }

  // 无法确定键是否存在，返回false
  return false
}

/**
 * 安全获取i18n翻译，如果键不存在则返回默认值
 * @param key 语料键
 * @param defaultValue 默认值(可选)
 * @param t 翻译函数(可选)
 * @returns 翻译文本或默认值
 */
export const safeI18n = (
  key: string,
  defaultValue: string = '',
  messages?: Record<string, Record<string, string>>,
  locale?: string,
  t?: (key: string) => string
): string => {
  if (!hasI18nKey(key, messages, locale)) {
    return defaultValue
  }

  // 如果提供了翻译函数，直接使用
  if (t) {
    return t(key)
  }

  try {
    // 尝试使用全局i18n实例
    const i18nInstance = (window as any).$i18n
    if (i18nInstance && i18nInstance.global && i18nInstance.global.t) {
      return i18nInstance.global.t(key)
    }
  } catch (e) {
    console.warn('安全获取i18n翻译时出错:', e)
  }

  return defaultValue
}

/**
 * 获取访客ID
 * @returns {string} 访客ID
 */
// 常量定义
const STORAGE_KEYS = {
  VISITOR_ID: 'EVENT_VID',
  SESSION_ID: 'SESSION_SID',
  PARAMS: 'p'
}

/**
 * 生成UUID
 * @returns {string} 生成的UUID
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Date.now() + Math.random() * 16) % 16 | 0
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
}

export const getVisitorId = () => {
  // 使用闭包缓存ID
  let cachedId: string | null = null

  return function () {
    if (cachedId) return cachedId

    try {
      // 尝试从localStorage获取
      const storedId = localStorage.getItem(STORAGE_KEYS.VISITOR_ID)
      if (storedId) {
        cachedId = storedId
        return storedId
      }

      // 生成新ID并存储
      const newId = generateUUID()
      localStorage.setItem(STORAGE_KEYS.VISITOR_ID, newId)
      cachedId = newId
      return newId
    } catch (error) {
      // 如果localStorage不可用，生成临时ID
      console.warn('无法访问localStorage:', error)
      return generateUUID()
    }
  }
}

const fpUtils = {
  getParamsFromUrl,
  formatDate,
  jsBridge,
  hasI18nKey,
  safeI18n,
  parseBase64Params,
  ...setLog
}
export default {
  install(app: Application): void {
    app.config.globalProperties.$utils = fpUtils
  }
}
