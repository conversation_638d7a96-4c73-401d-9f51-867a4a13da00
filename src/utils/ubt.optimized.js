import { setLog } from '@/api';

/**
 * UBT (User Behavior Tracking) 模块
 * 用于跟踪和上报用户行为数据
 */

// 常量定义
const STORAGE_KEYS = {
  VISITOR_ID: 'EVENT_VID',
  SESSION_ID: 'SESSION_SID',
  PARAMS: 'p'
};

const SESSION_TIMEOUT = 30 * 60 * 1000; // 30分钟会话超时

// 事件类型枚举
const EventType = Object.freeze({
  PAGEVIEW: 'PAGEVIEW',
  PERFORMANCE: 'PERFORMANCE',
  ACTION: 'ACTION',
  CUSTOM: 'CUSTOM',
  ERROR: 'ERROR',
  EXPOSURE: 'EXPOSURE',
  NETWORK: 'NETWORK'
});

/**
 * 生成UUID
 * @returns {string} 生成的UUID
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (Date.now() + Math.random() * 16) % 16 | 0;
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

/**
 * 获取访客ID
 * @returns {string} 访客ID
 */
function getVisitorId() {
  // 使用闭包缓存ID
  let cachedId = null;

  return function() {
    if (cachedId) return cachedId;

    try {
      // 尝试从localStorage获取
      const storedId = localStorage.getItem(STORAGE_KEYS.VISITOR_ID);
      if (storedId) {
        cachedId = storedId;
        return storedId;
      }

      // 生成新ID并存储
      const newId = generateUUID();
      localStorage.setItem(STORAGE_KEYS.VISITOR_ID, newId);
      cachedId = newId;
      return newId;
    } catch (error) {
      // 如果localStorage不可用，生成临时ID
      console.warn('无法访问localStorage:', error);
      return generateUUID();
    }
  };
}

/**
 * 获取会话ID
 * @returns {string} 会话ID
 */
function getSessionId() {
  // 使用闭包缓存会话数据
  let sessionCache = null;

  return function() {
    const now = Date.now();

    // 检查缓存是否有效
    if (sessionCache && sessionCache.expiry > now) {
      return sessionCache.id;
    }

    try {
      // 尝试从sessionStorage获取
      const storedSession = sessionStorage.getItem(STORAGE_KEYS.SESSION_ID);
      if (storedSession) {
        const sessionData = JSON.parse(storedSession);
        if (sessionData.expiry > now) {
          sessionCache = sessionData;
          return sessionData.id;
        }
      }

      // 生成新会话ID
      const sessionId = generateTimestamp() + Math.random().toString(36).substring(2);
      sessionCache = {
        id: sessionId,
        expiry: now + SESSION_TIMEOUT
      };

      sessionStorage.setItem(STORAGE_KEYS.SESSION_ID, JSON.stringify(sessionCache));
      return sessionId;
    } catch (error) {
      // 如果sessionStorage不可用，生成临时ID
      console.warn('无法访问sessionStorage:', error);
      return generateTimestamp() + Math.random().toString(36).substring(2);
    }
  };
}

/**
 * 生成基于时间的时间戳
 * @returns {string} 格式化的时间戳
 */
function generateTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}

/**
 * 获取页面访问ID
 * @param {boolean} forceNew 是否强制生成新ID
 * @returns {string} 页面访问ID
 */
function getPageViewId() {
  let cachedId = null;

  return function(forceNew = false) {
    if (cachedId && !forceNew) return cachedId;
    cachedId = generateUUID();
    return cachedId;
  };
}

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器名称和版本
 */
function getBrowserInfo() {
  const ua = navigator.userAgent;
  const browserPatterns = [
    { name: 'Firefox', regex: /firefox|fxios/i, versionRegex: /(?:firefox|fxios)\/(\d+\.\d+)/i },
    { name: 'Edge', regex: /edg/i, versionRegex: /edg\/(\d+\.\d+)/i },
    { name: 'Chrome', regex: /chrome|crios|crmo/i, versionRegex: /(?:chrome|crios|crmo)\/(\d+\.\d+)/i },
    { name: 'Safari', regex: /safari/i, versionRegex: /version\/(\d+\.\d+)/i },
    { name: 'Internet Explorer', regex: /msie|trident/i, versionRegex: /(?:msie |rv:)(\d+\.\d+)/i }
  ];

  // 按优先级检查浏览器类型
  for (const browser of browserPatterns) {
    if (browser.regex.test(ua) && !(browser.name === 'Safari' && /chrome|crios|crmo/i.test(ua)) &&
        !(browser.name === 'Chrome' && /edg/i.test(ua))) {
      try {
        const match = ua.match(browser.versionRegex);
        return {
          name: browser.name,
          version: match ? match[1] : 'Unknown'
        };
      } catch (e) {
        console.warn(`解析浏览器版本出错:`, e);
      }
    }
  }

  return { name: 'Unknown', version: 'Unknown' };
}

/**
 * 从sessionStorage获取参数
 * @returns {Object} 解析后的参数对象
 */
function getSessionParams() {
  try {
    const paramsStr = sessionStorage.getItem(STORAGE_KEYS.PARAMS);
    return paramsStr ? JSON.parse(paramsStr) : {};
  } catch (error) {
    console.warn('解析会话参数出错:', error);
    return {};
  }
}

// 初始化各种信息对象
const visitorId = getVisitorId();
const sessionId = getSessionId();
const pageViewId = getPageViewId();

// 上下文信息
const contextInfo = {
  get vid() { return visitorId(); },
  get sid() { return sessionId(); },
  get pvid() { return pageViewId(); },
  get path() { return window.location.pathname; },
  get url() { return window.location.href; },
  get userAgent() { return window.navigator.userAgent; },
  get ts() { return Date.now(); }
};

// 设备信息
const browserInfo = getBrowserInfo();
const deviceInfo = {
  screen: `${window.screen.width}x${window.screen.height}`,
  network: 'other',
  browserName: browserInfo.name,
  browserVer: browserInfo.version
};

// 元数据、地理位置和用户信息
const metaInfo = { env: 'dev' };
const geoInfo = {};
const userInfo = { id: '', token: '' };

/**
 * 页面视图类 - 管理页面视图数据
 */
class PageView {
  constructor() {
    this._data = {
      meta: metaInfo,
      context: contextInfo,
      agent: deviceInfo,
      geo: geoInfo,
      user: userInfo
    };
    this._lastAttrName = null;
  }

  get data() {
    return this._data;
  }

  /**
   * 更新特定属性
   * @param {string} attrName 属性名
   * @param {any} attrValue 属性值
   */
  updateAttribute(attrName, attrValue) {
    // 删除上一个临时属性
    if (this._lastAttrName && this._lastAttrName !== attrName) {
      delete this._data[this._lastAttrName];
    }

    this._lastAttrName = attrName;

    // 添加新属性
    if (attrName) {
      this._data[attrName] = attrValue;
    }
  }
}

/**
 * 发送日志数据到服务器
 * @param {Object} data 要发送的数据
 * @returns {Promise} 请求结果Promise
 */
function sendLogData(data) {
  return new Promise((resolve, reject) => {
    const localParams = getSessionParams();

    const info = {
      event: 'current_faq', // 打点区分: 定制版
      user_id: localParams.fpid || '',
      app_id: localParams.gameid || '',
      event_ts: Date.now(),
      event_tag: 'custom',
      data_version: '1.0',
      log_source: 'wp',
      details: {
        sys_name: 'current_wo',
        track_key: localParams.track_key || '',
        datafrom: 'web',
        ...data
      }
    };

    const params = {
      log_info: info,
      log: JSON.stringify(info)
    };

    setLog(params)
      .then(resolve)
      .catch(error => {
        console.error('UBT日志发送失败:', error);
        reject(error);
      });
  });
}

/**
 * UBT主类 - 用户行为跟踪
 */
class UBT {
  constructor(env) {
    if (!env) {
      throw new Error('UBT构造函数需要环境参数');
    }

    // 初始化属性
    this.initialized = false;
    this.pageViewInstance = null;
    this._eventListeners = [];

    // 设置环境
    metaInfo.env = env;

    // 初始化
    this.init();
  }

  /**
   * 初始化UBT
   * @returns {UBT} 当前实例
   */
  init() {
    try {
      // 避免重复初始化
      if (this.initialized) return this;

      this.initialized = true;
      this.pageViewInstance = new PageView();

      // 设置页面浏览监控
      this._setupPageViewMonitoring();

      // 设置错误监控
      this._setupErrorMonitoring();

    } catch (error) {
      console.error('UBT初始化失败:', error);
    }

    return this;
  }

  /**
   * 更新配置选项
   * @param {Object} options 配置选项
   */
  updateOptions(options) {
    if (!options) return;

    // 更新设备信息
    if (options.agent) {
      Object.assign(deviceInfo, options.agent);
    }

    // 更新地理位置信息
    if (options.geo) {
      Object.assign(geoInfo, options.geo);
    }
  }

  /**
   * 设置用户信息
   * @param {Object} info 用户信息
   */
  setUser(info) {
    if (info) {
      Object.assign(userInfo, info);
    }
  }

  /**
   * 上报事件
   * @param {string} type 事件类型
   * @param {any} data 事件数据
   * @returns {Promise} 上报结果Promise
   */
  report(type, data) {
    return new Promise(resolve => {
      const timestamp = Date.now();

      // 使用setTimeout确保异步执行
      setTimeout(() => {
        try {
          // 更新元数据
          metaInfo.sendTime = timestamp;
          metaInfo.type = type;

          // 根据事件类型更新页面视图
          switch(type) {
            case EventType.PAGEVIEW:
              this.pageViewInstance.updateAttribute('', '');
              break;
            case EventType.PERFORMANCE:
            case EventType.ACTION:
            case EventType.CUSTOM:
            case EventType.ERROR:
            case EventType.NETWORK:
              this.pageViewInstance.updateAttribute(type.toLowerCase(), data);
              break;
          }

          // 发送数据
          sendLogData(this.pageViewInstance.data)
            .then(resolve)
            .catch(error => console.error('上报事件失败:', error));
        } catch (error) {
          console.error('处理事件数据失败:', error);
          resolve(null);
        }
      }, 0);
    });
  }

  /**
   * 上报自定义事件
   * @param {string} key 事件键
   * @param {any} data 事件数据
   * @returns {Promise} 上报结果Promise
   */
  eventReport(key, data) {
    return this.report(EventType.CUSTOM, {
      key: key,
      data: data ? JSON.stringify(data) : '{}'
    });
  }

  /**
   * 设置页面浏览监控
   * @private
   */
  _setupPageViewMonitoring() {
    // 保存原始的历史方法
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    // 页面浏览上报函数
    const reportPageView = () => {
      pageViewId(true); // 强制生成新的页面ID
      this.report(EventType.PAGEVIEW, '');
    };

    // 重写历史方法，增加上报
    history.pushState = function() {
      originalPushState.apply(history, arguments);
      reportPageView();
    };

    history.replaceState = function() {
      originalReplaceState.apply(history, arguments);
      reportPageView();
    };

    // 监听页面加载
    const loadListener = () => reportPageView();
    window.addEventListener('load', loadListener);
    this._eventListeners.push({ target: window, type: 'load', listener: loadListener });

    // 监听页面可见性变化
    const visibilityListener = () => {
      if (document.visibilityState === 'visible') {
        reportPageView();
      }
    };
    document.addEventListener('visibilitychange', visibilityListener);
    this._eventListeners.push({ target: document, type: 'visibilitychange', listener: visibilityListener });
  }

  /**
   * 设置错误监控
   * @private
   */
  _setupErrorMonitoring() {
    // 监听资源加载错误
    const resourceErrorListener = (event) => {
      // 区分资源加载错误和JS执行错误
      if (event.target !== window) {
        const errorData = {
          type: 'LOAD',
          message: JSON.stringify({
            message: event.message || '',
            src: event.target.src || event.target.href || ''
          })
        };
        this.report(EventType.ERROR, errorData);
      }
    };
    window.addEventListener('error', resourceErrorListener, true);
    this._eventListeners.push({ target: window, type: 'error', listener: resourceErrorListener, options: true });

    // 监听JS执行错误
    window.onerror = (message, source, lineno, colno, error) => {
      const errorData = {
        type: 'ERROR',
        message: message,
        filename: source,
        lineno: lineno,
        colno: colno,
        stack: error?.stack || ''
      };
      this.report(EventType.ERROR, errorData);
    };

    // 监听未处理的Promise错误
    const promiseErrorListener = (event) => {
      const reason = event.reason || {};
      const errorData = {
        type: 'PROMISE_ERROR',
        name: reason.name || 'UnhandledRejection',
        message: reason.message || String(reason),
        stack: reason.stack || ''
      };
      this.report(EventType.ERROR, errorData);
    };
    window.addEventListener('unhandledrejection', promiseErrorListener);
    this._eventListeners.push({ target: window, type: 'unhandledrejection', listener: promiseErrorListener });
  }

  /**
   * 清理所有事件监听器
   * 在SPA应用中可能需要调用此方法防止内存泄漏
   */
  cleanup() {
    // 移除所有注册的事件监听器
    this._eventListeners.forEach(({ target, type, listener, options }) => {
      target.removeEventListener(type, listener, options);
    });
    this._eventListeners = [];

    // 恢复原始的history方法
    if (history._originalPushState) {
      history.pushState = history._originalPushState;
    }
    if (history._originalReplaceState) {
      history.replaceState = history._originalReplaceState;
    }
  }

  /**
   * 页面浏览事件
   */
  pageView() {
    pageViewId(true);
    return this.report(EventType.PAGEVIEW, '');
  }

  /**
   * 错误事件
   */
  error() {
    this._setupErrorMonitoring();
  }
}

// 保存原始history方法以便清理
history._originalPushState = history.pushState;
history._originalReplaceState = history.replaceState;

// 暴露给全局
window.UBT = UBT;

export default UBT;
