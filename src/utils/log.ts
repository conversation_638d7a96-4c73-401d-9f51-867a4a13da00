/* 
 * @Author: wenh<PERSON>.wang 
 * @Date: 2022-05-11 11:34:51 
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2022-08-04 15:49:13
 */
/* eslint-disable */
import { setLog } from '@/api'
import store from '@/store'

interface InfoT {
  session_id: string,
  event: string,
  user_id: any
  app_id: any,
  details: {
    [key: string]: any
  }
}
export const setLogs = (logInfo = {}, callback?: (data: any) => unknown): void => {
  const gameParams = store.state.userInfo
  const info: InfoT = {
    session_id: '',
    event: 'current_faq', // 打点区分: 定制版
    user_id: gameParams.fpid,
    app_id: gameParams.gameid,
    details: {
      sys_name: 'current_wo',
      track_key: gameParams.track_key,
      datafrom: 'web',
      ...logInfo
    }
  }
  const params = {
    log: JSON.stringify(info)
  }
  setLog(params).then((res: any) => {
    if (callback) {
      callback(res)
    }
  }).catch(function (err: any) {
    console.log(err)
    if (callback) {
      callback(err)
    }
  })
}

export default {
  basicLog: setLogs,
  adsLog: (params = {}): void => {
    const crtChannel = store.state.channel.currentChannel
    const newParams = {
      ...params,
      position: 'advertisement',
      tab: `${crtChannel.parent_id ? (crtChannel.parent_id + '||') : ''}${crtChannel.id}`,
      action: 'click'
    }
    setLogs(newParams)
  },
  articlesLog: (params = {}): void => {
    const crtChannel = store.state.channel.currentChannel
    const newParams = {
      ...params,
      tab: `${crtChannel.parent_id ? (crtChannel.parent_id + '||') : ''}${crtChannel.id}`,
      action: 'click'
    }
    setLogs(newParams)
  }
}
