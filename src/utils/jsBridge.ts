const METHOD_MAP: Record<TMethodKey, string> = {
  REDIRECT: 'redirect'
}
const SCENE_MAP: Record<TSceneKey, number> = {
  SDK: 0,
  GAME: 1
}
type ArrayAny = Array<unknown>
interface ObjAnyT {
  [key: string]: unknown
}
type TMethodKey = 'REDIRECT'
type TSceneKey = 'SDK' | 'GAME'

class JsBridge {
  private baseUrl: string
  
  constructor () {
    this.baseUrl = 'fpcapi://openai'
  }

  bridge (method: TMethodKey , scene: TSceneKey, target: string) {
    const url = `${this.baseUrl}?method=${METHOD_MAP[method]}&scene=${SCENE_MAP[scene]}&target=${target}`
    window.location.href = url
  }
  
  jumpToUrl (url: string) {
    this.bridge('REDIRECT', 'SDK', encodeURIComponent(url))
  }

  jumpToGame (event: string) {
    this.bridge('REDIRECT', 'GAME', event)
  }

  jsBridge (path: string, params?: ObjAnyT) {
    const SDKUrl = 'http://kingsgroup.game/'
    let url = SDKUrl + path
    if (params) {
      const paramArr: ArrayAny = []
      Object.keys(params).forEach(key => {
        paramArr.push(`${key}=${params[key]}`)
      })
      url += `?${paramArr.join('&')}`
    }
    window.location.href = url
  }
}

export default new JsBridge()
