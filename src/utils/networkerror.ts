import { createApp, reactive } from "vue"
import NextworkError from '@/components/networkerror/NextworkError.vue'
import store from "@/store/index"

const conf = reactive({
  show: false,
  callback: null as (() => void) | null
})

const $networkerror = createApp(NextworkError, {conf}).mount(document.createElement('div'))
export const networkerror = {
  show(cb: () => void): void {
    if (conf.show) return
    conf.callback = cb
    conf.show = true
    store.commit('setLoadingCount', 0)
    document.body.appendChild($networkerror.$el)
  },
  hide (): void{
    conf.show = false
  }
}
