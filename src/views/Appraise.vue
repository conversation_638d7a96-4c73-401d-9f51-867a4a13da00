<template>
	<div class="ticket-bg">
		<div class="ticket-wrapper">
			<div class="content">
				<van-form :show-error="false" class="form-wrapper">
					<div class="appraise">
						<div class="text">
							<div class="form-label required">{{ $t("text_appraise") }}</div>
							<span v-if="showChildStar">
								<div class="form-label required">{{ $t("text_appraise_attitude") }}</div>
								<div class="form-label required">{{ $t("text_appraise_speed") }}</div>
								<div class="form-label required">{{ $t("text_appraise_treatment") }}</div>
							</span>
						</div>
						<div class="star-box">
							<div v-for="(star, index) in 5" :key="index" class="star" :class="{ light: star <= selectNum }" @click="selectStar(index)"></div>
							<span v-if="showChildStar">
								<div v-for="(star, index) in 5" :key="index+'2'" class="star" :class="{ light: star <= selectNum2 }"
									@click="selectStarChild(index, 2)"></div>
								<div v-for="(star, index) in 5" :key="index+'3'" class="star" :class="{ light: star <= selectNum3 }"
									@click="selectStarChild(index, 3)"></div>
								<div v-for="(star, index) in 5" :key="index+'4'" class="star" :class="{ light: star <= selectNum4 }"
									@click="selectStarChild(index, 4)"></div>
							</span>
						</div>
					</div>
					<!-- 标签 -->
					<!-- <div v-if="selectNum <= 3 && selectNum !== 0" class="label-container">
						<div
							class="label"
							:class="{ select: labels.includes(2) }"
							@click="selectLabel(2)"
						>
							{{ $t("text_handle_speed_unsatisfy") }}
						</div>
						<div
							class="label"
							:class="{ select: labels.includes(3) }"
							@click="selectLabel(3)"
						>
							{{ $t("text_handle_result_unsatisfy") }}
						</div>
						<div
							class="label"
							:class="{ select: labels.includes(4) }"
							@click="selectLabel(4)"
						>
							{{ $t("text_handle_attitude_unsatisfy") }}
						</div>
					</div>
					<div v-if="selectNum !== 0 && selectNum > 3" class="label-container">
						<div
							class="label"
							:class="{ select: labels.includes(5) }"
							@click="selectLabel(5)"
						>
							{{ $t("text_solve_fast") }}
						</div>
						<div
							class="label"
							:class="{ select: labels.includes(6) }"
							@click="selectLabel(6)"
						>
							{{ $t("text_result_satisfy") }}
						</div>
						<div
							class="label"
							:class="{ select: labels.includes(7) }"
							@click="selectLabel(7)"
						>
							{{ $t("text_services_satisfy") }}
						</div>
					</div> -->
					<!-- 意见输入 -->
					<van-field
						ref="textarea"
						class="form-item"
						v-model="content"
						type="textarea"
						rows="5"
						autosize
						maxlength="200"
						show-word-limit
						:placeholder="$t('text_suggest_important')"
					/>
				</van-form>
				<!-- 提交按钮 -->
				<div class="button-wrapper" v-if="!isShowTips">
					<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
						<div class="fp-button" @mousedown.stop.prevent="handleAppraise" @touchstart.stop.prevent="handleAppraise"  @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("text_submit") }}</div>
					</div>
        </div>
				<!-- 底部提示，点击回首页 -->
				<div @click="goToHome" v-if="isShowTips" class="tips-gohome" v-html="$t('text_goto_home')"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
	import { defineComponent, onMounted, reactive, toRefs, ref, getCurrentInstance } from 'vue'
	import { useI18n } from 'vue-i18n'
	import { Toast } from 'vant'
	import { useRoute, useRouter } from 'vue-router'
	import { appraiseTicket } from '@/api/tickets'
	interface dataT {
		[key: string]: unknown,
		ticketId?: number,
		selectNum: number,
		selectNum2: number,
		selectNum3: number,
		selectNum4: number,
		content: string,
		// labels: Array<unknown>,
		pushAnimate: boolean,
		locked: boolean
	}
	export default defineComponent({
		name: 'Appraise'
	})
</script>
<script setup lang="ts">
	const route = useRoute()
	const textarea = ref()
	const router = useRouter()
	const { t: $t } = useI18n()
	const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
	const data: dataT = reactive({
		selectNum: 0,
		selectNum2: 0,
		selectNum3: 0,
		selectNum4: 0,
		content: '',
		// labels: [],
		pushAnimate: false,
		locked: false
	})
	const isShowTips = ref<boolean>(false)
	// 评价页面加载打点
	setLog({
		button: 0,
		action: 'loading',
		result: 1,
		position: 'evaluation'
	})

	onMounted(() => {
		if (route.query.ticketId) {
			data.ticketId = +route.query.ticketId
		} else {
			console.log('缺少重要参数')
		}
		// H5兼容ios12 监听所有input blur
		const inputsElement = [...document.getElementsByTagName('input')]
		inputsElement.forEach(element => {
			if (element) {
				element.onblur = () => {
					setTimeout(() => {
						const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
						window.scrollTo(0, Math.max(scrollHeight - 1, 0))
					}, 300)
				}
			}
		})
	})

	const showChildStar = ref<boolean>(false)
	const selectStar = (num: number) => {
		// data.labels = []
		showChildStar.value = true
		data.selectNum = num + 1
		data.selectNum2 = data.selectNum3 = data.selectNum4 = num < 2 ? 0 : data.selectNum
	}
	const selectStarChild = (num: number, type: number) => {
		data['selectNum' + type] = num + 1
	}
	// const selectLabel = (label: unknown) => {
	// 	const index = data.labels.findIndex((item) => item === label)
	// 	if (index !== -1) {
	// 		data.labels.splice(index, 1)
	// 	} else {
	// 		data.labels.push(label)
	// 	}
	// }
	const handleAppraise = () => {
		// 解决安卓机器点击提交按钮触发键盘弹起的问题
		textarea.value.blur()

		data.pushAnimate = true
		if (data.locked) {
			// '评价提交中\n请稍后'
			Toast($t('text_submiting'))
			return
		}
		if (data.selectNum === 0 || data.selectNum2 === 0 || data.selectNum3 === 0 || data.selectNum4 === 0) {
			Toast($t('text_select_count'))
			return
		}
		// if (data.labels.length === 0) {
		// 	Toast($t('text_select_reason'))
		// 	return
		// }
		const params = {
			ticket_id: data.ticketId,
			appraise: data.selectNum,
			service_rating: data.selectNum2,
      service_time_rating: data.selectNum3,
      service_solution_rating: data.selectNum4,
			// labels: data.labels,
			remark: data.content
		}
		data.locked = true
		appraiseTicket(params).then(() => {
			// 评价成功打点
			setLog({
				button: 1,
				action: 'click',
				result: 1,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			// router.push({
			// 	path: '/detail',
			// 	query: {
			// 		ticketId: data.ticketId,
			// 		from: 'appraise'
			// 	}
			// })
			data.locked = false
			isShowTips.value = true
		}, (res: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			Toast(res)
			data.locked = false
		}).catch((err: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			Toast(err)
			data.locked = false
		})
	}
	const goToHome = (e: Event) => {
		if ((e.target as HTMLElement).localName === 'span') {
			router.push({
				path: '/smart'
			})
		}
	}

	const { selectNum, selectNum2, selectNum3, selectNum4, content, pushAnimate } = toRefs(data)
</script>

<style lang="scss" scoped>
.appraise {
	display: flex;
	align-items: center;
	font-size: 1.75vw;
	.text {
		margin-right: 13.34px;
		text-align: right;
		div {
			word-break: break-all;
			height: 60.03px;
			line-height: 46.69px;
		}
	}
}
.tips-gohome {
	width: 80%;
	margin: 53.36px auto;
	text-align: center;
	bottom: 66.7px;
	word-break: normal;
	font-size: 37.352px;
}
.form-item {
	margin-top: 26.68px;
}
.form-wrapper {
	& ::v-deep(.van-form) {
		font-size: 37.352px;
	}

	& ::v-deep(.van-cell) {
		background-color: rgba(103, 89, 58, 0.3);
		border: 2px solid #bea55f;
		padding: 16px 26.68px;
		text-shadow: none !important;
		color: #E9C86F;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 34.684px;
		color: #E9C86F;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #b6ae9b;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #b6ae9b;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #b6ae9b;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #b6ae9b;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #b6ae9b;
		}
	}

	& ::v-deep(.van-field__right-icon) {
		width: 53.36px;
		padding: 0;
		line-height: 0;

		img {
			width: 100%;
			height: auto;
		}
	}
}
.star-box {
	width: 400.2px;
}
.star {
	display: inline-block;
	width: 73.37px;
	height: 73.37px;
	background: url("~@/assets/img/star.png") no-repeat;
	background-size: cover;
	margin: 0px 2px;
	&.light {
		background: url("~@/assets/img/light-star.png") no-repeat;
		background-size: cover;
	}
}
.label-container {
	margin-left: 26.68px;
	.label {
		width: auto;
		float: left;
		margin: 40px;
		padding: 10.672px 40px;
		color: #867444;
		border: 2Px solid #817042;
		&.select {
			color: #E9C86F;
			border: 2Px solid #E9C86F;
		}
	}
}
@media all and (orientation : portrait) {
	.form-wrapper {
		& ::v-deep(.van-form) {
			font-size: 44px;
		}
		& ::v-deep(.van-field__control) {
			font-size: 42px;
		}
		& ::v-deep(.van-field__right-icon) {
			width: 60px;
		}
		& ::v-deep(.van-cell) {
			border: 2Px solid #817042;
			padding: 16px 30px;

			&::after {
				border-bottom: 0;
			}
		}
	}
	.form-item {
		margin-top: 53.36px;
	}
	.appraise {
		.text {
			div {
				height: 26.68px;
				line-height: 0px;
			}
		}
	}
	.star-box {
		width: 333.5px;
	}
	.star {
		width: 66.7px;
		height: 66.7px;
		background: url("~@/assets/img/star.png") no-repeat;
		background-size: cover;
		margin: 0px 0px;
	}
}
</style>
<style lang="scss">
.tips-gohome span {
  color: #ffffff;
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 0.05rem;
}
</style>
