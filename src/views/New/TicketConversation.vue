<template>
  <div class="bot-page">
    <div class="main">
      <div class="main-role"></div>
      <div class="container article-bg">

        <div class="t-items">
          <div class="title">
            <img src="~@/assets/img/group.png">
            <span>{{ conversationCatInfo.cat_name }}</span>
          </div>
        </div>

        <div class="wrap">
          <div class="scroll-box" ref="contentRef" @touchstart="handleTouchStart">
            <div class="conversation-messages">
              <!-- 用户消息 -->
              <div v-for="(message, index) in messages"
                   :key="index"
                   class="chat-item"
                   :class="message.role === 'user' ? 'user-q' : 'gpt-a'">
                <!-- 用户消息 -->
                <template v-if="message.role === 'user'">
                  <div class="avatar"
                       :class="{ default: !userAvatar }"
                       :style="{ backgroundImage: `url(${userAvatar})` }"></div>
                  <!-- 判断图片 -->
                  <div class="msg msg-picture" v-if="message.question_key === 'picture' && message.content.includes('https://kg-web-cdn')">
                    <div class="img-wrap" v-for="(img, index) in message.content.split(',')" :key="index">
                      <img :src="img" alt="" @click="previewImg(img)">
                    </div>
                  </div>

                  <!-- 否则是文字 -->
                  <div class="msg" v-else>
                    {{ $t(message.content) }}
                  </div>
                </template>

                <!-- 系统消息 -->
                <template v-else>
                  <div class="avatar">
                  </div>
                  <div class="rich-wrap">
                    <div class="rich-wrap-bg"></div>
                    <div class="rich-body">
                       <!-- 判断会话工单提交完成，展示提交信息 -->
                      <div class="answer finish-message" v-if="message.content === finishMessageContent">
                        <div class="finish-message-content">
                          <!-- 感谢您的详细反馈！您的问题已收到，我们<b>会尽快处理</b>，并在完成后<b>立即通知您</b>。您也可以点击此处<span>我的历史服务记录</span>，查看处理进度。以下是您确认的信息要点： -->
                          <div v-html="$t('text_auto_generated_ticket_guidance_detailed')"></div>
                        </div>
                        <div class="divider"></div>
                        <div class="finish-message-form">
                          <!-- 工单详情字段 -->
                          <div v-for="(field, fieldIndex) in ticketFormFields" :key="fieldIndex" class="form-field-item">
                            <div class="field-label">{{ field.label }}：</div>
                            <div class="field-value">
                              <div class="field-img-box" v-if="field.type === 'image'">
                                <div class="field-img-item" v-for="(item, index) in field.value" :key="index">
                                  <img v-if="item.url.includes('https://kg-web-cdn')" :src="item.url" class="field-image" @click="previewImg(item.url)" />
                                  <span v-else>{{ $t(item.url) }}</span>
                                </div>
                              </div>
                              <span v-else v-html="$t(field.value)"></span>
                            </div>
                          </div>
                        </div>
                        <div class="divider"></div>
                        <div class="finish-message-content">
                          <!-- 处理期间我们会<b>及时告知进展</b>，如有其他疑问欢迎随时联系我。感谢您的信任！ -->
                          <div class="finish-message-content-text" v-html="$t('text_auto_generated_ticket_progress_update')"></div>
                        </div>

                        <!-- 点赞点踩 -->
                        <LikeOrDislike :id="currentTicketId" @click="likeClick"></LikeOrDislike>
                      </div>
                      <div v-else class="answer">{{ $t(message.content) }}</div>

                      <!-- 控件区域 -->
                      <template v-if="message.question_key">
                        <!-- 时间选择器 -->
                        <div v-if="message.question_key === 'time'"
                             class="control-wrapper"
                             :class="{ 'disabled': isTimeAnswered(message.question_key) }">
                          <van-field readonly
                                   :disabled="isTimeAnswered(message.question_key)"
                                   clickable
                                   name="datetimePicker"
                                   class="time-field"
                                   right-icon="arrow-down"
                                   :placeholder="$t('text_select_time')"
                                   @click="showTimePicker = true" />
                          <van-popup v-model:show="showTimePicker"
                                   overlay-class="f-overlay"
                                   teleport="body"
                                   position="bottom">
                            <van-datetime-picker :type="filterTimeType(message.question_key)"
                                               v-model="timePickerValue"
                                               @confirm="onTimeConfirm"
                                               @cancel="onTimeCancel">
                              <template #confirm>
                                <!-- 确认 -->
                                {{ $t('btn_confirm') }}
                              </template>
                              <template #cancel>
                                <!-- 取消 -->
                                {{ $t('btn_cancel') }}
                              </template>
                            </van-datetime-picker>
                          </van-popup>
                        </div>

                        <!-- 图片上传 -->
                        <div v-if="message.question_key === 'picture'"
                             class="control-wrapper">
                          <ConversationImageUploadDialog
                            ref="imageUploadRef"
                            @success="onImageUploadSuccess"
                            @remove="onImageRemove"
                            @update:status="onImageStatusUpdate"
                          />
                        </div>
                      </template>
                    </div>
                  </div>
                </template>
              </div>

                <!-- loading -->
                <div class="system-loading" v-if="isInputLoading">
                  <div class="chat-item gpt-a">
                    <div class="avatar">
                      <div class="icon-thinking">
                        <div class="dot dot-1"></div>
                      <div class="dot dot-2"></div>
                      <div class="dot dot-3"></div>
                    </div>
                  </div>

                  <div class="rich-wrap">
                    <div class="rich-wrap-bg"></div>
                    <div class="rich-body">
                        <div class="answer">
                          <span class="answer-text">...</span>
                          <!-- <div class="feather-animation"></div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 本次会话已结束 -->
                <div class="finish-container" v-if="conversationCloseStatus">
                  <div class="finish-container-content">
                    <!-- 本次会话已结束！ -->
                    {{ $t('text_player_stay_page_session_ended') }}
                  </div>
                </div>
            </div>
          </div>
          <div class="icon-down" v-if="isShowBottomBtn" @click="scrollToBottomSmoothly()"></div>
        </div>
      </div>
    </div>
    <div class="conversation-bottom-wrap" v-if="!conversationCloseStatus">
      <ConversationBottomWrap ref="inputRef" @submit="sendMessage" :showInput="showInput" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "TicketConversation",
});
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch, getCurrentInstance } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import { ImagePreview, Toast } from "vant";
import { useScroll, useThrottleFn } from '@vueuse/core';
import { ITEM_TYPES } from '@/enum'
import type { LIKE_TYPES, TChatItem } from '@/enum/types'
import {
  RoleType,
  QuestionKeyType,
  QuestionKeyLang,
  MessageItem,
  QuestionGetListItem,
} from "@/enum/ticketConversation";
import ConversationBottomWrap from "./components/ConversationBottomWrap.vue";
import ConversationImageUploadDialog from "./components/ConversationImageUploadDialog.vue";
import LikeOrDislike from "./components/LikeOrDislike.vue";
import { formatDate } from "@/utils/conversation";
import { useI18n } from "vue-i18n";

const route = useRoute();
const router = useRouter();
const store = useStore();
const { t } = useI18n(); // 获取翻译函数

const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog

// 基础数据
const contentRef = ref<HTMLElement | null>(null);

// 从 store 获取状态
const isShowChat = computed(() => store.state.global.isShowChat)
const userInfo = computed(() => store.state.userInfo)
const isInputLoading = computed(() => store.state.conversation.isInputLoading);
const categoryTitle = computed(() => store.state.conversation.categoryTitle);
const conversationId = computed(() => store.state.conversation.conversationId);
const catId = computed(() => store.state.conversation.catId);
const currentTicketId = computed(() => store.state.conversation.currentTicketId);
const finishMessageContent = computed(() => store.state.conversation.finishMessageContent);
const userAvatar = computed(() => store.state.conversation.userAvatar);
const messages = computed(() => store.state.conversation.messages);
const showInput = computed(() => store.state.conversation.showInput);
const isCompleted = computed(() => store.state.conversation.isCompleted);
const currentQuestionKey = computed(() => store.state.conversation.currentQuestionKey);
const ticketFormFields = computed(() => store.state.conversation.ticketFormFields);
const conversationCatInfo = computed(() => store.state.global.conversationCatInfo);
const conversationCloseStatus = computed(() => store.state.conversation.conversationCloseStatus);

// 控件数据
const inputRef = ref<HTMLElement>();
const imageUploadRef = ref();
const timeValue = computed({
  get: () => store.state.conversation.timeValue,
  set: (value) => store.commit('conversation/SET_TIME_VALUE', value)
});
const timePickerValue = computed({
  get: () => store.state.conversation.timePickerValue,
  set: (value) => store.commit('conversation/SET_TIME_PICKER_VALUE', value)
});
const showTimePicker = computed({
  get: () => store.state.conversation.showTimePicker,
  set: (value) => store.commit('conversation/SET_SHOW_TIME_PICKER', value)
});
const uploadStatus = computed({
  get: () => store.state.conversation.uploadStatus,
  set: (value) => store.commit('conversation/SET_UPLOAD_STATUS', value)
});

// 滚动相关
const isShowBottomBtn = ref(false);
const canScroll = ref(true);

// 简化的滚动处理函数
const scrollToBottomSmoothly = (behavior: ScrollBehavior = 'smooth') => {
  if (!contentRef.value || !canScroll.value) return;

  try {
    const element = contentRef.value as HTMLElement;
    // 使用RAF (requestAnimationFrame) 确保滚动在渲染帧上执行
    const scrollHeight = element.scrollHeight;
    const scrollTop = element.scrollTop;
    // console.log('scrollHeight', scrollHeight);
    // console.log('scrollTop', scrollTop);
    requestAnimationFrame(() => {
      console.log('滚动到底部');
      element.scrollTo({
        top: element.scrollHeight,
        behavior: behavior === 'auto' ? 'auto' : 'smooth'
      });
    });
  } catch (error) {
    console.error('滚动失败:', error);
  }
};

// 触摸事件处理
const handleTouchStart = () => {
  // 触摸开始时，允许用户操作
  canScroll.value = true;
};

// 添加触摸结束事件处理
const handleTouchEnd = () => {
  // 根据需要处理触摸结束逻辑
};

// 监听滚动位置，决定是否显示底部按钮
const { y } = useScroll(contentRef);
watch(y, () => {
  if (contentRef.value) {
    const threshold = contentRef.value.scrollHeight - (contentRef.value.offsetHeight * 2);
    isShowBottomBtn.value = y.value < threshold;
  }
});

// 监听消息列表变化，自动滚动到底部（包含了发送消息后的场景）
watch(messages, async () => {
  // 2. 每次添加信息后滚动到底部
  await nextTick();
  scrollToBottomSmoothly();
}, { deep: true });

// 监听会话关闭状态
watch(conversationCloseStatus, async (newVal) => {
  if (newVal) {
    // 3. 结束状态出现后滚动到底部
    await nextTick();
    scrollToBottomSmoothly();
  }
});

// 添加定时器相关的状态
const inactivityTimer = ref<any>(null);
const warningTimer = ref<any>(null);
const lastActivityTime = ref<number>(Date.now());

// 重置定时器
const resetInactivityTimers = () => {
  // 清除现有定时器
  if (inactivityTimer.value) clearTimeout(inactivityTimer.value);
  if (warningTimer.value) clearTimeout(warningTimer.value);

  // 更新最后活动时间
  lastActivityTime.value = Date.now();

  // 设置1分钟警告定时器
  // 打印倒计时-秒
  const countdown = 10 * 1000;
  const interval = 1000;
  let remainingTime = countdown;

  const updateCountdown = () => {
    const seconds = Math.floor(remainingTime / 1000);
    console.log(`倒计时: ${seconds} 秒`);
    remainingTime -= interval;
    if (remainingTime >= 0) {
      setTimeout(updateCountdown, interval);
    }
  };

  // updateCountdown();

  warningTimer.value = setTimeout(async () => {
    console.log('warningTimer.value', warningTimer.value);
    if (!isCompleted.value && !conversationCloseStatus.value) {
      console.log('发送警告消息');
      // 发送警告消息- 请问，您是否还在线？（5分钟未回复，本次会话将结束）
      await store.dispatch('conversation/sendSystemMessage', t('text_player_stay_page_prompt_online'));
      // 清除定时器
      clearTimeout(warningTimer.value);
    }
  }, 5 * 60 * 1000); // 5分钟

  // 设置5分钟结束会话定时器
  inactivityTimer.value = setTimeout(async () => {
    if (!isCompleted.value && !conversationCloseStatus.value) {
      console.log('发送结束消息');

      // 调用结束会话接口
      try {
        await store.dispatch('conversation/closeConversation');
      } catch (error) {
        console.error('关闭会话失败:', error);
      } finally {
        // 清除定时器
        clearTimeout(inactivityTimer.value);
        clearTimeout(warningTimer.value);
      }
    }
  }, 30 * 60 * 1000); // 30分钟
};

// 监听用户活动
const handleUserActivity = () => {
  if (!isCompleted.value && !conversationCloseStatus.value) {
    resetInactivityTimers();
  }
};

// 初始化
onMounted(async () => {
  const routeCatId = Number(route.query.cat_id) || 0;
  const routeConversationId = (route.query.conversation_id as string) || "";

  // 进入问答页面打点
  // event	事件标识	elfin_load_done
  // position	页面位置标识	ticketConversation
  // from	来源页面（上级页面）	填写具体上级页面名称
  // to	目标页面（当前页面）	填写当前页面名称
  // origin	页面来源类型枚举	1 - 卡片、2 - 智能客服提问、3 - 继续对话
  // result	页面加载结果	success/failed
  setLog({
    event: 'elfin_load_done',
    position: 'ticketConversation',
    from: route.query.from || '',
    to: location.href,
    origin: route.query.origin || '',
    result: 'success',
  })

  await store.dispatch('conversation/initConversation', {
    catId: routeCatId,
    conversationId: routeConversationId
  });

  // 1. 初始化进入页面时滚动到底部
  await nextTick();
  scrollToBottomSmoothly('auto');

  // 初始化定时器
  resetInactivityTimers();

  // android 设备优化
  if (/Android/.test(navigator.userAgent)) {
    // 对安卓设备应用特定优化
    const scrollElement = contentRef.value;
    if (scrollElement) {
      // @ts-ignore 扩展的样式属性
      scrollElement.style.webkitOverflowScrolling = 'touch';
      // @ts-ignore 扩展的样式属性
      scrollElement.style.overflowScrolling = 'touch';
    }
  }
});

// 图片加载后滚动处理
let timer: any;
let timerTimes = 0;
const setInitScroll = () => {
  if (!contentRef.value) return;

  timer = setTimeout(() => {
    timerTimes += 1;
    if (contentRef.value) {
      // 使用更可靠的滚动方式
      scrollToBottomSmoothly('auto');
    }

    // 只在需要的时候继续执行
    if (timerTimes <= 5) {
      setInitScroll();
    } else {
      clearTimeout(timer);
      timerTimes = 0;
    }
  }, 500); // 增加间隔时间，避免频繁触发
};

onUnmounted(() => {
  store.commit('conversation/RESET_STATE');
  clearTimeout(timer);

  // 清除定时器
  if (inactivityTimer.value) clearTimeout(inactivityTimer.value);
  if (warningTimer.value) clearTimeout(warningTimer.value);

  // 移除事件监听器
  document.removeEventListener('mousemove', handleUserActivity);
  document.removeEventListener('keydown', handleUserActivity);
  document.removeEventListener('click', handleUserActivity);
  document.removeEventListener('touchstart', handleUserActivity);
});

// 监听 catId，conversationId，ticketId，如果都有值，则更新 finishMessageContent
watch([catId, conversationId, currentTicketId], () => {
  if (catId.value && conversationId.value && currentTicketId.value) {
    const finishMessageContent = `${catId.value}_${conversationId.value}_${currentTicketId.value}_finish`;
    console.log('finishMessageContent', finishMessageContent);
    store.commit('conversation/SET_FINISH_MESSAGE_CONTENT', finishMessageContent);
  }
});

// 监听会话是否完成
watch(isCompleted, async (newVal, oldVal) => {
  if (newVal && !oldVal) {
    console.log("isCompleted 会话工单信息收集完成, newVal", newVal, "oldVal", oldVal);
    // 会话刚刚完成，检查是否需要提交工单
    await checkAndSubmitTicket();
  }
});

// 监听 currentTicketId，如果存在则获取工单详情
watch(currentTicketId, async (newVal) => {
  if (newVal) {
    console.log('工单ID存在，获取工单详情:', newVal);
    await store.dispatch('conversation/getTicketDetail');
    // 获取finish-message-content中的span，点击跳转
    const finishMessageContentSpan = document.querySelector('.finish-message-content span');
    if (finishMessageContentSpan) {
      finishMessageContentSpan.addEventListener('click', () => {
        router.push({
          path: '/newcs/newHistory'
        })
      })
    }
    // 获取工单详情后，滚动到底部
    await nextTick();
    scrollToBottomSmoothly('auto');
  }
}, { immediate: true });

// 点赞点踩
const likeClick = (type: LIKE_TYPES[keyof LIKE_TYPES]) => {
  console.log('点赞点踩', type)
  setLog({
    event: 'elfin_conversation_appraise',
    position: 'ticketConversation',
    button: type,
    conversation_id: conversationId.value,
    ticket_id: currentTicketId.value,
  })
}

// 图片预览
const previewImg = (img: string): void => {
  ImagePreview({
    images: [img],
    teleport: "body",
    closeable: true,
    showIndex: false,
  });
};

// 检查并提交工单
const checkAndSubmitTicket = async () => {
  try {
    // 检查是否已经有工单ID
    if (currentTicketId.value) {
      console.log('工单已提交，ID:', currentTicketId.value);
      return;
    }

    // 判断工单为无效单的标识
    let isInvalidTicket = 0;

    let uid_key = '';

    // 获取 conversationCatInfo 中的 fields
    let fieldsConfig = [];

    if (conversationCatInfo.value?.fields) {
      try {
        // 解析 fields
        if (typeof conversationCatInfo.value.fields === 'string') {
          fieldsConfig = JSON.parse(conversationCatInfo.value.fields);
        } else if (Array.isArray(conversationCatInfo.value.fields)) {
          fieldsConfig = conversationCatInfo.value.fields;
        }
      } catch (error) {
        console.error('解析 fields 失败:', error);
        fieldsConfig = [];
      }
    }

    // // 获取当前问题列表
    const currentQuestionGetList = store.state.conversation.currentQuestionGetList || [];

    // 组装 fields 字段
    const fieldsData: Record<string, any> = {};

    // 遍历 fieldsConfig，与 currentQuestionGetList 比对
    console.log('fieldsConfig', fieldsConfig);
    console.log('currentQuestionGetList', currentQuestionGetList);
    fieldsConfig.forEach((field: any) => {
      const questionItem = currentQuestionGetList.find(
        (item: any) => item.question_key === field.field_key && item.has_answer
      );

      // 暂未收集到有效信息
      let questionItemAnswer = questionItem?.answer && questionItem.answer !== 'null' ? questionItem.answer : t('text_no_valid_information_collected');

      // 描述与建议，以及活动这三个key，如果得到结果是"暂未收集到有效信息"时，拼接当前field_key下的所有问题回答
      if (field.field_key === QuestionKeyType.description || field.field_key === QuestionKeyType.suggestion || field.field_key === QuestionKeyType.event) {
        if (questionItemAnswer === t('text_no_valid_information_collected')) {
          // isInvalidTicket = 1;
          // 拼接当前field_key下的所有问题回答
          const questionList = messages.value.filter((item: any) => item.question_key === field.field_key);
          console.log('questionList', questionList);
          // 拼接问题回答
          questionItemAnswer = questionList.filter((item: any) => item.role === 'user').map((item: any) => item.content).join('</br>');
          console.log('description/suggestion questionItemAnswer', questionItemAnswer);
        }
      }

      if (questionItem) {
        // 特判 picture 类型
        if (field.field_key === QuestionKeyType.picture && questionItemAnswer) {
          const imgList = questionItemAnswer.split(',') || [];
          fieldsData[field.field_name] = imgList.map((img: string) => ({
            url: img,
            file_type: 'image',
            file_name: img.split('/').pop() || ''
          }));
        } else {
          fieldsData[field.field_name] = questionItemAnswer;
        }
      }

      if (
        (
          field.field_type === ITEM_TYPES.TEXT ||
          field.field_type === ITEM_TYPES.TEXTAREA ||
          field.field_type === ITEM_TYPES.NUMBER
        ) && field.field_map
      ) {
        if (field.field_map === 'role_name') {
          fieldsData[field.field_name] = userInfo.value[field.field_map]
            ? decodeURIComponent(userInfo.value[field.field_map]) : userInfo.value.name
              ? decodeURIComponent(userInfo.value.name) : ''
        } else {
          fieldsData[field.field_name] = userInfo.value[field.field_map as string] ? decodeURIComponent(userInfo.value[field.field_map as string]) : (fieldsData[field.field_name] || '')
          if (field.field_map === 'uid' || field.field_map === 'UID') {
            uid_key = field.field_name
          }
        }
      }
    });

    console.log('组装的 fields 数据:', fieldsData);

    // 提交参数及处理逻辑，参考TicketForm中的submitParams数据
    const params = {
      // is_invalid: isInvalidTicket,
      ticket_type: isShowChat.value,
      conversation_id: conversationId.value,
      origin: 1, // 工单来源 1:玩家
      uid_key: uid_key || '',
      fields:  JSON.stringify(fieldsData) || '' // 参考 "{\"发生时间\":\"2025-03-14\",\"活动名称\":\"活动1\",\"具体问题描述\":\"没看到活动奖励\",\"上传截图\":[{\"file_name\":\"98aca20cec9042b2804c0b1b57cfb5081741933004.png\",\"file_type\":\"image\",\"url\":\"https://kg-web-cdn.kingsgroupgames.com/prod/upload/csimage/pc/98aca20cec9042b2804c0b1b57cfb5081741933004.png\"}],\"问题的建议\":\"无\"}"
    };

    console.log('提交工单参数', params);

    // // 提交工单
    const result = await store.dispatch('conversation/createTicket', params);

    if (result) {
      console.log('工单提交成功，ID:', result?.ticket_id);
    }
  } catch (error) {
    console.error('提交工单失败:', error);
  }
};

// 发送消息
const sendMessage = async (content?: string) => {
  if (!content) {
    return;
  }

  try {
    await store.dispatch('conversation/sendMessage', content);

    // 重置定时器
    resetInactivityTimers();
  } catch (error) {
    console.error("发送消息失败:", error);
  }
};

// 时间确认 - 选择器确认按钮
const onTimeConfirm = async () => {
  const isDateOnly = filterTimeType(currentQuestionKey.value) === 'date';
  const formattedTime = formatDate(timePickerValue.value, isDateOnly);
  await store.dispatch('conversation/handleTimeConfirm', formattedTime);
};

// 时间取消 - 选择器取消按钮
const onTimeCancel = () => {
  store.commit('conversation/SET_SHOW_TIME_PICKER', false);
};

// 图片上传成功回调
const onImageUploadSuccess = async (images: Array<Record<string, string>>) => {
  await store.dispatch('conversation/handleImageUploadSuccess', images);
};

// 图片删除回调
const onImageRemove = (index: number) => {
  // 获取当前图片列表
  const currentImages = imageUploadRef.value?.getImageList() || [];

  store.dispatch('conversation/handleImageRemove', { index, currentImages });
};

// 图片上传状态更新
const onImageStatusUpdate = (status: string) => {
  store.dispatch('conversation/handleImageStatusUpdate', status);
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 手动提交工单
const submitTicket = async () => {
  await checkAndSubmitTicket();
};

// 添加新的方法来判断时间是否已被回答
const isTimeAnswered = (question_key: string) => {
  const currentQuestionGetList = store.state.conversation.currentQuestionGetList;
  const timeQuestion = currentQuestionGetList.find((item: QuestionGetListItem) => item.question_key === question_key);
  if (timeQuestion?.has_answer || conversationCloseStatus.value) {
    store.commit('conversation/SET_SHOW_TIME_PICKER', false);
    return true;
  }
  return false;
};

// 过滤时间类型
const filterTimeType = (question_key: string) => {
  const currentQuestionGetList = store.state.conversation.currentQuestionGetList;
  const timeQuestion = currentQuestionGetList.find((item: QuestionGetListItem) => item.question_key === question_key);
  // console.log('timeQuestion', timeQuestion);
  // only_date true表示仅日期，false表示日期时间
  if (timeQuestion?.field_extend?.only_date) {
    return 'date';
  }
  return 'datetime';
};
</script>

<style lang="scss" scoped>
@mixin writtingCss () {
  position: absolute;
  content: '';
  display: inline-block;
  width: 78px;
  height: 79px;
  background-color: red;
  bottom: 0;
  transform: translateX(-20px) rotateY(45deg);
  @include backgroundSec('soc/icon-pen.png');
  -webkit-box-reflect: below 0px -webkit-linear-gradient(bottom, rgba(255,255,255,0.3) 0%, transparent 40%, transparent 100%);
}

.bot-page {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding-bottom: 118px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
  }

  .main-role {
    height: 531px;
    width: 398px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 1;
    left: -160px;
  }

  .container {
    position: relative;
    width: 907px;
    height: 542px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: -130px;
    box-sizing: border-box;
    margin-bottom: 18px;
    padding: 26px 34px 11px;
    position: relative;
    right: 68px;
    background: url("~@/assets/img/conversation/bg.png") no-repeat center center;
    background-size: 100% 100%;

    .nav-box {
      width: 131px;
      position: absolute;
      top: 40px;
      right: -130px;

      .nav-item {
        box-sizing: border-box;
        width: 100%;
        height: 43px;
        padding: 0px 4px;
        margin-bottom: 10px;
        @include backgroundSec('soc/bg-nav.png');
        text-align: center;
        line-height: 43px;
        font-size: 22px;
        color: #B4CCE4;
        font-family: Source Han Serif CN;
        cursor: pointer;

        &.active-nav {
          color: #1B1F22;
          @include backgroundSec('soc/bg-nav-active.png');
        }
      }
    }

    .wrap {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-start;
    }

    .scroll-box {
      width: 100%;
      height: 100%;
      // padding-bottom: 148px;
      padding-bottom: 60px;
      // margin-bottom: 88px;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }

    .icon-down {
      position: absolute;
      right: 40px;
      bottom: 20px;
      width: 45px;
      height: 45px;
      @include backgroundSec('soc/icon-down.png');
    }
  }
}

.chat-item {
  margin-bottom: 25px;
  .control-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.van-field) {
    width: 300px;
    height: 48px;
    padding: 0;
    background: url("~@/assets/img/conversation/field-btn.png") no-repeat center
      center;
    background-size: 100% 100%;
    .van-cell__value, .van-field__body {
      display: flex;
      width: 100%;
    }
    .van-field__control {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 22px;
      color: #313940;
      text-align: center;
      font-style: normal;
      &::placeholder {
        color: #313940;
      }
    }
    .van-field__right-icon {
      display: none;
    }
  }
}

.avatar {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
}

.system-loading {
  position: relative;
  width: 70px;
  height: 70px;
  margin-bottom: 50px;
  .avatar {
    top: 20px;
    left: 0;
    background-image: url("~@/assets/img/soc/icon-gpt.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
     .icon-thinking {
      position: absolute;
      width: 39px;
      height: 34px;
      @include backgroundSec('soc/icon-thinking.png');
      right: -19px;
      top: -25px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 8px;
      .dot {
        width: 7px;
        height: 4px;
        border: 2px;
        margin: 0 1px;
        background-color: #000000;
        &.dot-1 { animation: dotAnimate1 infinite 1s; }
        &.dot-2 { animation: dotAnimate2 infinite 1s; }
        &.dot-3 { animation: dotAnimate3 infinite 1s; }
      }
    }
  }

   .rich-wrap {
    width: 584px;
    padding: 15px;
    position: relative;
    font-size: 0;

    .rich-body {
      padding: 5px 10px 0;
      position: relative;
      word-break: break-word;

      .question {
        word-break: break-all;
        font-size: 22px;
        font-family: PingFang SC;
        font-weight: normal;
        color: #d3ad5b;
        padding-bottom: 10px;
      }

      .answer {
        font-family: PingFang SC;
        color: #9ABBCD;
        padding-bottom: 1px;
        position: relative;
        font-size: 22px;
        font-weight: 400;
        line-height: 1.3;
        display: flex;
        align-items: center;

        .answer-text {
          font-size: 30px;
          font-weight: 600;
        }

        .feather-animation {
          position: relative;
          width: 40px;
          height: 40px;
          top: -50px;
          margin-left: 50px;
          // background-image: url("~@/assets/img/feather-white.png");
           @include backgroundSec('soc/icon-pen.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          animation: featherSliding 3s ease-in-out infinite;
          @include writtingCss();
        }
      }
    }

    .rich-wrap-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.33);
      background: rgba(24, 37, 47, 1);
      &::after {
        content: "";
        position: absolute;
        top: 20px;
        right: 100%;
        border-style: solid;
        border-color: transparent;
        border-right-width: 22px;
        border-right-color: rgba(24, 37, 47, 1);
        border-top-width: 9px;
        border-top-color: transparent;
        border-bottom-width: 9px;
        border-bottom-color: transparent;
        border-left-width: 0;
      }
    }
  }
}

.user-q {
  min-height: 80px;
  padding: 0 120px;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;

  .avatar.default {
    right: 0;
    top: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url("~@/assets/img/soc/icon-avatar.png")!important;
  }

  .msg {
    word-break: break-all;
    max-height: 100%;
    padding: 20px;
    border-radius: 6px;
    font-size: 22px;
    font-family: PingFang SC;
    background: rgba(172, 193, 222, 1);
    font-weight: 400;
    line-height: 1.3;
    color: #1b1f22;
    position: relative;
    &.msg-picture {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 50%;
      gap: 10px;
      .img-wrap {
        width: 100%;
        height: 100%;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 18px;
      left: 100%;
      border-style: solid;
      border-color: transparent;
      border-left-width: 22px;
      border-left-color: rgba(172, 193, 222, 1);
      border-top-width: 9px;
      border-top-color: transparent;
      border-bottom-width: 9px;
      border-bottom-color: transparent;
      border-right-width: 0;
    }
  }
}

.gpt-a {
  min-height: 100px;
  padding: 18px 120px;
  padding-left: 105px;
  position: relative;

  .avatar {
    top: 20px;
    left: 0;
    background-image: url("~@/assets/img/soc/icon-gpt.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
     .icon-thinking {
      position: absolute;
      width: 39px;
      height: 34px;
      @include backgroundSec('soc/icon-thinking.png');
      right: -19px;
      top: -25px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 8px;
      .dot {
        width: 7px;
        height: 4px;
        border: 2px;
        margin: 0 1px;
        background-color: #000000;
        &.dot-1 { animation: dotAnimate1 infinite 1s; }
        &.dot-2 { animation: dotAnimate2 infinite 1s; }
        &.dot-3 { animation: dotAnimate3 infinite 1s; }
      }
    }
  }

  .rich-wrap {
    width: 584px;
    padding: 15px;
    position: relative;
    font-size: 0;


    .rich-body {
      padding: 5px 10px 0;
      position: relative;
      word-break: break-word;

      .question {
        word-break: break-all;
        font-size: 22px;
        font-family: PingFang SC;
        font-weight: normal;
        color: #d3ad5b;
        padding-bottom: 10px;
      }

      .answer {
        font-family: PingFang SC;
        color: #9ABBCD;
        padding-bottom: 1px;
        position: relative;
        font-size: 22px;
        font-weight: 400;
        line-height: 1.3;
        &.finish-message {
          .finish-message-content {
            .finish-message-content-text {
             :deep(br) {
              display: none;
             }
            }
            :deep(span) {
              color: #d3ad5b;
              text-decoration: underline;
              cursor: pointer;
            }
          }
          .wrap {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex-direction: row;
          }
        }
      }

      .divider {
        width: 100%;
        height: 2px;
        margin: 20px 0;
        background-image: url("~@/assets/img/soc/bg-line.png");
        background-size: contain;
      }

      .control-wrapper {
        margin: 20px 0 15px 0;

        .control-label {
          font-size: 18px;
          color: #d3ad5b;
          margin-bottom: 10px;
        }

        .submit-btn {
          margin-top: 10px;
          padding: 8px 15px;
          background-color: #d4ad5b;
          color: #333;
          text-align: center;
          border-radius: 4px;
          font-size: 16px;
          cursor: pointer;
          display: inline-block;
        }
      }

      :deep(.van-field) {
        background-color: transparent;

        .van-field__control {
          color: #313940;
        }
      }

      :deep(.van-uploader) {
        .van-uploader__upload {
          background-color: #2d2c2b;
          color: #313940;
        }
      }
    }

    .rich-wrap-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.33);
      background: rgba(24, 37, 47, 1);

      &::after {
        content: "";
        position: absolute;
        top: 20px;
        right: 100%;
        border-style: solid;
        border-color: transparent;
        border-right-width: 22px;
        border-right-color: rgba(24, 37, 47, 1);
        border-top-width: 9px;
        border-top-color: transparent;
        border-bottom-width: 9px;
        border-bottom-color: transparent;
        border-left-width: 0;
      }
    }
  }
}

@media all and (orientation: portrait) {
  .bot-page {
    .main {
      margin-top: calc(env(safe-area-inset-top) + var(--head-bar-height) + 75px) !important;
    }
    .main-role {
      display: none;
    }
    .container {
      margin: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 30px;
      right: 0;
      .nav-box {
        display: flex;
        width: 100%;
        position: absolute;
        top: -60px;
        left: 10px;
        .nav-item {
          box-sizing: border-box;
          width: 135px;
          height: 43px;
          padding: 0px 4px;
          margin-left: 20px;
          @include backgroundSec('soc/bg-nav.png');
          text-align: center;
          line-height: 43px;
          font-size: 24px;
          color: #B4CCE4;
          font-family: Source Han Serif CN;
          cursor: pointer;
        }
      }
    }
  }

  .gpt-a {
    .rich-wrap {
      width: 70vw;
    }
  }
}

// 底部输入区域样式
.bot-page :deep(.conversation-bottom-wrap) {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 118px;
  width: 100%;
  padding: 0 10px 0 30px;
  z-index: 2;
  @include backgroundSec("bg-bottom.jpg");

  .van-field {
    padding: 0;
    background-color: transparent;

    .van-field__control {
      height: 57px;
      display: flex;
      flex: 1;
      border: 0;
      outline: none;
      font-size: 20px;
      line-height: 57px;
      color: #65889b;
      @include backgroundSec("bg-input.png");
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .van-field__button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      width: 150px;
    }

    .van-button {
      height: 50px;
      max-width: 100%;
      @include backgroundSec("bg-btn-send.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center center;
      margin-left: 15px;
      line-height: 48px;
      text-align: center;
      font-family: EmergeBFW01-Regular;
      color: #1b1f22;
      font-weight: bold;
      font-size: 30px;
    }

    .van-button__content {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      span {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 20px;
      }
    }
  }
}

.submit-ticket-btn-wrapper {
  display: flex;
  justify-content: center;
  margin: 30px 0;

  .submit-ticket-btn {
    display: inline-block;
    padding: 10px 30px;
    background: url("~@/assets/img/conversation/field-btn.png") no-repeat center center;
    background-size: 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 22px;
    color: #313940;
    text-align: center;
    cursor: pointer;
  }
}

.finish-message-form {
  padding: 10px 0;

  .form-field-item {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    &:last-child {
      margin-bottom: 0;
    }

    .field-label {
      display: flex;
      max-width: 100%;
      flex-shrink: 0;
      font-size: 22px;
      // margin-bottom: 8px;
    }

    .field-value {
      font-size: 22px;
      color: #9abbcd;
      word-break: break-all;

      .field-img-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        .field-img-item {
          // margin-top: 5px;
          overflow: hidden;
          img {
            width: 100px;
            height: 100px;
            border-radius: 4px;
            object-fit: cover;
          }
        }
      }

      .field-image {
        max-width: 100%;
        max-height: 200px;
        border-radius: 4px;
      }
    }
  }
}

.input-loading {
  // position: absolute;
  // top: -10px;
  // left: 50%;
  // transform: translateX(-50%);
  width: 100%;
  margin-bottom: 50px;
  .loading-box {
    .loading-icon {
      display: inline-block;
      width: 40px;
      height: 40px;
      margin-right: 5px;
    }
    .loading-text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 22px;
      color: #5F7987;
      line-height: 30px;
      text-align: left;
      font-style: normal;
      text-align: center;
      // transform: scale(0.8);
    }
  }
}

// 移动端

@media all and (orientation: portrait) {
  .input-loading {
    top: -45px;
  }
}


.bot-page .container {
  padding-top: 100px;
  padding-bottom: 110px;
}

.control-wrapper {
  &.disabled {
    pointer-events: none;
    filter: grayscale(100%);
    :deep(.van-field__control) {
      color: #313940;
      -webkit-text-fill-color: #313940;
    }
  }
}

@keyframes dotAnimate1 {
  0% { background-color: rgba(0, 0, 0, 1); }
  50% { background-color: rgba(0, 0, 0, 0); }
  99% { background-color: rgba(0, 0, 0, 1); }
}
@keyframes dotAnimate2 {
  0% { background-color: rgba(0, 0, 0, .5); }
  25% { background-color: rgba(0, 0, 0, 1); }
  75% { background-color: rgba(0, 0, 0, 0); }
  100% { background-color: rgba(0, 0, 0, .5); }
}
@keyframes dotAnimate3 {
  0% { background-color: rgba(0, 0, 0, 0); }
  50% { background-color: rgba(0, 0, 0, 1); }
  100% { background-color: rgba(0, 0, 0, 0); }
}

@keyframes featherWriting {
  0% {
    transform: translateY(-2px) rotate(-10deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(2px) rotate(10deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-2px) rotate(-10deg);
    opacity: 0.7;
  }
}

@keyframes featherSliding {
  0% {
    transform: translateX(-60px) rotateY(45deg);
    opacity: 0.7;
  }
  40% {
    transform: translateX(-40px) rotateY(45deg);
    opacity: 1;
  }
  60% {
    transform: translateX(-30px) rotateY(45deg);
    opacity: 1;
  }
  100% {
    transform: translateX(-20px) rotateY(45deg);
    opacity: 0.7;
  }
}

</style>

<style lang="scss" scoped>
.t-items {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0 34px;
}
.title {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  line-height: 30px;
  color: #cecfc9;
  font-size: 24px;
  border-radius: 2px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  img {
    display: block;
    height: 30px;
    width: 30px;
    float: left;
    opacity: 0.8;
    margin-right: 6px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 55px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.list-item {
  color: #E9C86F;
  overflow: auto;
  font-size: 22px;
  line-height: 35px;
  vertical-align: middle;
  margin: 20px auto;
  cursor: pointer;
  img {
    display: block;
    height: 35px;
    width: 35px;
    float: left;
    margin-right: 5px;
  }
}

.finish-container {
  width: 100%;
  height: 100%;
  margin: 30px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  .finish-container-content {
    font-size: 22px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 1.3;
    color: #a2a8ae;
  }
}
</style>
