<template>
  <div class="answers-detail">
    <div class="main-role"></div>
    <div class="content-body article-bg">
      <template v-if="stateDetail.art_title">
        <div class="article-title" :style="{textAlign: (route.query.card_group === '2'?'left':'center')}">{{ stateDetail.art_title }}</div>
        <div class="divider"></div>
        <div class="article-body">
          <div class="scroll">
            <RichText :msg="stateDetail.art_content" source-type="article"></RichText>
            <span v-if="stateDetail.ticket_cat_id > 0" @click="goTickets(stateDetail.ticket_cat_id)" class="tickets_enter">{{ $t('text_submit_cstickets') }}</span>
            <div class="bottom-line"></div>
            <LikeOrDislike v-if="!(stateDetail.ticket_cat_id > 0)" class="like-wrap" :id="id" @click="likeClick"></LikeOrDislike>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, getCurrentInstance } from 'vue'
import { networkerror } from '@/utils/networkerror'
import { useRoute, useRouter, RouteLocationNormalized } from 'vue-router'
import { useStore } from 'vuex'
import { Toast } from 'vant'
import { getArticleDetail } from '@/api/new'
export default defineComponent({
  name: 'ArticleDetail'
})
</script>
<script setup lang="ts">
import RichText from './components/RichText.vue'
import LikeOrDislike from './components/LikeOrDislike.vue'
// import { LIKE_CONF } from '@/enum'
import type { LIKE_TYPES } from '@/enum/types'
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
setLog({
	event: 'elfin_load_done',
  position: 'article',
  result: 'success'
})
const { commit } = useStore()
const route = useRoute()
const id = route.query.id as string
const stateDetail = ref({
  art_title: '',
  art_content: '',
  ticket_cat_id: 0
})
const getDetail = () => {
  commit('setLoadingCount', 1)
  getArticleDetail({
    art_id: route.query.card_group === '2' ? Number(route.query.art_id) : Number(id)
  }).then((res: Record<string, unknown>) => {
    stateDetail.value = { ...stateDetail.value, ...res}
  }).catch((err: string) => {
    Toast(err)
    networkerror.show(getDetail)
  }).finally(() => commit('setLoadingCount', 0))
}
getDetail()

const likeClick = (type: LIKE_TYPES[keyof LIKE_TYPES]) => {
  // todo 赞踩接口
  // pushLog({
  //   event: type === LIKE_CONF.like ? 'funbot_thumbup' : 'funbot_thumbdown',
  //   type: 'article',
  //   answer_mode: 'article',
  //   content: {
  //     headline: stateDetail.value.art_title,
  //     url: window.location.href
  //   },
  //   if_effective: 1
  // })
  setLog({
    event: 'elfin_appraise',
    position: 'article',
    button: type,
    art_id: id
  })
}
const goTickets = (id: number) => {
  router.push({
    path: '/newcs/answersDetail',
    query: {
      cat_id: id
    }
  })
}
const router = useRouter()
router.beforeResolve((to: RouteLocationNormalized, from: RouteLocationNormalized) => {
  if (from.name === 'ArticleDetail') {
    networkerror.hide && networkerror.hide()
  }
})
</script>

<style lang="scss" scoped>
.article-title {
  font-size: 24px;
  font-family: Source Han Serif CN;
  font-weight: 400;
  text-align: center;
  margin-bottom: 23px;
  color: #D4AD5B;
  text-shadow: 0px 1px 0px rgba(0,0,0,0.6);
}
.divider {
  width: 100%;
  height: 2px;
  margin: 4px 0 10px;
  @include backgroundSec('soc/bg-line.png');
}
.article-body {
  flex: 1;
  overflow: hidden;
  margin-top: 16px;
}
.scroll {
  height: 100%;
  width: 100%;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
.bottom-line {
  width: 100%;
  height: 2px;
  margin: 4px 0 50px;
  @include backgroundSec('soc/bg-line.png');
}
.like-wrap {
  margin-top: 20px;
}
.tickets_enter {
  font-size: 26px;
  text-decoration: underline;
  color: #F5C133;
}
</style>