<template>
  <div class="conversation-image-upload">
    <div class="upload-field"
         :class="{ 'disabled': isPictureAnswered }"
         @click.stop="showDialog">
      <span>{{ uploading ? $t("text_uploading") + ' ...' : $t('text_upload_image') }}</span>
    </div>

     <div v-if="showSkipButton && showSkipButtonRemark && !isPictureAnswered"
               class="skip-btn"
               @click="handleSkip">{{ $t('text_skip') }}</div>

    <!-- 上传弹窗 - 使用van-popup -->
    <van-popup
      class="upload-dialog-container"
      v-model:show="dialogVisible"
      teleport="body"
      position="top"
      close-on-click-overlay
      @click-overlay="closeDialog"
    >
      <div class="dialog-content">
        <div class="dialog-header">
          <span>{{ $t('text_upload_image') }}</span>
          <div class="close-btn"
               @click="closeDialog"></div>
        </div>
        <div class="dialog-body">
          <div class="image-grid">
            <div v-for="(img, index) in imgList"
                 :key="index"
                 class="image-item">
              <!-- IMG URL不存在时，先展示LOADING -->
              <div v-if="data.uploading"
                   class="loading-icon">
              </div>
              <img v-else :src="img.url" @click="previewImg(img)" />
              <div class="delete-icon"
                   @click.stop="removeUploadedImg(index)"></div>
            </div>
            <div v-if="imgList.length < maxCount"
                 class="upload-btn"
                 @click="handleUploadClick">
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <div class="confirm-btn"
               @click="confirmUpload">{{ $t('btn_confirm') }}</div>
        </div>
      </div>
    </van-popup>

    <!-- SDK上传 - 隐藏但可触发 -->
    <div v-if="!uploading && !isPictureAnswered && imgList.length < maxCount && !isH5Upload && !browserIsPC"
         class="sdk-upload"
         ref="sdkUploadRef">
    </div>

    <!-- H5上传 - 隐藏但可触发 -->
    <div v-if="(isH5Upload || browserIsPC) && !isPictureAnswered && !uploading && imgList.length < maxCount"
         class="h5-upload">
      <van-uploader :after-read="afterRead"
                    :result-type="'file'"
                    accept="image/*"
                    :max-count="maxCount"
                    :multiple="true"
                    ref="h5UploadRef" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import LoadingWrap from "@/components/loadingWrap";

export default defineComponent({
  name: "ConversationImageUploadDialog",
  components: { LoadingWrap },
});
</script>

<script setup lang="ts">
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  computed,
  onBeforeUnmount,
  defineProps,
  withDefaults,
  defineEmits,
} from "vue";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { ImagePreview, Toast } from "vant";
import { uploadSever } from "@/utils/upload";
import { browser } from "@/utils";
import { QuestionGetListItem } from "@/enum/ticketConversation";

// eslint-disable-next-line
const { proxy: _this, appContext } = getCurrentInstance() as any;
const { commit, state, dispatch } = useStore();
const { t: $t } = useI18n();

// 判断是否显示跳过按钮
const showSkipButtonRemark = ref(true);
const showSkipButton = computed(() => {
  const currentQuestionGetList = state.conversation.currentQuestionGetList;
  const pictureQuestion = currentQuestionGetList.find(
    (item: QuestionGetListItem) => item.question_key === "picture"
  );
  console.log("showSkipButton pictureQuestion", pictureQuestion);
  return pictureQuestion && !pictureQuestion.is_required;
});

const conversationCloseStatus = computed(() => state.conversation.conversationCloseStatus);

// 浏览器环境检测
const browserIsPC = browser.version.isWindows || false;

// WEBVIEW环境检测
const webviewIsPC = new URLSearchParams(window.location.search).get('isWebview') || true; // todo, 先写死，后续接入sdk时再处理

// 判断图片问题是否已回答
const isPictureAnswered = computed(() => {
  const currentQuestionGetList = state.conversation.currentQuestionGetList;
  const pictureQuestion = currentQuestionGetList.find(
    (item: QuestionGetListItem) => item.question_key === "picture"
  );
  console.log("isPictureAnswered pictureQuestion", pictureQuestion);
  console.log("isPictureAnswered conversationCloseStatus", conversationCloseStatus.value);
  return pictureQuestion?.has_answer || conversationCloseStatus.value || false;
});

// 上传状态机
const isUploading = computed(() => state.uploading);

// 组件属性
const props = withDefaults(
  defineProps<{
    isH5Upload?: boolean;
    maxCount?: number;
  }>(),
  {
    isH5Upload: false,
    maxCount: 5,
  }
);

// 组件事件
const emit = defineEmits<{
  (event: "success", images: Array<Record<string, string>>): void;
  (event: "remove", index: number): void;
  (event: "update:status", status: string): void;
}>();

// 组件状态
const data = reactive({
  uploading: false,
  failed: false,
  imgList: [] as Array<Record<string, string>>,
});

// 上传组件引用
const h5UploadRef = ref();
const sdkUploadRef = ref();

// SDK交互初始化
const initSDKCallback = () => {
  window.chooseFinish = (params) => {
    chooseFinish(params);
  };
  window.backImgUrl = (params: string): void => {
    uploadFinish(params);
  };
};

// 组件销毁要更改状态机
onBeforeUnmount(() => {
  data.uploading = false;
  commit("setUploadingType", false); // 同步上传状态机
});

// 上传服务器地址配置
const uploadUrl: Record<string, string> = {
  "fpcs-web-test.funplus.com": "https://upload-api-test.funplus.com",
  "fpcs-web-stage.funplus.com": "https://upload-api-test.funplus.com",
  "fpcs-web.funplus.com": "https://upload-global.funplus.com",
  "fpcs-web-test.funplus.com.cn": "https://upload-api-test.funplus.com.cn",
  "fpcs-web-stage.funplus.com.cn": "https://upload-api-test.funplus.com.cn",
  "fpcs-web.funplus.com.cn": "https://upload-api.funplus.com.cn",
  "fpcs-web-tx.kingsgroup.cn": "https://upload-global.funplus.com",
  "fpcs-web-tx.yoo-mei.cn": "https://upload-global.funplus.com",
  "fpcs-web.nenglianghe.cn": "https://upload-api.funplus.com.cn",
};

// 弹窗状态
const dialogVisible = ref(false);

// 显示弹窗
const showDialog = () => {
  if (isPictureAnswered.value) {
    return;
  }
  dialogVisible.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  data.imgList = [];
};

// 跳过当前问题
const handleSkip = () => {
  dialogVisible.value = false;
  showSkipButtonRemark.value = false;
  dispatch('conversation/sendSkipMessage', $t('text_nodata'));
};

// 确认上传
const confirmUpload = () => {
  dialogVisible.value = false;
  emit("success", data.imgList);
};

// 修改原有的handleUploadClick方法
const handleUploadClick = () => {
  if (isPictureAnswered.value) {
    return;
  }
  showUploadOptions();
};

const showUploadOptions = () => {
  // if (data.uploading) {
  //   Toast($t('text_uploading'))
  //   return
  // }

  // if (data.imgList.length >= props.maxCount) {
  //   Toast(`最多上传${props.maxCount}张图片`)
  //   return
  // }

  if (props.isH5Upload || browserIsPC) {
    // 触发H5上传
    h5UploadRef.value.$el.querySelector("input").click();
  } else {
    // 触发SDK上传
    uploadImg();
  }
};

// H5上传
const afterRead = (
  file: Record<string, unknown> | Array<Record<string, unknown>>
) => {
  data.uploading = true;
  emit("update:status", "uploading");
  commit("setUploadingType", true);

  // 处理多文件上传
  if (Array.isArray(file)) {
    const validFiles: string[] = [];
    // 验证每个文件类型
    for (const f of file) {
      const fileObj = f.file as File;
      if (fileObj && validateImageFile(fileObj)) {
        validFiles.push(f.file as string);
      } else {
        Toast($t('text_upload_failed'));
      }
    }

    if (validFiles.length === 0) {
      data.uploading = false;
      commit("setUploadingType", false);
      return;
    }

    uploadSever(
      validFiles,
      "https://upload-global.funplus.com/api/storage/put"
    ).then(
      (res: []) => {
        data.imgList.push(...res);
        _this.$forceUpdate();
        emit("update:status", `已上传${data.imgList.length}张图片`);

        // 重置上传状态机
        data.uploading = false;
        commit("setUploadingType", false);
      },
      (err) => {
        console.log("failed", err);
        data.failed = true;
        emit("update:status", "failed");
        data.uploading = false;
        commit("setUploadingType", false);
      }
    );
  } else {
    // 兼容单文件上传
    const fileObj = file.file as File;
    if (!validateImageFile(fileObj)) {
      Toast($t('text_upload_failed'));
      data.uploading = false;
      commit("setUploadingType", false);
      return;
    }

    uploadSever(
      file.file as string,
      "https://upload-global.funplus.com/api/storage/put"
    ).then(
      (res: []) => {
        data.imgList.push(...res);
        _this.$forceUpdate();
        emit("update:status", `已上传${data.imgList.length}张图片`);

        // 重置上传状态机
        data.uploading = false;
        commit("setUploadingType", false);
      },
      (err) => {
        console.log("failed", err);
        data.failed = true;
        emit("update:status", "failed");
        data.uploading = false;
        commit("setUploadingType", false);
      }
    );
  }
};

// 验证文件是否为图片类型
const validateImageFile = (file: File): boolean => {
  // 检查文件的MIME类型
  if (!file.type.startsWith('image/')) {
    return false;
  }

  // 检查文件扩展名（双重验证）
  const fileName = file.name.toLowerCase();
  const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
  return validExtensions.some(ext => fileName.endsWith(ext));
};

// 上传图片(SDK)
const uploadImg = () => {
  // 如果上传流程被占用，则提示上传中
  if (isUploading.value) {
    Toast($t("text_uploading"));
  } else {
    initSDKCallback();
    const path = "openFile";
    const count = props.maxCount - data.imgList.length;
    const param = {
      count: count < 5 ? count : 5,
      albumType: 1,
      storage: 1,
      fileUploadUrl: uploadUrl[location.host]
        ? uploadUrl[location.host]
        : "https://upload-global.funplus.com",
    };
    appContext.config.globalProperties.$utils.jsBridge(path, param);
  }
};

// SDK选择图片后callback
const chooseFinish = (params: unknown): void => {
  if (params) {
    commit("setUploadingType", true); // 同步上传状态机
    data.uploading = true;
    emit("update:status", "uploading");
  }
};

// SDK上传完成callback
const uploadFinish = (params: string): void => {
  if (JSON.parse(params).code === 0) {
    data.imgList.push(...JSON.parse(params).data);
    _this.$forceUpdate();
    // emit("success", JSON.parse(params).data);
    emit("update:status", `已上传${data.imgList.length}张图片`);
  } else {
    data.failed = true;
    emit("update:status", "failed");
  }
  data.uploading = false;
  commit("setUploadingType", false); // 同步上传状态机
};

// 图片预览
const previewImg = (img: Record<string, string>): void => {
  ImagePreview({
    images: [img.url],
    teleport: "body",
    closeable: props.isH5Upload,
    showIndex: false,
  });
};

// 删除图片
const removeUploadedImg = (index: number): void => {
  data.imgList.splice(index, 1);
  emit("remove", index);
  emit(
    "update:status",
    data.imgList.length > 0 ? `已上传${data.imgList.length}张图片` : ""
  );
};

// 删除上传失败的图片
const removeFailedImg = () => {
  data.failed = false;
  emit("update:status", "");
};

// 获取上传图片列表
const getImageList = () => {
  return data.imgList;
};

// 暴露方法给父组件
defineExpose({
  getImageList,
});

const { uploading, failed, imgList } = toRefs(data);
</script>

<style lang="scss" scoped>
.conversation-image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;

  .skip-btn {
    position: absolute;
    bottom: -15px;
    right: 0;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 22px;
    color: #99A5B2;
    line-height: 30px;
    text-align: justify;
    font-style: normal;
    text-decoration-line: underline;
    cursor: pointer;
  }

  .upload-field {
    width: 300px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("~@/assets/img/conversation/field-btn.png") no-repeat center
      center;
    background-size: 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 22px;
    color: #313940;
    text-align: center;
    font-style: normal;
    cursor: pointer;
    margin-bottom: 10px;

    &.disabled {
      filter: grayscale(100%);
      :deep(.van-field__control) {
        color: #313940;
        -webkit-text-fill-color: #313940;
      }
    }
  }

  .image-preview {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
    width: 100%;

    .image-item {
      position: relative;
      margin: 0 10px 10px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .delete-icon {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 22px;
        height: 22px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &::before,
        &::after {
          content: "";
          position: absolute;
          width: 12px;
          height: 2px;
          background-color: #fff;
          transform: rotate(45deg);
        }

        &::after {
          transform: rotate(-45deg);
        }
      }
    }
  }

  .upload-status,
  .upload-failed {
    margin-top: 10px;
    font-size: 14px;
    color: #9abbcd;
  }

  .upload-failed {
    color: #ff4d4f;
    display: flex;
    align-items: center;

    .van-icon {
      margin-right: 5px;
    }

    .delete-icon {
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .sdk-upload,
  .h5-upload {
    display: none;
  }

  :deep(.van-uploader__input-wrapper) {
    display: none;
  }
}

// 使用van-popup代替自定义弹窗
.upload-dialog-container {

  .dialog-content {
    width: 690px;
    height: 638px;
    background: url("~@/assets/img/conversation/upload-dialog-bg.png")
      no-repeat;
    background-size: 100% 100%;
    border-radius: 8px;
    overflow: hidden;
    transform: scale(0.85) translateY(-7%);
  }

  .dialog-header {
    height: 80px;
    background: url("~@/assets/img/conversation/upload-dialog-title-bg.png")
      no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;

    span {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #e2c885;
      line-height: 33px;
      text-align: justify;
      font-style: normal;
    }

    .close-btn {
      width: 40px;
      height: 40px;
      background: url("~@/assets/img/conversation/upload-dialog-close.png")
        no-repeat;
      background-size: contain;
      cursor: pointer;
    }
  }

  .dialog-body {
    height: 450px;
    padding: 42px 37px;
    min-height: 200px;

    .image-grid {
      display: flex;
      gap: 17px;
      flex-wrap: wrap;
      .image-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 194px;
        height: 194px;
        border-radius: 8px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .delete-icon {
          position: absolute;
          top: 5px;
          right: 5px;
          width: 22px;
          height: 22px;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          background-size: contain;
          cursor: pointer;
           &::before,
            &::after {
              content: "";
              position: absolute;
              width: 12px;
              height: 2px;
              background-color: #CFEEFF;
              transform: rotate(45deg);
            }

            &::after {
              transform: rotate(-45deg);
            }
        }
      }

      .upload-btn {
        position: relative;
        width: 194px;
        height: 194px;
        background: rgba(0, 0, 0, 0.3);
        cursor: pointer;

        &::before {
          content: "";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 92px;
          height: 95px;
          background: url("~@/assets/img/conversation/upload-dialog-plus.png")
            no-repeat;
          background-size: contain;
        }
      }
    }
  }

  .dialog-footer {
    padding: 20px;
    display: flex;
    justify-content: center;

    .confirm-btn {
      width: 200px;
      height: 60px;
      background: url("~@/assets/img/conversation/upload-dialog-btn.png")
        no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      color: #313940;
      cursor: pointer;
    }
  }
}

@media all and (orientation: portrait) {
  .conversation-image-upload {
    .image-preview {
      .image-item {
        width: 60px;
        height: 60px;
      }
    }
  }
}
</style>

<style lang="scss">
.upload-dialog-container {
  height: 100% !important;
  border-radius: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
