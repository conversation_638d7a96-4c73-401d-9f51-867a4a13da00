<template>
  <div class="t-items">
    <div class="title">
			<img src="~@/assets/img/group.png">
			<!-- 请选择问题分类 / 请选择xxx的细分问题分类 -->
			<span>{{ itemData.fields.desc }}</span>
		</div>
    <van-form class="form-wrapper">
      <van-field
        class="form-item"
        v-model="submitData"
        :disabled="hasClick"
        autofocus
      />
    </van-form>
    <div class="submit-btn" @click.stop="onSubmit">{{ $t("text_submit") }}</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs, defineExpose } from 'vue'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
interface dataT {
  submitData: string,
  pushAnimate: boolean,
  hasClick: boolean
}
export default defineComponent({
	name: 'AutoUserInput'
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const data: dataT = reactive({
  submitData: '',
  pushAnimate: false,
  hasClick: false
})
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const emit = defineEmits<{
  (event: 'item', itemInfo: autoFlowDataT): void
}>()
const onSubmit = () => {
  if (!data.submitData) {
    return
  }
  if (data.hasClick) {
    Toast($t("auto_only_text"))
    return
  }
  // 按钮按下
  data.hasClick = true
	data.pushAnimate = true
  const itemData = Object.assign({}, props.itemData)
  itemData.fields.value = data.submitData
  emit('item', itemData)
}
const reset = () => {
  data.submitData = ''
  data.hasClick = false
}
defineExpose({
  reset
})
const { submitData, pushAnimate, hasClick } = toRefs(data)
</script>

<style lang="scss" scoped>
.title {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  line-height: 30px;
  color: #cecfc9;
  font-size: 24px;
  border-radius: 2px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  margin-bottom: 15px;
  img {
    display: block;
    height: 30px;
    width: 30px;
    float: left;
    opacity: 0.8;
    margin-right: 6px;
  }
}
// .title::after {
//   content: '';
//   position: absolute;
//   left: -2Px;
//   opacity: 0.6;
//   top: 2Px;
//   height: 2Px;
//   z-index: 1;
//   width: 96px;
//   background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
// }
// .title::before {
//   content: '';
//   position: absolute;
//   left: 2Px;
//   opacity: 0.6;
//   top: -2Px;
//   height: 55px;
//   z-index: 1;
//   width: 2Px;
//   background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
// }
.form-wrapper {
  & ::v-deep(.van-form) {
		font-size: 22px;
	}
	& ::v-deep(.van-cell) {
		background: rgba(0, 0, 0, 0.2);
		border: 2px solid #5f5f58 !important;
		padding: 10px 20px;
		text-shadow: none;
		color: #E9C86F;
    border-radius: 2px;
		&::after {
			border-bottom: 0;
		}
	}
	& ::v-deep(.van-field__control) {
		font-size: 22px;
		color: #E9C86F;
    text-align: center;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #b6ae9b;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #b6ae9b;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #b6ae9b;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #b6ae9b;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #b6ae9b;
		}
	}
	& ::v-deep(.van-field__right-icon) {
		width: 50px;
		padding: 0;
		line-height: 0;
		img {
			width: 100%;
			height: auto;
		}
	}
}
.disabled_btn {
  opacity: 0.4;
}
</style>