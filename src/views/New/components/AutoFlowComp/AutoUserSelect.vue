<template>
  <div class="t-items">
		<div class="title">
			<img src="~@/assets/img/group.png">
			<!-- 请选择问题分类 / 请选择xxx的细分问题分类 -->
			<span>{{ itemData.fields.desc }}</span>
		</div>
		<div class="list">
			<div :class="['list-item', hasSelect && activeKey === k ? 'hasSelected' : '']" v-for="(v, k) in itemData.fields.list" :key="k">
				<div style="float:left" @click="listClickHandle(v, k)">
					<img  v-if="hasSelect && activeKey === k" src="~@/assets/img/point_selected.png"><img v-else src="~@/assets/img/point.png"><span>{{ v.content }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
interface dataT {
  hasSelect: boolean,
  activeKey: number
}
export default defineComponent({
	name: 'AutoUserSelect'
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const data: dataT = reactive({
  hasSelect: false,
  activeKey: -1
})
const emit = defineEmits<{
  (event: 'item', itemInfo: Record<string, unknown>): void
}>()
const listClickHandle = (item: Record<string, unknown>, activeKey: number) => {
  if (data.hasSelect) {
    if (activeKey !== data.activeKey) {
      Toast($t('auto_only_text'))
    }
    return
  }
  data.hasSelect = true
  data.activeKey = activeKey
  emit('item', {
    ...props.itemData,
    fields: item
  })
}
const { hasSelect, activeKey } = toRefs(data)
</script>

<style lang="scss" scoped>
.title {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  line-height: 30px;
  color: #cecfc9;
  font-size: 24px;
  border-radius: 2px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  img {
    display: block;
    height: 30px;
    width: 30px;
    float: left;
    opacity: 0.8;
    margin-right: 6px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 55px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.list-item {
  color: #E9C86F;
  overflow: auto;
  font-size: 22px;
  line-height: 35px;
  vertical-align: middle;
  margin: 20px auto;
  cursor: pointer;
  img {
    display: block;
    height: 35px;
    width: 35px;
    float: left;
    margin-right: 5px;
  }
}
.hasSelected {
  color: #afafad;
}
</style>