<template>
  <div class="t-items">
    <dialog-wrapper>
      <div class="richtext_box">
        <div class="richtext_tickets rich_content" v-html="itemData.fields.desc"></div> <span @click.stop="clickHandle" :class="['tickets_enter', hasClick ? 'disabled' : '']">{{ $t('text_submit_cstickets') }}</span>
      </div>
    </dialog-wrapper>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs } from 'vue'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
export default defineComponent({
	name: 'AutoTickets'
})
</script>
<script setup lang="ts">
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const data: {
  hasClick: boolean
} = reactive({
  hasClick: false
})
const emit = defineEmits<{
  (event: 'auto-to-ticket', itemInfo: Record<string, unknown>): void
}>()
const clickHandle = (item: Record<string, unknown>) => {
  if (data.hasClick) return
  data.hasClick = true
  emit('auto-to-ticket', { ...props.itemData })
}
const { hasClick } = toRefs(data)
</script>

<style lang="scss" scoped>
.t-items {
  & ::v-deep .dialog_wrapper {
    max-width: 100%;
    width: 100%;
    margin-top: 0px;
    margin-bottom: 0.2rem;
  }
}
.richtext_box {
  font-size: 22px;
  box-sizing: border-box;
  & ::v-deep img {
    max-width: 100%;
    margin: 10px 0px;
    display: block;
  }
  & ::v-deep .tickets_enter {
    text-decoration: underline;
    color: #F5C133;
  }
  .disabled {
    color: #CECFC9;
  }
}

@media all and (orientation: portrait) {
  .t-items {
    & ::v-deep .dialog_wrapper {
      background: rgba(0, 0, 0, 0.2);
      color: #CECFC9;
      border: none;
    }
  }
}
</style>