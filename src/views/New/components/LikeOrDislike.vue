<template>
  <div class="wrap">
    <transition name="hideLike" :duration="150">
      <div v-if="!localLike[props.id] || localLike[props.id] === LIKE_CONF.like" class="icons like" :class="{ active: localLike[props.id] === LIKE_CONF.like }" @click="doClick(LIKE_CONF.like)">
        <span class="star star-1"></span><span class="star star-2"></span><span class="star star-3"></span><span class="star star-4"></span><span class="star star-5"></span>
      </div>
    </transition>
    <transition name="hideLike" :duration="150">
      <div v-if="!localLike[props.id] || localLike[props.id] === LIKE_CONF.dislike" class="icons dislike" :class="{ active: localLike[props.id] === LIKE_CONF.dislike }" @click="doClick(LIKE_CONF.dislike)"></div>
    </transition>
  </div>
</template>

<script lang="ts">
import { defineProps, defineEmits, Ref, defineComponent } from 'vue'
import { useStorage } from '@vueuse/core'
export default defineComponent({
  name: 'LikeOrDislike'
})
</script>
<script setup lang="ts">
import { LIKE_CONF } from '@/enum'
const emits = defineEmits(['click'])
const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  }
})
const localLike = useStorage('localLike', {}) as Ref<Record<string, number>>
const doClick = (type: number) => {
  if (localLike.value[props.id] > 0) return
  localLike.value[props.id] = type
  emits('click', type)
}
</script>

<style lang="scss" scoped>
$time_star: .7s;
.wrap {
  margin-top: 5px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 30px;
  &>div {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .icons {
    width: 56px;
    height: 32px;
    margin-left: 25px;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    &.like {
      background-image: url('~@/assets/img/soc/icon-like.png');
      &.active {
        animation: likeAnimate .3s ease forwards;
        .star-1 { animation: star01 $time_star ease forwards; }
        .star-2 { animation: star02 $time_star ease forwards; }
        .star-3 { animation: star03 $time_star ease forwards; }
        .star-4 { animation: star04 $time_star ease forwards; }
        .star-5 { animation: star05 $time_star ease forwards; }
      }
    }
    &.dislike {
      background-image: url('~@/assets/img/soc/icon-dislike.png');
      &.active {
        background-image: url('~@/assets/img/soc/icon-dislike-active.png');
      }
    }
  }
  .star{
    width: 15px;
    height: 15px;
    opacity: 0;
    position: absolute;
    @include backgroundSec('soc/star.png');
  }

}

.hideLike-leave-active {
  transition: transform;
}
.hideLike-leave-to {
  transform: scale(0);
}

@keyframes likeAnimate {
  0% {
    transform: scale(1);
    background-image: url('~@/assets/img/soc/icon-like.png');
  }
  49% {
    transform: scale(.7);
    background-image: url('~@/assets/img/soc/icon-like.png');
  }
  50% {
    opacity: 0;
    transform: scale(.7);
    background-image: url('~@/assets/img/soc/icon-like-active.png');
  }
  80% {
    opacity: 1;
    transform: scale(1.1);
    background-image: url('~@/assets/img/soc/icon-like-active.png');
  }
  100% {
    transform: scale(1);
    background-image: url('~@/assets/img/soc/icon-like-active.png');
  }
}
@keyframes star01 {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0) rotate(0);
  }
  70% {
    opacity: 1;
    transform: scale(0.7) translate(-25px, -30px) rotate(-60deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.7) translate(-23px, -33px) rotate(-60deg);
  }
}
@keyframes star02 {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0) rotate(0);
  }
  70% {
    opacity: .7;
    transform: scale(0.5) translate(-10px, -30px) rotate(40deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.5) translate(-11px, -33px) rotate(40deg);
  }
}
@keyframes star03 {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0) rotate(0);
  }
  70% {
    opacity: 1;
    transform: scale(0.7) translate(4px, -40px) rotate(-40deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.7) translate(4.4px, -44px) rotate(-40deg);
  }
}
@keyframes star04 {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0) rotate(0);
  }
  70% {
    opacity: .7;
    transform: scale(0.6) translate(15px, -30px) rotate(-20deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.6) translate(16.5px, -33px) rotate(-20deg);
  }
}
@keyframes star05 {
  0% {
    opacity: 0;
    transform: scale(0) translate(0, 0) rotate(0);
  }
  70% {
    opacity: 1;
    transform: scale(0.8) translate(25px, -30px) rotate(-40deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translate(27.5px, -33px) rotate(-40deg);
  }
}
</style>