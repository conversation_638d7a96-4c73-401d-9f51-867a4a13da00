<template>
  <van-popup
    class="classify-popup-container"
    v-model:show="props.show"
    teleport="body"
    position="bottom"
    round
    :style="popupStyle"
    close-on-click-overlay
    @click-overlay="closePopup"
  >
    <div class="classify-popup" ref="popupRef">
      <div class="popup-title">
        <span>{{ $t('text_please_select') }}</span>
        <div class="popup-close" @click="closePopup"></div>
      </div>
      <div class="list" ref="listRef">
        <div class="list-item" v-for="(v, k) in categoryList" :key="k" @click="handleCategoryClick(v)">
          <div>{{ v.label }}</div>
          <div class="list-item-arrow"></div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'ClassifyPopup'
})
</script>

<script setup lang="ts">
import { reactive, toRefs, onMounted, getCurrentInstance, defineProps, defineEmits, computed, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { sceneEntrance, getRelationCats } from '@/api/tickets'
import { Toast } from 'vant'

interface CategoryItem {
  id?: number,
  label?: string,
  level?: number,
  tpl_id?: number,
  children?: Array<CategoryItem>,
  relate_type?: number
}

interface StateType {
  categoryList: Array<CategoryItem>,
  parentCategory?: CategoryItem,
  isLoaded: boolean
}

const props = defineProps<{
  show: boolean,
  levelId?: number | null
}>()

const emits = defineEmits(['close', 'selectCategory'])

const router = useRouter()
const route = useRoute()
const { t: $t } = useI18n()
const { commit } = useStore()
const state = reactive<StateType>({
  categoryList: [],
  parentCategory: undefined,
  isLoaded: false
})

// DOM引用
const popupRef = ref<HTMLElement | null>(null)
const listRef = ref<HTMLElement | null>(null)

// 弹窗高度相关状态
const popupHeight = ref(0)
const bottomWrapHeight = ref(118) // 默认高度，将根据实际情况更新

// 关闭弹窗
const closePopup = () => {
  emits('close')
}

const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog

onMounted(() => {
  loadCategoryData()

  // 获取页面中 bottom-wrap 元素的高度
  getBottomWrapHeight()

  // 设置已加载状态，下次打开时不需要重新加载背景
  setTimeout(() => {
    state.isLoaded = true
  }, 100)

  // 优化移动端滚动体验
  setupMobileScroll()
})

// 获取页面中 bottom-wrap 元素的高度
const getBottomWrapHeight = () => {
  const bottomWrapElement = document.querySelector('.bottom-wrap')
  if (bottomWrapElement) {
    bottomWrapHeight.value = bottomWrapElement.clientHeight
  }
}

// 更新高度
const updateHeight = async () => {
  await nextTick()
  if (listRef.value) {
    const listHeight = listRef.value.scrollHeight
    const maxHeight = window.innerHeight * 0.8 // 设置最大高度为视口高度的80%
    popupHeight.value = Math.min(Math.max(listHeight + 60, bottomWrapHeight.value), maxHeight)
  }
}

// 优化移动端滚动体验
const setupMobileScroll = () => {
  if (listRef.value) {
    // 移除之前可能添加过的事件监听器
    listRef.value.removeEventListener('touchmove', handleTouchMove)

    // 添加新的触摸事件处理
    listRef.value.addEventListener('touchmove', handleTouchMove, { passive: true })

    // 修复iOS中可能出现的滚动失效问题
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      // 使用as any绕过TypeScript类型检查
      (listRef.value.style as any).webkitOverflowScrolling = 'touch'
    }
  }
}

// 触摸移动事件处理函数
const handleTouchMove = (e: TouchEvent) => {
  // 阻止事件冒泡，但由于使用passive: true，默认滚动行为将被保留
  e.stopPropagation()
}

// 监听组件更新后重新设置滚动
watch([() => state.categoryList, () => props.show], async () => {
  if (props.show) {
    await nextTick()
    getBottomWrapHeight() // 每次显示弹窗时重新获取 bottom-wrap 高度
    updateHeight()
    setupMobileScroll() // 重新设置滚动
  }
}, { immediate: true })

// 加载分类数据
const loadCategoryData = () => {
  commit('setLoadingCount', 1)

  let params = {}, getCatsUrl = null
  // 如果有level_id，那就是聊天页"提交工单"，关联工单不同层级分类
  if (props.levelId) {
    params = { fork_cat_ids: [props.levelId] }
    getCatsUrl = getRelationCats(params)
  } else {
    // 获取入口信息, 判断是否是智能客服点踩后跳转的工单需求
    params = {}
    getCatsUrl = sceneEntrance(params)
  }

  getCatsUrl.then((res: Array<CategoryItem>) => {
    commit('setLoadingCount', 0)
    state.categoryList = res
    // 数据加载后更新高度
    nextTick(() => {
      updateHeight()
    })
  }, (err: string) => {
    commit('setLoadingCount', 0)
    Toast(err)
  }).catch((err: string) => {
    commit('setLoadingCount', 0)
    Toast(err)
  })
}

// 处理分类点击事件
const handleCategoryClick = (item: CategoryItem) => {
  // 分类点击打点
  setLog({
    button: item.id,
    action: 'click',
    result: 1,
    position: 'que_classify'
  })

  if (item.children && item.children.length > 0) {
    // 有子分类，发出选择类别事件，父组件可以显示子类别
    emits('selectCategory', item, 'sub')
  } else {
    // 没有子分类，直接发出选择类别事件，父组件可以跳转到问题解答页
    emits('selectCategory', item, 'answer')
  }
}

const { categoryList } = toRefs(state)

// 计算弹窗样式
const popupStyle = computed(() => {
  return {
    borderRadius: '0',
    background: 'transparent',
    transition: 'all 0.3s ease',
    maxHeight: popupHeight.value ? `${popupHeight.value}px` : '80vh',
    height: popupHeight.value ? `${popupHeight.value}px` : 'auto'
  }
})
</script>

<style lang="scss" scoped>
.classify-popup-container {
  border-radius: none;
}
.classify-popup {
  height: 100%;
  padding-bottom: 30px;
  display: flex;
  flex-direction: column;
  position: relative;
  max-height: 80vh; /* 添加最大高度限制 */
  overflow: hidden; /* 修改overflow设置 */
  // background-color: #2E4654; /* 使用纯色背景替代图片 */
  /* 如果需要保留图片效果，可以使用高效加载方式 */
  -webkit-overflow-scrolling: touch; /* 全局启用弹性滚动 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: url('~@/assets/img/conversation/classify-popup-one-bg.png') no-repeat center center;
    background-size: 100% 100%;
    will-change: transform; /* 提示浏览器提前做优化处理 */
    transform: translateZ(0); /* 强制GPU加速 */
    backface-visibility: hidden; /* 优化渲染性能 */
  }

  .popup-title {
    position: relative;
    width: 100%;
    padding: 27px 30px 20px 30px;
    color: #cecfc9;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24px;
    color: #E2C885;
    line-height: 33px;
    text-align: left;
    font-style: normal;
    background-color: rgba(28, 34, 45, 0.9);
    flex-shrink: 0; /* 防止标题被压缩 */
    /* 如果需要保留图片效果，可以使用高效加载方式 */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      background: url('~@/assets/img/conversation/classify-popup-title.png') no-repeat center center;
      background-size: 100% 100%;
      will-change: transform;
      transform: translateZ(0); /* 强制GPU加速 */
      backface-visibility: hidden; /* 优化渲染性能 */
    }

    .popup-close {
      position: absolute;
      top: 30px;
      right: 30px;
      display: block;
      width: 30px;
      height: 30px;
      cursor: pointer;
      z-index: 2;
      background-color: transparent;
      transform: translateZ(0); /* 强制GPU加速 */
      background: url('~@/assets/img/conversation/upload-dialog-close.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .list {
    flex: 1;
    overflow-y: auto;
    padding: 0 30px;
    -webkit-overflow-scrolling: touch; /* 增加iOS滚动支持 */
    position: relative; /* 添加相对定位 */
    height: 0; /* 确保flex布局下可以正确滚动 */
    touch-action: pan-y; /* 允许垂直方向的滑动 */
    overscroll-behavior: contain; /* 防止滚动穿透 */
    -webkit-transform: translateZ(0); /* 强制GPU加速 */
    transform: translateZ(0);
    will-change: transform; /* 提示浏览器提前做优化处理 */

    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }

  .list-item {
    overflow: auto;
    vertical-align: middle;
    padding: 26px 0;
    border-bottom: 3px solid #3C4857;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 22px;
    color: #9ABBCD;
    line-height: 30px;
    text-align: left;
    font-style: normal;
  }

  .list-item-arrow {
    display: inline-block;
    width: 14px;
    height: 22px;
    background: url('~@/assets/img/conversation/classify-popup-arrow.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .button-wrapper {
    padding: 20px 0;
    display: flex;
    justify-content: center;
  }

  .cancel-btn {
    width: 200px;
    height: 45px;
    line-height: 45px;
    text-align: center;
    color: #cecfc9;
    border: 1px solid #cecfc9;
    border-radius: 22px;
    font-size: 18px;
  }
}
</style>
<style lang="scss">
.classify-popup-container {
  border-radius: none;
}
</style>
