<template>
	<div class="ticket-bg">
		<div class="ticket-wrapper">
			<div class="content">
				<van-form :show-error="false" class="form-wrapper">
					<div class="form-label">
						<span class="redIcon"></span>
						<span>{{ $t("text_reopen_label") }}</span>
          </div>
					<van-field
						class="form-item"
						v-model="form.content"
						type="textarea"
						ref="textarea"
						rows="3"
						autosize
						maxlength="2000"
						show-word-limit
					/>
					<div class="form-label">
						<div class="icon"></div>
						<span>{{ $t("text_img_add") }}</span>
					</div>
					<img-upload
						:isH5Upload="isPC || isPrivZoneH5"
						class="form-item"
						@success="uploadImg"
						@remove="removeImg"
					></img-upload>
					<div class="form-label">
						<div class="icon"></div>
						<span>{{ $t("text_video_add") }}</span>
					</div>
					<video-upload
						:isH5Upload="isPC || isPrivZoneH5"
						class="form-item"
						@success="uploadVideo"
						@remove="removeVideo"
					></video-upload>
					<div class="submit-btn reopen-btn" @mousedown.stop.prevent="handleComplete" @touchstart.stop.prevent="handleComplete">{{ $t("text_submit") }}</div>
				</van-form>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, ref, getCurrentInstance, computed, defineEmits, defineProps } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { reopenTicket } from '@/api/tickets'
	interface dataT {
		imgList: Array<unknown>,
		videoList: Array<unknown>,
		pushAnimate: boolean,
		form: {
			ticket_id?: number,
			content: string,
			files: Array<unknown>
		},
		locked: boolean
	}
	export default defineComponent({
		name: 'ReopenTicket'
	})
</script>
<script lang="ts" setup>
	const { state } = useStore()
	const userInfo = computed(() => state.userInfo)
	// const isWXGame = computed(() => userInfo.value && 'openid' in userInfo.value )
	// const isFromAI = computed(() => userInfo.value && userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai'))
	const isPC = JSON.parse(sessionStorage.getItem('isPC') ?? 'false')

	// 对私域上传做的特判处理：
	// 私域 h5上传
	const isPrivZone = computed(() => userInfo.value?.zone_from === 'privZone')
	const isInWebview = computed(() => state.globalEnvConfig.isInWebview)
	// 私域 且 不是端内webview 则 使用h5上传；如果是私域 且 是端内webview 则 使用SDK桥接上传
	const isPrivZoneH5 = computed(() => isPrivZone.value && !isInWebview.value)

	const route = useRoute()
	const { t: $t } = useI18n()
	const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
	const textarea = ref()
	const data: dataT = reactive({
		imgList: [],
		videoList: [],
		form: {
			content: '',
			files: []
		},
		pushAnimate: false,
		locked: false
	})
	const props = defineProps<{
		catIdFromParent: number
	}>()
	onMounted(() => {
		if (route.query.ticketId) {
			data.form.ticket_id = +route.query.ticketId
		} else {
			console.log('缺少重要参数')
		}
		// // H5兼容ios12 监听所有input blur
		// const inputsElement = [...document.getElementsByTagName('input')]
		// inputsElement.forEach(element => {
		// 	if (element) {
		// 		element.onblur = () => {
		// 			setTimeout(() => {
		// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
		// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
		// 			}, 300)
		// 		}
		// 	}
		// })
	})
	// 上传图片
	const uploadImg = (path: Array<string>) => {
		data.imgList.push(...path)
	}
	// 删除图片
	const removeImg = (index: number) => {
		data.imgList.splice(index, 1)
	}
	// 上传视频
	const uploadVideo = (path: Array<string>) => {
		data.videoList.push(...path)
	}
	// 删除视频
	const removeVideo = () => {
		data.videoList.splice(0, 1)
	}
	const handleComplete = () => {
		// 解决安卓机器点击提交按钮触发键盘弹起的问题
		textarea.value.blur()
		data.pushAnimate = true
		if (data.locked) {
			// '表单提交中\n请稍候'
			Toast.fail($t('text_submiting'))
			return
		}
		if (!data.form.content) {
			Toast($t('text_reopen_label'))
			return
		}
		data.form.files = [...data.imgList, ...data.videoList]
		const params = JSON.parse(JSON.stringify(data.form))
		params.files = JSON.stringify(params.files)
		data.locked = true
		reopenTicket(params).then(() => {
			// 提交成功打点
			setLog({
				is_succeed: 'true',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
      clickChild()
			data.locked = false
		}, (res: string) => {
			// 重开提交失败打点
			setLog({
				is_succeed: 'false',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
			data.locked = false
			Toast(res)
		}).catch((err: string) => {
			// 重开提交失败打点
			setLog({
				is_succeed: 'false',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
			data.locked = false
			Toast(err)
		})
	}
  const emit = defineEmits(['show-success'])
  const clickChild = () => {
    emit('show-success')
  }

	const { form } = toRefs(data)
</script>

<style lang="scss" scoped>
.content {
	word-wrap: break-word;
	word-break: break-all;
	overflow: auto;
	font-size: 24px;
	.title {
		overflow: auto;
		margin: 0px 0px 20px 0px;
		box-sizing: border-box;
		width: 100%;
		padding: 5px;
		height: auto;
		line-height: 56px;
    color: #D4AD5B;
		border-left: 4Px solid rgba(245, 193, 51, 0.7);
		background: rgba(5, 5, 6, 0.26);
		img {
			display: block;
			height: 30px;
			width: 30px;
			float: left;
			opacity: 0.8;
			margin: 13px 5px 0px 3px;
		}
	}

	.form-wrapper {
		box-sizing: border-box;
		.form-item-wrap {
			padding-bottom: 20px;
		}
		.form-item {
			margin: 10px 0 20px;
		}
	}

	& ::v-deep(.van-form) {
		font-size: 22px;
		font-family: Source Han Serif CN;
		font-weight: 400;
		color: #9ABBCD;
	}

	& ::v-deep(.van-cell) {
		background: rgba(5,5,6,0.26);
		border: 1px solid #445960;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-form) {
		font-size: 24px;
		color: #9ABBCD;
	}

	& ::v-deep(.van-cell) {
		background: rgba(5,5,6,0.26);
		border: 1px solid #445960;
		padding: 10px 20px;
		color: #9ABBCD;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}
}

// 修改上传图片、视频组件样式
::v-deep .img-wrap .img-upload,
::v-deep .video-wrap .video-upload,
::v-deep .pc-box {
	width: 150px;
	height: 150px;
	border: 1px solid #445960;
	background: rgba(5, 5, 6, 0.26) url('~@/assets/img/soc/plus.webp') no-repeat center;
	background-size: 70px;
}

@media all and (orientation : portrait) {
	.img-wrap {
		::v-deep img {
			width: 150px;
			height: 150px;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			top: 0;
			right: 0;
		}
	}
	.video-wrap {
		::v-deep .video-preview {
			// width: 65vw;
			// height: 18vh;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			right: -50px;
		}
	}
}
</style>
