<template>
	<div class="t-items">
		<div v-if="formData.category" class="content">
			<div class="title">
				<!-- <span>{{ formData.category }}</span> -->
			</div>
			<van-form ref="formRef" :show-error="false" class="form-wrapper">
				<div v-for="(item, index) in formItemList" :key="index" class="form-item-wrap">
					<!-- “备注”“提示”或者“说明”的label特殊处理 -->
					<div v-if="item.field_type === ITEM_TYPES.CAPTION" class="form-caption">
						<span :class="{ redIcon: item.is_required }"></span>
						<span>{{ item.field_name }}: {{ item.hint_field }}</span>
					</div>
					<div v-else class="form-label">
						<span :class="{ redIcon: item.is_required }"></span>
						<span>{{ item.field_name }}</span>
					</div>
					<!-- input -->
					<van-field
						class="form-item"
						v-if="item.field_type === ITEM_TYPES.TEXT"
						v-model="submitForm.fields[item.field_name]"
						:placeholder="item.hint_field"
						:rules="[
							{ required: item.is_required, message: item.required_rule }
						]"
					/>
					<!-- textarea -->
					<van-field
						class="form-item"
            v-if="item.field_type === ITEM_TYPES.TEXTAREA"
            v-model="submitForm.fields[item.field_name]"
            type="textarea"
            rows="4"
            autosize
            maxlength="2000"
            show-word-limit
            :placeholder="item.hint_field"
            :rules="[
              { required: item.is_required, message: item.required_rule }
            ]"
          />
					<!-- number input -->
					<van-field
						class="form-item"
            v-if="item.field_type === ITEM_TYPES.NUMBER"
            v-model="submitForm.fields[item.field_name]"
            type="number"
            :placeholder="item.hint_field"
            :rules="[
              { required: item.is_required, message: item.required_rule }
            ]"
          />
					<!-- datepicker -->
					<template v-if="item.field_type === ITEM_TYPES.DATEPICKER">
            <van-field
							class="form-item"
              readonly
              clickable
              name="datetimePicker"
              v-model="submitForm.fields[item.field_name]"
              :right-icon="selectImg"
              :rules="[
                { required: item.is_required, message: item.required_rule }
              ]"
              @click="item.show_picker = true"
            />
            <van-popup v-model:show="item.show_picker" overlay-class="f-overlay" position="bottom" teleport="body">
              <van-datetime-picker
                :type="item.field_extend.only_date === true ? 'date' : 'datetime' "
                v-model="currentDate"
                @confirm="onTimeConfirm($event, item, item.field_name)"
                @cancel="item.show_picker = false"
              >
                <template #confirm>
                  {{ $t('btn_confirm') }}
                </template>
                <template #cancel>
                  {{ $t('btn_cancel') }}
                </template>
              </van-datetime-picker>
            </van-popup>
          </template>
					<!-- select -->
					<template v-if="item.field_type === ITEM_TYPES.SELECT">
            <van-field
							class="form-item"
              readonly
              clickable
              name="picker"
              v-model="submitForm.fields[item.field_name]"
              :right-icon="selectImg"
              :rules="[
                { required: item.is_required, message: item.required_rule }
              ]"
              @click="item.show_picker = true"
            />
            <van-popup v-model:show="item.show_picker" position="bottom" teleport="body">
              <van-picker
                show-toolbar
                :columns="item.option_field"
                :columns-field-names="{text: 'name'}"
                @confirm="onPickerConfirm($event, item, item.field_name)"
                @cancel="item.show_picker = false"
              >
                <template #confirm>
                  {{ $t('btn_confirm') }}
                </template>
                <template #cancel>
                  {{ $t('btn_cancel') }}
                </template>
              </van-picker>
            </van-popup>
          </template>
					<!-- img upload -->
					<div class="form-item">
						<template v-if="item.field_type === ITEM_TYPES.PIC">
							<img-upload
								:isH5Upload="isPC || isPrivZoneH5"
								@success="uploadSuccess(item.field_name, $event)"
								@remove="removeUploaded(item.field_name, $event)"
							></img-upload>
						</template>
					</div>
					<!-- video upload -->
					<div class="form-item">
						<template v-if="item.field_type === ITEM_TYPES.VIDEO">
							<video-upload
								:isH5Upload="isPC || isPrivZoneH5"
								@success="uploadSuccess(item.field_name, $event)"
								@remove="removeUploaded(item.field_name)"
							></video-upload>
						</template>
					</div>
				</div>
				<!-- 提交按钮，表单未加载时不展示按钮 -->
				<div v-if="formItemList && formItemList.length !== 0">
					<div class="submit-btn" @click="onSubmit">{{ $t("text_submit") }}</div>
				</div>
			</van-form>
		</div>
		<!-- <van-loading v-else size="24px" color="#9ABBCD" type="spinner" vertical style="margin-top: 150px;">{{ $t('text_in_progress') }}</van-loading> -->
	</div>
</template>

<script lang="ts">
	import { defineComponent, ref, reactive, toRefs, defineProps, onMounted, nextTick, defineEmits, getCurrentInstance, computed } from 'vue'
	import { useStore } from 'vuex'
	import { Toast } from 'vant'
	import { useI18n } from 'vue-i18n'
	import { getTplInfo, createTicket } from '@/api/tickets'
	import { useDebounceFn } from '@vueuse/core'
	import { ITEM_TYPES } from '@/enum'
	interface AnyObj {
		// eslint-disable-next-line
		[key: string]: any
	}
	interface FormData {
		fields?: string,
		category?: string,
		tpl?: string,
		tpl_id?: number
	}
	interface FormItem {
		[key: string]: unknown,
		field_name: string,
		hint_field: string,
		is_required: boolean,
		required_rule: string,
		show_picker?: boolean,
		option_field: Array<Record<string, unknown>>,
		field_extend: Record<string, unknown>
	}
	interface SubmitForm {
		[key: string]: unknown,
		fields: AnyObj
	}
	export default defineComponent({
		name: 'TicketForm'
	})
</script>
<script setup lang="ts">
const { commit, state } = useStore()
const userInfo = computed(() => state.userInfo)
const isPC = JSON.parse(sessionStorage.getItem('isPC') ?? 'false')

// 对私域上传做的特判处理：
// 私域 h5上传
const isPrivZone = computed(() => userInfo.value?.zone_from === 'privZone')
const isInWebview = computed(() => state.globalEnvConfig.isInWebview)
// 私域 且 不是端内webview 则 使用h5上传；如果是私域 且 是端内webview 则 使用SDK桥接上传
const isPrivZoneH5 = computed(() => isPrivZone.value && !isInWebview.value)

// const isWXGame = computed(() => userInfo.value && 'openid' in userInfo.value )
// const isFromAI = computed(() => userInfo.value && userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai') )
const { t: $t } = useI18n()
const currentDate = ref(new Date())
const formRef = ref()
const selectImg = require('@/assets/img/select-general.png')
const isShowChat = computed(() => state.global.isShowChat)
const data: {
	formData: FormData,
	formItemList: Array<FormItem>,
	submitForm: SubmitForm,
	locked: boolean
} = reactive({
	formData: {},
	formItemList: [],
	submitForm: {
		ticket_type: isShowChat.value,
		origin: 1,
		//  nickname: '',
		cat_id: null,
		fields: {},
		trouble_uid: null
	},
	locked: false,
})
const _u = getCurrentInstance()?.appContext.config.globalProperties.$utils
const setLog = _u.basicLog
setLog({
	event: 'elfin_load_done',
  position: 'ticket_form',
  result: 'success'
})
const props = defineProps<{
	itemData: {
		id: number,
		label?: string,
		level: number,
		tpl_id: number,
		index: number,
		process_id?: number,
		process_session?: string
	}
	fromTicketId: number
}>()
const emit = defineEmits<{
	(event: 'update-scroll'): void
	(event: 'submit-success', ticket_id: number): void
}>()
onMounted(() => {
	const { tpl_id, id, process_id, process_session } = props.itemData
	// 提交表单需要cat_id
	data.submitForm.cat_id = id
	// 自动化流程需要process_id和process_session
	if (process_id) data.submitForm.process_id = process_id
	if (process_session) data.submitForm.process_session = process_session
	getTplInfo({
		tpl_id,
		cat_id: id,
		process_id: process_id ? process_id : 0
	}).then((res: FormData) => {
		// 表单加载成功打点
		setLog({
			button: 0,
			action: 'loading',
			result: 1,
			position: 'form_fill'
		})
		data.formData = res
		data.formItemList = res.fields && JSON.parse(res.fields)
		getFieldMap()
		nextTick(() => {
			emit('update-scroll')
			// H5兼容ios12 监听所有input blur
			const inputsElement = [...document.getElementsByTagName('input')]
			inputsElement.forEach(element => {
				if (element) {
					element.onblur = () => {
						setTimeout(() => {
							const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
							window.scrollTo(0, Math.max(scrollHeight - 1, 0))
						}, 300)
					}
				}
			})
		})
	}, () => {
		// 表单加载失败打点
		setLog({
			button: 0,
			action: 'loading',
			result: 0,
			position: 'form_fill'
		})
	}).finally(() => {
		commit('setLoadingCount', 0)
	})
})
// sdk映射默认值
const getFieldMap = () => {
	console.log('getFieldMap', data.formItemList)
	console.log('getItemData', props.itemData)
	console.log('userInfo', userInfo.value)
	data.formItemList.forEach((item) => {
		if (
			(
				item.field_type === ITEM_TYPES.TEXT ||
				item.field_type === ITEM_TYPES.TEXTAREA ||
				item.field_type === ITEM_TYPES.NUMBER
			) && item.field_map
		) {
			if (item.field_map === 'role_name') {
				data.submitForm.fields[item.field_name] = userInfo.value[item.field_map]
					? decodeURIComponent(userInfo.value[item.field_map]) : userInfo.value.name
						? decodeURIComponent(userInfo.value.name) : ''
			} else {
				data.submitForm.fields[item.field_name] = userInfo.value[item.field_map as string] ? decodeURIComponent(userInfo.value[item.field_map as string]) : ''
        if (item.field_map === 'uid' || item.field_map === 'UID') {
          data.submitForm['uid_key'] = item.field_name
        }
			}
		}
	})
}
const onTimeConfirm = (time: Date, item: FormItem, name: string): void => {
	if (item.field_extend.only_date) {
		data.submitForm.fields[name] = _u.formatDate(time.getTime(), 'YYYY-MM-DD')
	}	else {
		data.submitForm.fields[name] = _u.formatDate(time.getTime())
	}
	item.show_picker = false
}
const onPickerConfirm = (option: Record<string, unknown>, item: FormItem, name: string): void => {
	data.submitForm.fields[name] = option.name
	item.show_picker = false
}
// img、video上传成功
const uploadSuccess = (name: string, path: Array<string>): void => {
	!data.submitForm.fields[name] && (data.submitForm.fields[name] = [])
	data.submitForm.fields[name].push(...path)
}
// 删除img、video上传
const removeUploaded = (name: string, index?: number): void => {
	index = index || 0
	data.submitForm.fields[name].splice(index, 1)
}
// 提交表单
const onSubmit = useDebounceFn(() => {
	// 表单验证
	formRef.value && formRef.value.validate()
	.then(() => {
		const result = data.formItemList.find(formItem => {
			return (
				(formItem.field_type === ITEM_TYPES.PIC ||
					formItem.field_type === ITEM_TYPES.VIDEO) &&
				formItem.is_required &&
				(!data.submitForm.fields[formItem.field_name] ||
					!data.submitForm.fields[formItem.field_name].length)
			)
		})
		if (result) {
			Toast(result.required_rule)
			return
		}
    // 同一时间只能提交一个表单
    if (data.locked) {
      // 表单提交中\n请稍候
      Toast.fail($t('text_submiting'))
      return
    }

		data.locked = true
		const submitParams = JSON.parse(JSON.stringify(data.submitForm))
    // 如果有uid_key，将uid_key对应的值赋给trouble_uid，这是代提单的uid
    if (submitParams.uid_key && submitParams.fields[submitParams.uid_key]) {
      submitParams.trouble_uid = Number(submitParams.fields[submitParams.uid_key])
    }
		submitParams.fields = JSON.stringify(submitParams.fields)
		if (props.fromTicketId !== 0) {
			submitParams.from_ticket_id = props.fromTicketId
		}
		commit('setLoadingCount', 1)
		// 新增创单时，校验是否来源于私域背包-充值无忧服务卡，是的话需要添加参数
		// 判断sessionStorage是否存在p,且p.priv_rights_type与p.priv_rights_use都存在，则设置submitParams.priv_rights_type
		const p = JSON.parse(sessionStorage.getItem('p') || '{}')
		if (p.priv_rights_type && p.priv_rights_use) {
			submitParams.priv_rights_use = true
		}
		createTicket(submitParams).then((res: Record<string, number>) => {
			// 工单表单提交成功打点
			setLog({
				button: 1,
				action: 'click',
				result: 1,
				position: 'form_fill'
			})
			emit('submit-success', res.ticket_id)
		}, (res: string) =>{
			// 工单表单提交失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'form_fill'
			})
			Toast(res)
		}, (err: string )=> {
      // 工单表单提交失败打点
      setLog({
        button: 1,
        action: 'click',
        result: 0,
        position: 'form_fill'
      })
      Toast(err)
    }).finally(()=> {
			data.locked = false
			commit('setLoadingCount', 0)
		})
	}).catch((err: string) => {
		console.log('err', err)
		// data.locked = false
		// Toast(err)
		// 如果err是字符串，则Toast(err)
		if (typeof err === 'string') {
			Toast(err)
		}
	}).catch((err: string) => {
		console.log('TicketForm onSubmit err', err)
	})
}, 300)

const { formData, formItemList, submitForm } = toRefs(data)
</script>

<style lang="scss" scoped>
.content {
	word-wrap: break-word;
	word-break: break-all;
	.title {
		margin-bottom: 20px;
		box-sizing: border-box;
		width: 100%;
		height: auto;
		line-height: 23px;
    font-size: 24px;
		font-family: Source Han Serif CN;
		font-weight: 400;
		color: #D4AD5B;
		text-shadow: 0px 1px 0px rgba(0,0,0,0.6);
	}
	.form-wrapper {
		box-sizing: border-box;
		.form-item-wrap {
			padding-bottom: 20px;
			.form-item {
				margin-top: 7px;
			}
		}
	}

	& ::v-deep(.van-form) {
		font-size: 22px;
		font-family: Source Han Serif CN;
		font-weight: 400;
		color: #9ABBCD;
	}

	& ::v-deep(.van-cell) {
		background: rgba(5,5,6,0.26);
		border: 1px solid #445960;
		padding: 15px 20px;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}

	& ::v-deep(.van-field__right-icon) {
		width: 40px;
		padding: 0;
		line-height: 0;
		img {
			width: 100%;
			height: auto;
		}
	}
}

// 修改上传图片、视频组件样式
::v-deep .img-wrap .img-upload,
::v-deep .video-wrap .video-upload,
::v-deep .pc-box {
	width: 150px;
	height: 150px;
	border: 1px solid #445960;
	background: rgba(5, 5, 6, 0.26) url('~@/assets/img/soc/plus.webp') no-repeat center;
	background-size: 70px;
}

.bottom-line {
	width: 100%;
	height: 2px;
	margin: 4px 0 50px;
	@include backgroundSec('soc/bg-line.png');
}
.like-wrap {
	margin-top: 20px;
}

@media all and (orientation: portrait) {
	.img-wrap {
		::v-deep img {
			width: 150px;
			height: 150px;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			top: 0;
			right: 0;
		}
	}
	.video-wrap {
		::v-deep .video-preview {
			// width: 65vw;
			// height: 18vh;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			right: -50px;
		}
	}
}
</style>
