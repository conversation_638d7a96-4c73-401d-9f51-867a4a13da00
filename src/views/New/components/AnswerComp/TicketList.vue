<template>
	<div class="t-items">
		<div class="title">
			<img src="~@/assets/img/group.png">
			<!-- 请选择问题分类 / 请选择xxx的细分问题分类 -->
			<span>{{ itemData.level === 0 ? $t("text_selection_sort") : $t("text_selection_sort_sub", {q: itemData.label})
				}}</span>
		</div>
		<div class="list">
			<div class="list-item" v-for="(v, k) in itemData.children" :key="k">
				<div style="float:left" @click="$emit('item', v)">
					<img src="~@/assets/img/point.png"><span>{{ v.label }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, defineProps } from 'vue'
interface itemT {
	id?: number,
	label?: string,
	level: number,
	index: number,
	tpl_id?: number,
	children?: Array<itemT>
}
interface Props {
	itemData: itemT
}
export default defineComponent({
	name: 'TicketList'
})
</script>
<script setup lang="ts">
import { useStore } from 'vuex'
const { commit } = useStore()
defineProps<Props>()
commit('setLoadingCount', 0)
</script>

<style lang="scss" scoped>
.title {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  line-height: 30px;
  color: #cecfc9;
  font-size: 24px;
  border-radius: 2px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  img {
    display: block;
    height: 30px;
    width: 30px;
    float: left;
    opacity: 0.8;
    margin-right: 6px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 55px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.list-item {
  color: #E9C86F;
  overflow: auto;
  font-size: 22px;
  line-height: 35px;
  vertical-align: middle;
  margin: 20px auto;
  cursor: pointer;
  img {
    display: block;
    height: 35px;
    width: 35px;
    float: left;
    margin-right: 5px;
  }
}
</style>