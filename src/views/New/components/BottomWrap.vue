<!-- 
  通用底部组件 （输入框提问、联想词提问、热门问题提问 ）
  对外暴露方法：
    1、hotWord-click    - 热门问题点击事件
      txt: string         - 热门问题内容
    2、submit           - 输入框提问、联想词提问
      txt: string         - 提问内容
      type: string        - 提问类型 (chat_custom: 输入框提问, asso_answer: 联想词提问)
-->
<template>
  <div class="bottom-wrap">
    <div class="container">
      <div class="hot-words" v-if="baseInfo.bottom_data.length">
        <div v-for="(item, index) in baseInfo.bottom_data" :key="index" class="item" @click.stop="wordClick(item)">
          <div class="bg">
            <div class="bg-1"></div>
            <div class="bg-2"></div>
            <div class="bg-3"></div>
          </div>
          <div class="content"><auto-font-size :text="item"></auto-font-size></div>
        </div>
      </div>
      <!-- <div class="line"></div> -->
      <div class="user-input">
        <!-- 自助查询 -->
        <div v-if="showSelfServies" @click.stop="wordClick($t('text_self_service'))" class="btn_self_servies">
          <auto-font-size :text="$t('btn_self_help')"></auto-font-size>
        </div>
        <form class="search-form" @submit="submit">
          <div class="q-list" :style="{ height: qListHeight }">
            <div class="q-item" v-for="(item, i) in qList" :key="`${i}_${item}`" @click.stop="qItemClick(item)">{{ item }}</div>
          </div>
          <input class="input" ref="inputEle" :placeholder="$t('txt_placeholder')" v-model.trim="searchTxt" />
        </form>
        <div class="btn submit" @click.stop="submit">
          <auto-font-size :text="$t('txt_submit')"></auto-font-size>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, defineEmits, watch } from 'vue'
import { useStore } from 'vuex'
import { useDebounceFn } from '@vueuse/core'
import { getHotQList } from '@/api/new'
export default defineComponent({
  name: 'BottomWrap'
})
</script>
<script setup lang="ts">
const { state } = useStore()
const baseInfo = computed(() => state.baseInfo)
const emits = defineEmits<{
  (event: 'hotWord-click', txt: string): void
  (event: 'submit', txt: string, type: string): void
}>()
const searchTxt = ref('')
const wordClick = (content: string) => {
  emits('hotWord-click', content)
}
const inputEle = ref<HTMLElement>()
const submit = (event: Event) => {
  event.stopPropagation && event.stopPropagation()
  event.preventDefault && event.preventDefault()
  if (!searchTxt.value) return
  emits('submit', searchTxt.value, 'chat_custom')
  searchTxt.value = ''
  inputEle.value?.blur && inputEle.value?.blur()
}
const qItemClick = (txt: string) => {
  emits('submit', txt, 'asso_answer')
  searchTxt.value = ''
  inputEle.value?.blur && inputEle.value?.blur()
}
const qList = ref<Array<string>>([])
const qListHeight = computed(() => {
  const l = qList.value.length
  if (!l) return 0
  const h = (l * 47 + 2) / 133.4
  return `${h}rem`
})
watch(searchTxt, useDebounceFn(() => {
  if (!searchTxt.value || searchTxt.value.length < 3) {
    return qList.value = []
  }
  // 联想词接口
  getHotQList({
    query: searchTxt.value
  }).then((res: any) => {
    res = res || []
    if (searchTxt.value) qList.value = res
    // res.length > 0 && assoContentLog(searchTxt.value, res.join(', '))
  })
}, 500))

// 自助查询按钮
const showSelfServies = ref<boolean>(['ss', 'gog'].indexOf(sessionStorage.getItem('projectName') as string) > -1)
</script>

<style lang="scss" scoped>
.bottom-wrap {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 118px;
  @include backgroundSec('bg-bottom.jpg');
  display: flex;
  align-items: stretch;
  justify-content: center;
}
.v-test {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 70px;
  height: 70px;
}
.container {
  width: 1201px;
  padding-left: 195px;
  padding-right: 75px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}
.hot-words {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  height: 43px;
  align-items: flex-start;
  .item {
    transform: scale(0.9);
    position: relative;
    text-align: center;
    padding: 0 18px;
    height: 43px;
    cursor: pointer;
    &+.item {
      margin-left: -8px;
    }
    .bg {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }
    .bg-1 {
      width: 95px;
      height: 38px;
      @include backgroundSec('hot-words/bg-1.png');
    }
    .bg-2 {
      flex: 1;
      height: 38px;
      @include backgroundSec('hot-words/bg-2.png');
    }
    .bg-3 {
      width: 19px;
      height: 38px;
      @include backgroundSec('hot-words/bg-3.png');
    }
    .content {
      padding-top: 6px;
      font-size: 22px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #9ABBCD;
      line-height: 1.1;
      position: relative;
    }
  }
  .hot-item {
    padding-left: 45px;
    position: relative;
    &::before {
      content: "";
      width: 16px;
      height: 32px;
      position: absolute;
      left: 23px;
      @include backgroundSec('hot-words/icon-hot.png');
      background-size: contain;
      z-index: 1;
    }
    .bg-1 {
      @include backgroundSec('hot-words/hot-1.png');
    }
    .bg-2 {
      @include backgroundSec('hot-words/hot-2.png');
    }
    .bg-3 {
      @include backgroundSec('hot-words/hot-3.png');
    }
    .content {
      color: #3C3016;
    }
  }
}
@media screen and (min-width: 1200px) {
  .hot-words:hover {
    scrollbar-width: auto;
    &::-webkit-scrollbar {
      display: block;
      height: 5px;
    }
    &::-webkit-scrollbar-track {
      background: #d6cccc; /* 设置滚动条轨道的背景颜色 */
    }
    &::-webkit-scrollbar-thumb {
      background: #888; /* 设置滚动条滑块的背景颜色 */
    }
  }
}
.user-input {
  position: relative;
  height: 57px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .search-form {
    flex: 1;
    height: 57px;
    @include backgroundSec('bg-input.png');
    padding: 0 32px;
    position: relative;
    .q-list {
      z-index: 1;
      position: absolute;
      bottom: 57px;
      left: 0.53547%;
      right: 0.53547%;
      padding: 0 20px;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: center;
      background: #273443;
      box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.6);
      height: 0;
      overflow: hidden;
      transition: height .1s ease;
      .q-item {
        height: 47px;
        line-height: 47px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 20px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #65889B;
        flex-shrink: 0;
        &+.q-item {
          border-top: 1px solid rgba(154,187,205,0.08);
        }
      }
    }
    input {
      height: 57px;
      width: 100%;
      width: 100%;
      display: block;
      background: none;
      border: 0;
      outline: none;
      font-size: 20px;
      line-height: 57px;
      color: #65889B;
    }
  }
  .btn {
    min-width: 174px;
    height: 50px;
    padding: 0 30px;
    @include backgroundSec('bg-btn-send.png');
    margin-left: 15px;
    line-height: 48px;
    text-align: center;
    font-family: EmergeBFW01-Regular;
    color: #1B1F22;
    font-weight: bold;
    font-size: 30px;
  }
}
.btn_self_servies {
  max-width: 240px;
  position: absolute;
  right: 946px;
  bottom: 8px;
  height: 42px;
  line-height: 40px;
  font-size: 24px;
  background: #BAAE9C;
  border: 1px solid #CFC3AA;
  border-radius: 2px;
  & ::v-deep .auto-font-size span {
    color: #282320;
    display: inline-block;
    position: relative;
    padding: 2px 15px 0 45px;
    &::before {
      content: '';
      width: 27px;
      height: 29px;
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-43%);
      background: url('~@/assets/img/ss/icon_robot.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  &:active {
    transform: scale(0.92);
  }
}

@media all and (orientation: portrait) {
  .btn_self_servies {
    margin: 0 8px 0 -25px;
    max-width: 120px;
    position: unset;
  }
  .bottom-wrap {
    height: 173px;
  }
  .container {
    width: 100%;
    padding: 0 30px;
  }
  .hot-words {
    margin-bottom: 20px;
  }
  .user-input {
    .btn {
      min-width: auto;
      max-width: 140px;
      margin: 0 -25px 0 5px;
    }
  }
}
</style>