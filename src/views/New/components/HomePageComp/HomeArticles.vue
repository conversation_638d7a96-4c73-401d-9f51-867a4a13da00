<template>
  <div class="home-articles" v-if="baseInfo.article_lists.length > 0 && baseInfo.article_lists[0].art_title">
    <div class="title" v-if="baseInfo.temp_title">
      <span><auto-font-size :text="baseInfo.temp_title"></auto-font-size></span>
    </div>
    <div class="article-list">
      <div class="scroll">
        <div v-for="(item, index) in baseInfo.article_lists" :key="`${index}_${item.art_id}`" :class="['item', readedList.indexOf(item.art_id) > -1 ? 'readed' : '']" @click="itemClick(item)">
          <div class="hot" v-if="item.art_label === 1"></div>
          <div class="new" v-if="item.art_label === 2"></div>
          {{ item.art_title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useStorage } from '@vueuse/core'
import type { Ref } from 'vue'
interface IArticleItem {
  art_id: number
  art_title: string
  art_label: number
}
export default defineComponent({
  name: 'HomeArticles'
})
</script>
<script setup lang="ts">
const { state } = useStore()
const router = useRouter()
const baseInfo = computed(() => state.baseInfo)
const readedList = useStorage('readedList', []) as Ref<number[]>
const itemClick = (item: IArticleItem) => {
  if (readedList.value.indexOf(item.art_id) === -1) {
    readedList.value.push(item.art_id)
  }
  router.push({
    path: '/newcs/articleDetail',
    query: { id: item.art_id }
  })
}
</script>

<style lang="scss" scoped>
.home-articles {
  margin-top: 12px;
  flex: 1;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  .title {
    line-height: 1.1;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      height: 23px;
      font-size: 24px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #D4AD5B;
      padding: 0 195px;
      position: relative;

      &::before, &::after {
        content: "";
        position: absolute;
        width: 186px;
        height: 2Px;
        top: 11px;
        left: 0;
        @include backgroundSec('soc/bg-title-icon.png');
      }
      &::after {
        left: auto;
        right: 0;
        transform: rotateY(180deg);
      }
    }
  }
  .article-list {
    flex: 1;
    overflow: hidden;
    margin-top: 14px;
    .scroll {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .item {
      position: relative;
      margin-bottom: 10px;
      font-size: 18px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #9ABBCD;
      width: 100%;
      height: auto;
      @include backgroundSec('soc/bg-article-item.png');
      line-height: 40px;
      padding-left: 30px;
      padding-right: 43px;
      padding-bottom: 1px;
      &.readed {
        color: #65889B;
      }
      .hot {
        position: absolute;
        width: 36px;
        height: 19.5px;
        right: 0px;
        @include backgroundSec('soc/icon-hot.png');
      }
      .new {
        position: absolute;
        width: 36px;
        height: 19.5px;
        right: 0px;
        @include backgroundSec('soc/icon-new.png');
      }
    }
  }
}

@media all and (orientation : portrait) {
  .home-articles {
    padding-bottom: 25px;
    .title {
      margin: 10px 0;
    }
    .article-list {
      .item {
        line-height: 70px;
      }
    }
  }
}
</style>