<template>
  <div class="home-swiper" v-if="baseInfo.banner_lists.length > 0 && baseInfo.banner_lists[0].image_url" :key=upKey>
    <van-swipe class="my-swipe" :show-indicators="false" indicator-color="white" ref="swipeIns" :autoplay="3000">
      <van-swipe-item v-for="(item, i) in baseInfo.banner_lists" :key="`${i}_${item.image_title}`" @click="itemLick(item)">
        <div class="item" v-lazy:background-image="item.image_url">
          <div class="title"><span>{{ item.image_title }}</span></div>
        </div>
        {{ item.image_title }}
      </van-swipe-item>
    </van-swipe>
    <div class="page-arr icon-next" @click="changePage(1)"></div>
    <div class="page-arr icon-prev" @click="changePage(-1)"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { BANNER_LINK_TYPE } from '@/enum'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import type { SwipeInstance } from 'vant'
interface IItem {
  id?: number
  image_url: string
  image_title: string
  image_group: number
  art_id: number
  jump_url: string
}
export default defineComponent({
  name: 'HomeSwiper'
})
</script>
<script setup lang="ts">
const { state } = useStore()
const baseInfo = computed(() => state.baseInfo)
const swipeIns = ref<SwipeInstance>()
const router = useRouter()
const changePage = (flag: number) => {
  flag > 0
    ? swipeIns.value?.next()
    : swipeIns.value?.prev()
}
const upKey = new Date().getTime()
const itemLick = (item: IItem) => {
  // 1：文章ARTICLE 3：urlURL 4：游戏GAME
  if (item.image_group === BANNER_LINK_TYPE.ARTICLE) {
    // 文章跳转
    if (item.art_id < 1) return
    router.push({ path: '/newcs/articleDetail', query: { id: item.art_id } })
  } else if (item.image_group === BANNER_LINK_TYPE.URL) {
    // 跳转 url
    // jsBridge.jumpToUrl(item.jump_url)
  } else if (item.image_group === BANNER_LINK_TYPE.GAME) {
    // 跳转游戏场景
    // jsBridge.jumpToGame(item.jump_url)
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
const handleResize = () => {
  // 在屏幕方向变化时，重新初始化轮播图 (解决图片和标题样式错误 无法正确渲染问题)
  setTimeout(() => {
    swipeIns.value?.resize()
  }, 200)
}
</script>

<style lang="scss" scoped>
.home-swiper {
  width: 100%;
  height: 134px;
  position: relative;
  .my-swipe {
    width: 100%;
    height: 100%;
    .item {
      height: 100%;
      background-size: cover;
      background-position: center center;
      position: relative;
    }
    .title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 38px;
      background: linear-gradient(to right, rgba(0,0,0,0) 0%, rgba(0,0,0,.1) 20%, rgba(0,0,0,.8) 50%, rgba(0,0,0,.1) 80%, rgba(0,0,0,0) 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 38px;
      span {
        max-width: 80%;
        font-size: 24px;
        font-family: Source Han Serif CN;
        font-weight: 600;
        color: #B4CCE4;
        position: relative;
        display: flex;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0 25px;
        // &::before, &::after {
        //   content: "";
        //   position: absolute;
        //   width: 14px;
        //   height: 14px;
        //   top: 12px;
        //   left: 0;
        //   @include backgroundSec('soc/icon-slider-title.png');
        // }
        // &::after {
        //   left: auto;
        //   right: 0;
        // }
      }
    }
    // ::v-deep(.van-swipe__indicators) {
    //   bottom: 45px;
    //   .van-swipe__indicator {
    //     border: 0;
    //     width: 14px;
    //     height: 14px;
    //     @include backgroundSec('soc/icon-page.png');
    //     &.van-swipe__indicator--active {
    //       background-color: transparent !important;
    //       @include backgroundSec('soc/icon-page-active.png');
    //     }
    //   }
    // }
  }
  .page-arr {
    width: 34px;
    height: 41px;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    @include backgroundSec('soc/icon-next.png');
    right: 19px;
    cursor: pointer;
    &.icon-prev {
      right: auto;
      left: 19px;
      transform: translateY(-50%) rotate(180deg);
    }
  }
}

@media all and (orientation: portrait) {
  .home-swiper {
    height: 160px;
    .my-swipe {
      .title {
        span {
          max-width: 85%;
          font-size: 19px;
        }
      }
    }
  }
}
</style>