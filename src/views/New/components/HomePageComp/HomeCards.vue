<template>
  <div class="history-bar" @click="toHistory">
    <div style="position: relative;">
      <div class="bar-title"  :class="{ 'has-unread': isRead && readCount > 0 }" :data-unread="readCount">
        <span>{{ $t('text_history') }}</span>
        <span class="more-icon"></span>
      </div>
      <!-- <div class="red-p" v-if="isRead"></div> -->
    </div>
  </div>
  <div class="home-cards">
    <div class="card-scroll">
      <div class="card" v-for="(v, k) in baseInfo.card_lists" :key="k" @click="cardClickHandle(v, k)">
        <img :src="v.image_url">
        <div class="card-title">
          <!-- <auto-font-size :text="v.image_title"></auto-font-size> -->
           {{ v.image_title }}
        </div>
      </div>
    </div>
    <div class="history-card" @click="toHistory">
      <div style="position: relative;">
        <!-- <div class="red-p" v-if="isRead"></div> -->
        <div class="img-box" :class="{ 'has-unread': isRead && readCount > 0 }" :data-unread="readCount"></div>
        <div class="card-title">
          <!-- <auto-font-size :text="$t('history_text_short')"></auto-font-size> -->
           {{ $t('history_text_short') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { checkUnread } from '@/api/smart'

export default defineComponent({
  name: 'HomeCards'
})
</script>
<script setup lang="ts">
import type { TNewCard } from '@/enum/types'
const { state } = useStore()
const baseInfo = computed(() => state.baseInfo)
const router = useRouter()
const isRead = ref(false)
const readCount = ref(0)
checkUnread({}).then((res: Record<string, unknown>) => {
  isRead.value = true
  readCount.value = res.notice_count as number || 0
}, () => {
  isRead.value = false
  readCount.value = 0
})
const toHistory = () => {
  router.push({
    name: 'NewHistory'
  })
}
const cardClickHandle = (v: TNewCard, index: number) => {
  // card_group 关联子卡片(1：图片 4：列表) 2：关联知识 3：关联表单分类
  if (v.card_group === 1 || v.card_group === 4) {
    router.push({
      name: 'SecondLevel',
      query: {
        temp_id: baseInfo.value.temp_id,
        card_idx: index,
        title: v.image_title
      }
    })
  } else if (v.card_group === 2) {
    router.push({
      path: '/newcs/articleDetail',
      query: {
        art_id: v.art_id,
        card_group: v.card_group
      }
    })
  } else if (v.card_group === 3) {
    router.push({
      path: '/newcs/answersDetail',
      query: {
        cat_id: v.ticket_cat_id
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.home-cards {
  width: 100%;
  box-sizing: border-box;
  background-color: #182129;
  margin-top: 14px;
  .history-card {
    width: 139px;
    height: 120px;
    background-color: #222f3a;
    float: right;
    position: relative;
    margin-top: 3px;
    transform: scale(0.95);
    .red-p {
      position: absolute;
      top: 15px;
      right: 35px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      @include backgroundSec('red_p.png');
    }
    .img-box {
      position: relative;
      width: 70px;
      height: 70px;
      background: url('~@/assets/img/soc/history-btn.png') no-repeat center center;
      background-size: 100% 100%;
      object-fit: contain;
      margin: 5px auto 0px;
      z-index: -10;
      &.has-unread {
        &:after {
          content: attr(data-unread);
          position: absolute;
          top: -10px;
          right: -10px;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          width: 32px;
          height: 32px;
          padding-bottom: 2px;
          font-weight: 500;
          font-size: 20px;
          color: #FFF7E8;
          @include backgroundSec('history_count.png');
        }
      }
    }
  }
  .card-scroll {
    width: 700px;
    float: left;
    overflow-x: auto;
    overflow-y: hidden;
    // white-space: nowrap;
    padding: 3px 5px;
    display: flex;
    flex-direction: row;
    .card {
      flex: 1;
      height: 120px;
      background: #1c262f;
      display: inline-block;
      transform: scale(0.95);
      img {
        display: block;
        width: 62%;
        height: 62%;
        object-fit: contain;
        margin: 0px auto 0px;
        z-index: -10;
      }
    }
  }
  .card-title {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 30px;
    word-break: break-word;
    font-size: 16px;
    color: #E2C885;
    box-sizing: border-box;
    line-height: 18px;
  }
}
.history-bar {
  display: none;
}

@media all and (orientation : portrait) {
  .home-cards {
    .card-scroll {
      width: 100%;
      padding: 20px 15px;
      display: grid;
      justify-content: space-between;
      grid-template-columns: repeat(auto-fill, 22%);
      grid-gap: 15px 0;
    }
    .history-card {
      display: none;
    }
  }
  .history-bar {
    background: #182129;
    height: 54px;
    margin: 15px 0 0;
    font-size: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .bar-title {
      margin-right: 20px;
      position: relative;
      display: flex;
      align-items: center;
      cursor: pointer;
      svg {
        position: relative;
        left: -3px;
        width: 26px;
        color: #c2a862;
      }
      &.has-unread {
        &:after {
          content: attr(data-unread);
          position: absolute;
          top: -25px;
          right: -35px;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          width: 32px;
          height: 32px;
          padding-bottom: 2px;
          font-weight: 500;
          font-size: 20px;
          color: #FFF7E8;
          @include backgroundSec('history_count.png');
        }
      }
      .more-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        border-style: solid;
        border-width: 3px 3px 0 0;
        transform: rotate(45deg);
      }
    }
    .red-p {
      position: absolute;
      top: 0;
      right: 12px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      @include backgroundSec('red_p.png');
    }
  }
}
</style>
