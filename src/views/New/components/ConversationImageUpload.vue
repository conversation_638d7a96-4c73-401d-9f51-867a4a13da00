<template>
  <div class="conversation-image-upload">
    <div class="upload-field"
         :class="{ 'disabled': isPictureAnswered }"
         @click.stop="handleUploadClick">
      <span>{{ uploading ? $t("text_uploading") + ' ...' : $t('text_upload_image') }}</span>
    </div>

    <!-- 已上传图片预览 -->
    <!-- <div class="image-preview" v-if="imgList.length > 0">
      <div class="image-item" v-for="(img, index) in imgList" :key="index">
        <img :src="img.url" alt="" @click="previewImg(img)" />
        <div class="delete-icon" @click="removeUploadedImg(index)"></div>
      </div>
    </div> -->

    <!-- 上传失败提示 -->
    <!-- <div v-if="failed" class="upload-failed">
      <van-icon name="warning-o" />
      <div>{{ $t("text_upload_failed") }}</div>
      <div class="delete-icon" @click="removeFailedImg"></div>
    </div> -->

    <!-- 上传中提示 -->
    <!-- <div v-if="uploading" class="upload-status">
      {{ $t("text_uploading") }}...
    </div> -->

    <!-- SDK上传 - 隐藏但可触发 -->
    <div v-if="!uploading && !isPictureAnswered && imgList.length < maxCount && !isH5Upload && !browserIsPC" class="sdk-upload" ref="sdkUploadRef"></div>

    <!-- H5上传 - 隐藏但可触发 -->
    <div v-if="(isH5Upload || browserIsPC) && !isPictureAnswered && !uploading && imgList.length < maxCount" class="h5-upload">
      <van-uploader
        :after-read="afterRead"
        :result-type="'file'"
        accept="image/*"
        :max-count="maxCount"
        :multiple="true"
        ref="h5UploadRef"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ConversationImageUpload'
})
</script>

<script setup lang="ts">
import { ref, reactive, toRefs, getCurrentInstance, computed, onBeforeUnmount, defineProps, withDefaults, defineEmits } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { ImagePreview, Toast } from 'vant'
import { uploadSever } from '@/utils/upload'
import { browser } from '@/utils'
import { QuestionGetListItem } from '@/enum/ticketConversation'

// eslint-disable-next-line
const { proxy: _this, appContext } = getCurrentInstance() as any
const { commit, state } = useStore()
const { t: $t } = useI18n()

// 浏览器环境检测
const browserIsPC = browser.version.isWindows || false

// 判断图片问题是否已回答
const isPictureAnswered = computed(() => {
  const currentQuestionGetList = state.conversation.currentQuestionGetList;
  const pictureQuestion = currentQuestionGetList.find((item: QuestionGetListItem) => item.question_key === 'picture');
  console.log('isPictureAnswered pictureQuestion', pictureQuestion);
  return pictureQuestion?.has_answer || false;
});

// 上传状态机
const isUploading = computed(() => state.uploading)

// 组件属性
const props = withDefaults(defineProps<{
  isH5Upload?: boolean;
  maxCount?: number;
}>(), {
  isH5Upload: false,
  maxCount: 5
})

// 组件事件
const emit = defineEmits<{
  (event: 'success', images: Array<Record<string, string>>): void;
  (event: 'remove', index: number): void;
  (event: 'update:status', status: string): void;
}>()

// 组件状态
const data = reactive({
  uploading: false,
  failed: false,
  imgList: [] as Array<Record<string, string>>
})

// 上传组件引用
const h5UploadRef = ref()
const sdkUploadRef = ref()

// SDK交互初始化
const initSDKCallback = () => {
  window.chooseFinish = params => {
    chooseFinish(params)
  }
  window.backImgUrl = (params: string): void => {
    uploadFinish(params)
  }
}

// 组件销毁要更改状态机
onBeforeUnmount(() => {
  data.uploading = false
  commit('setUploadingType', false) // 同步上传状态机
})

// 上传服务器地址配置
const uploadUrl: Record<string, string> = {
  'fpcs-web-test.funplus.com': 'https://upload-api-test.funplus.com',
  'fpcs-web-stage.funplus.com': 'https://upload-api-test.funplus.com',
  'fpcs-web.funplus.com': 'https://upload-global.funplus.com',
  'fpcs-web-test.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
  'fpcs-web-stage.funplus.com.cn': 'https://upload-api-test.funplus.com.cn',
  'fpcs-web.funplus.com.cn': 'https://upload-api.funplus.com.cn',
  'fpcs-web-tx.kingsgroup.cn': 'https://upload-global.funplus.com',
  'fpcs-web-tx.yoo-mei.cn': 'https://upload-global.funplus.com',
  'fpcs-web.nenglianghe.cn': 'https://upload-api.funplus.com.cn',
}

// 显示上传选项
const handleUploadClick = () => {
  if (isPictureAnswered.value) {
    return;
  }
  showUploadOptions();
}

const showUploadOptions = () => {
  if (data.uploading) {
    Toast($t('text_uploading'))
    return
  }

  // if (data.imgList.length >= props.maxCount) {
  //   Toast(`最多上传${props.maxCount}张图片`)
  //   return
  // }

  if (props.isH5Upload || browserIsPC) {
    // 触发H5上传
    h5UploadRef.value.$el.querySelector('input').click()
  } else {
    // 触发SDK上传
    uploadImg()
  }
}

// H5上传
const afterRead = (file: Record<string, unknown>) => {
  data.uploading = true
  emit('update:status', 'uploading')
  commit('setUploadingType', true)

  uploadSever(file.file as string, 'https://upload-global.funplus.com/api/storage/put').then((res: []) => {
    data.imgList.push(...res)
    _this.$forceUpdate()
    console.log('afterRead res', res)
    emit('success', res)
    emit('update:status', `已上传${data.imgList.length}张图片`)

    // 重置上传状态机
    data.uploading = false
    commit('setUploadingType', false)
  }, err => {
    console.log('failed', err)

    Toast($t('text_upload_failed'))
    data.failed = true
    emit('update:status', 'failed')

    // 重置上传状态机
    data.uploading = false
    commit('setUploadingType', false)
  })
}

// 上传图片(SDK)
const uploadImg = () => {
  // 如果上传流程被占用，则提示上传中
  if (isUploading.value) {
    Toast($t('text_uploading'))
  } else {
    initSDKCallback()
    const path = 'openFile'
    const count = props.maxCount - data.imgList.length
    const param = {
      count: count < 5 ? count : 5,
      albumType: 1,
      storage: 1,
      fileUploadUrl: uploadUrl[location.host] ? uploadUrl[location.host] : 'https://upload-global.funplus.com'
    }
    appContext.config.globalProperties.$utils.jsBridge(path, param)
  }
}

// SDK选择图片后callback
const chooseFinish = (params: unknown): void => {
  if (params) {
    commit('setUploadingType', true) // 同步上传状态机
    data.uploading = true
    emit('update:status', 'uploading')
  }
}

// SDK上传完成callback
const uploadFinish = (params: string): void => {
  if (JSON.parse(params).code === 0) {
    data.imgList.push(...JSON.parse(params).data)
    _this.$forceUpdate()
    emit('success', JSON.parse(params).data)
    emit('update:status', `已上传${data.imgList.length}张图片`)
  } else {
    data.failed = true
    emit('update:status', 'failed')
  }
  data.uploading = false
  commit('setUploadingType', false) // 同步上传状态机
}

// 图片预览
const previewImg = (img: Record<string, string>): void => {
  ImagePreview({
    images: [img.url],
    teleport: 'body',
    closeable: props.isH5Upload,
    showIndex: false
  })
}

// 删除图片
const removeUploadedImg = (index: number): void => {
  data.imgList.splice(index, 1)
  emit('remove', index)
  emit('update:status', data.imgList.length > 0 ? `已上传${data.imgList.length}张图片` : '')
}

// 删除上传失败的图片
const removeFailedImg = () => {
  data.failed = false
  emit('update:status', '')
}

// 获取上传图片列表
const getImageList = () => {
  return data.imgList
}

// 暴露方法给父组件
defineExpose({
  getImageList
})

const { uploading, failed, imgList } = toRefs(data)
</script>

<style lang="scss" scoped>
.conversation-image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .upload-field {
    width: 300px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("~@/assets/img/conversation/field-btn.png") no-repeat center center;
    background-size: 100% 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 22px;
    color: #313940;
    text-align: center;
    font-style: normal;
    cursor: pointer;
    margin-bottom: 10px;

    &.disabled {
      filter: grayscale(100%);
      :deep(.van-field__control) {
        color: #313940;
        -webkit-text-fill-color: #313940;
      }
    }
  }

  .image-preview {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 10px;
    width: 100%;

    .image-item {
      position: relative;
      margin: 0 10px 10px 0;
      width: 80px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .delete-icon {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &::before, &::after {
          content: '';
          position: absolute;
          width: 12px;
          height: 2px;
          background-color: #fff;
          transform: rotate(45deg);
        }

        &::after {
          transform: rotate(-45deg);
        }
      }
    }
  }

  .upload-status, .upload-failed {
    margin-top: 10px;
    font-size: 14px;
    color: #9ABBCD;
  }

  .upload-failed {
    color: #ff4d4f;
    display: flex;
    align-items: center;

    .van-icon {
      margin-right: 5px;
    }

    .delete-icon {
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .sdk-upload, .h5-upload {
    display: none;
  }

  :deep(.van-uploader__input-wrapper) {
    display: none;
  }
}

@media all and (orientation: portrait) {
  .conversation-image-upload {
    .image-preview {
      .image-item {
        width: 60px;
        height: 60px;
      }
    }
  }
}
</style>
