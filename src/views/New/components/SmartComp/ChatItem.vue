<template>
  <div class="chat-item user-q" v-if="!props.isWelcome && props.chatItem.question">
    <div class="avatar" :class="{ default: !props.avatar }" :style="{ backgroundImage: `url(${props.avatar})` }"></div>
    <div class="msg">
      {{ props.chatItem.question }}
    </div>
  </div>
  <div class="chat-item gpt-a">
    <div class="avatar">
      <div class="icon-thinking" v-if="props.chatItem.type === 'gpt' && !props.finish">
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
      </div>
    </div>
    <div class="rich-wrap">
      <div class="rich-wrap-bg"></div>
      <div class="rich-body">
        <div class="question" v-if="!props.isWelcome && props.chatItem.question && !StoreState.global.isShowChat">[{{ props.chatItem.question }}]</div>
        <!-- 自助查询 -->
        <Card v-if="props.chatItem.from === 'selfService'" :msgItem="JSON.parse(props.chatItem.answer.replace(String(props.chatItem.guideContent) , ''))" @itemClick="compClickHandle"></Card>
        <!-- 知识管理-评价-展示new 点踩后展示 -->
        <Dislike v-else-if="props.chatItem.from === 'Dislike'" :msgItem="props.chatItem" @dislikeSuccess="dislikeSuccess"></Dislike>
        <!-- 正常回答 -->
        <div v-else>
          <div v-if="props.chatItem.type === 'gpt'" class="answer gpt-answer" :class="{ finish: props.finish, empty: !state.showContent, isShowChatClass: StoreState.global.isShowChat }" v-html="state.showContent" @click.stop="contentClick"></div>
          <div v-if="props.chatItem.type === 'article'" class="answer" ref="richMsgEle">
            <RichText :msg="state.showContent" source-type="chat_rec_question"></RichText>
          </div>
          <!-- 推荐内容 -->
          <div v-if="props.finish && props.chatItem.recomQuestion?.length">
            <div class="divider"></div>
            <div class="question">[{{ $t('text_recommend_content') }}]</div>
            <div class="answer">
              <div v-for="(v, k) in props.chatItem.recomQuestion" :key="k">
                <span class="recommend-item" @click.stop="recommendClick(v)">{{ v }}</span>
              </div>
            </div>
          </div>
        </div>
        <template v-if="props.finish && state.showContent && props.chatItem.id && !props.isWelcome && !props.chatItem.noShowLike && props.chatItem.from !== 'Dislike'">
          <div class="divider"></div>
          <LikeOrDislike :id="props.chatItem.id" @click="likeClick"></LikeOrDislike>
        </template>
        <div class="disclaimer" v-if="props.finish && props.chatItem.disclaimer">
          <span>{{ $t('text_disclaimer_copy') }}</span>
          <span @click.stop="errCorrect" class="btn_correct">{{ $t('btn_error_correction') }}</span>
        </div>
        <CorrectPopup :show="showCorrect" v-if="showCorrect" @close="closeCorrect" :question="props.chatItem.question" :answer="state.showContent" />
        <!-- 问题未解决按钮 -->
        <div class="problem-box" v-if="props.finish && props.chatItem.problemUnresolved">
          <div class="problem" @click.stop="gotoTicket">
            <auto-font-size :text="$t('btn_problem_unresolved')"></auto-font-size>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分类选择弹窗 -->
  <ClassifyPopup
    :show="state.showClassifyPopup"
    :levelId="state.levelId"
    @close="closeClassifyPopup"
    @selectCategory="handleSelectCategory"
  />

  <!-- 子分类选择弹窗 -->
  <ClassifySubPopup
    :show="state.showSubPopup"
    :parentCategory="state.parentCategory"
    @close="closeAllPopups"
    @back="backToMainPopup"
    @selectCategory="handleSelectCategory"
  />

  <!-- 三级分类选择弹窗 -->
  <ClassifyThirdPopup
    :show="state.showThirdPopup"
    :parentCategory="state.subCategory"
    @close="closeAllPopups"
    @back="backToSubPopup"
    @selectCategory="handleSelectCategory"
  />
</template>

<script setup lang="ts">
import LikeOrDislike from '../LikeOrDislike.vue'
import RichText from '../RichText.vue'
import CorrectPopup from './CorrectPopup.vue'
import ClassifyPopup from '../ClassifyPopup.vue'
import ClassifySubPopup from '../ClassifySubPopup.vue'
import ClassifyThirdPopup from '../ClassifyThirdPopup.vue'
import type { LIKE_TYPES, TChatItem } from '@/enum/types'
import markdownRenderer from '@/utils/markdownRenderer'
import { pushChatAppraise } from '@/api/new'
// import { pushLog } from '@/utils/soc/log'
// import { LIKE_CONF } from '@/enum'
import { ref, reactive, onMounted, watchEffect, withDefaults, defineProps, defineEmits, getCurrentInstance, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
// import jsBridge from '@/utils/jsBridge'
import { ImagePreview } from 'vant'
import Card from './Card.vue'
import Dislike from './Dislike.vue'
import { useThrottleFn } from '@vueuse/core'

const router = useRouter()
const { state: StoreState } = useStore()
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog

const props = withDefaults(defineProps<{
  chatItem: TChatItem
  finish: boolean
  showContent?: string
  avatar?: string,
  isWelcome?: boolean
}>(), {
  finish: true,
  showContent: '',
  isWelcome: false
})

const likeClick = (type: LIKE_TYPES[keyof LIKE_TYPES]) => {
  if (type === 2 && (props.chatItem.showEval === '1' || props.chatItem.showEval === '3')) {
    emits('dislikeOptList', true)
  }
  // 提交答案和log上报，需要去掉引导语
  const postAnswer = props.chatItem.guideContent ? props.chatItem.answer.replace(props.chatItem.guideContent , '') : props.chatItem.answer
  setLog({
    event: 'elfin_appraise',
    position: 'faq',
    button: type,
    query: props.chatItem.question,
    answer: props.chatItem.answer,
    replied_from: props.chatItem.answerMode,
    message_id: props.chatItem.id,
    // 是否展示赞踩打点(兼容历史记录，之前的都展示) 0：展示 1：不展示
    isShowLike: props.chatItem.noShowLike ? 1 : 0,
    // 是否展示免责文案&纠错按钮(兼容历史记录，之前的都不展示) 1：展示 0：不展示
    isDisclaimer: props.chatItem.disclaimer ? 1 : 0
  })
  // pushLog({
  //   event: type === LIKE_CONF.like ? 'funbot_thumbup' : 'funbot_thumbdown',
  //   type: props.chatItem.sourceType,
  //   message_id: props.chatItem.id,
  //   answer_mode: props.chatItem.answerMode,
  //   content: {
  //     question: props.chatItem.question,
  //     answer: postAnswer
  //   },
  //   if_effective: 1 - props.chatItem.is_default
  // })
  // gpt 回答反馈需要上报
  if (props.chatItem.type === 'gpt') {
    pushChatAppraise({
      message_id: props.chatItem.id,
      appraise: type, // 1: 赞, 2: 踩
      from: props.chatItem.from,
      query: props.chatItem.question,
      answer: postAnswer
    })
  }
}

interface CategoryItem {
  id?: number,
  label?: string,
  level?: number,
  tpl_id?: number,
  children?: Array<CategoryItem>,
  relate_type?: number
}

const state = reactive({
  showContent: '',
  // 弹窗相关状态
  showClassifyPopup: false,
  showSubPopup: false,
  showThirdPopup: false,
  levelId: null as number | null,
  parentCategory: null as CategoryItem | null,
  subCategory: null as CategoryItem | null
})

watchEffect(() => {
  let content = ''
  if (!props.finish) {
    content = props.showContent
  } else {
    content = props.chatItem.answer
  }
  state.showContent = props.chatItem.type === 'gpt' ? markdownRenderer(content) : content
})

onMounted(() => {
  state.showContent = props.chatItem.type === 'gpt' ? markdownRenderer(props.chatItem.answer) : props.chatItem.answer
})

const contentClick = (event: Event) => {
  const ele = (event.target || event.srcElement) as HTMLElement
  // 两次未命中的"提交工单"(多语言兜底话术) 和 命中词库的"提交工单"(拼接在后面)点击 都是span标签
  if (ele.nodeName && ele.nodeName === 'SPAN') {
    // 命中词库的"提交工单"带有catId属性 跳后台配置的层级分类
    // 判断是否在工单灰度下，如果是则显示弹窗，否则跳转页面
    if (StoreState.global.isShowChat) {
      // 在灰度下，显示弹窗
      if (ele.getAttribute('catId')) {
        state.levelId = Number(ele.getAttribute('catId'))
        state.showClassifyPopup = true
      } else {
        state.levelId = null
        state.showClassifyPopup = true
      }
    } else {
      // 不在灰度下，保持原来的跳转页面方式
      const path = '/newcs/answersDetail'
      if (ele.getAttribute('catId')) {
        router.push({
          path,
          query: {
            level_id: Number(ele.getAttribute('catId'))
          }
        })
      } else {
        router.push({
          path
        })
      }
    }
  }
  if (ele.nodeName && ele.nodeName === 'A') {
    // event.preventDefault()
    // const jumpValue = ele.getAttribute('href')
    // jumpValue && jsBridge.jsBridge(jumpValue)
  }
  if (ele.nodeName && ele.nodeName === 'IMG') {
    const src = ele.getAttribute('src')
    if (src) {
      ImagePreview({
        images: [src],
        showIndex: false
      })
    }
  }
}

// 关闭一级分类弹窗
const closeClassifyPopup = () => {
  state.showClassifyPopup = false
}

// 关闭所有弹窗
const closeAllPopups = () => {
  state.showClassifyPopup = false
  state.showSubPopup = false
  state.showThirdPopup = false
}

// 返回到主分类弹窗
const backToMainPopup = () => {
  state.showSubPopup = false
  state.showClassifyPopup = true
}

// 返回到子分类弹窗
const backToSubPopup = () => {
  state.showThirdPopup = false
  state.showSubPopup = true
}

// 处理分类选择
const handleSelectCategory = (item: CategoryItem, type: string) => {
  if (type === 'sub') {
    // 选择了有子分类的一级分类，显示子分类弹窗
    state.parentCategory = item
    state.showClassifyPopup = false
    state.showSubPopup = true
  } else if (type === 'third') {
    // 选择了有子分类的二级分类，显示三级分类弹窗
    state.subCategory = item
    state.showSubPopup = false
    state.showThirdPopup = true
  } else if (type === 'answer') {
    // 选择了没有子分类的分类，跳转到问题解答页
    closeAllPopups()
    router.push({
      path: '/newcs/answersDetail',
      query: {
        cat_id: item.id,
        origin: '2'
      }
    })
  }
}

// 纠错弹窗
const showCorrect = ref<boolean>(false)
const errCorrect = () => {
  showCorrect.value = true
}
const closeCorrect = () => {
  showCorrect.value = false
}

// 点击未解决  30s内只有一次生效
const gotoTicket = useThrottleFn(() => {
  emits('problemUnres')
}, 30000)

const emits = defineEmits(['recommClick', 'dislikeOptList', 'problemUnres'])
// 推荐内容点击
const recommendClick = (v: string) => {
  emits('recommClick', v, 'recom')
}

// 自助查询卡片/list点击
let fuzzyNum = 0
const compClickHandle = (item: Record<string, unknown>) => {
	const itemText = item.card_response_text || item.question_desc
	// 统计模糊匹配次数，1次/2次
	if (item.answer_type === 100) {
		fuzzyNum = fuzzyNum > 1 ? 1 : fuzzyNum + 1
		// 统计模糊匹配情况下，点击"以上都不是"打点
		setLog({
			position: 'ask',
			action: 'fuzzy_unmatch',
			input: item.real_question_desc,
			fuzzy_num: fuzzyNum
		})
	} else { // 排除"以上都不是"的情况下的点击时间的打点
		setLog({
			button: 1,
			action: 'click',
			result: 1,
			position: 'ask',
			input: itemText,
			question_id: new Date().getTime()
		})
	}
  emits('recommClick', item.card_title, 'selfService')
}

// 选择点踩原因提交后
const dislikeSuccess = (reason: string) => {
  emits('dislikeOptList', false, reason, props.chatItem.id)
}

// 监听路由变化，关闭所有弹窗
watch(() => router.currentRoute.value.path, () => {
  closeAllPopups()
})

// 组件卸载前关闭所有弹窗
onBeforeUnmount(() => {
  closeAllPopups()
})
</script>

<style lang="scss" scoped>
@mixin writtingCss () {
  position: absolute;
  content: '';
  display: inline-block;
  width: 78px;
  height: 79px;
  background-color: red;
  bottom: 0;
  transform: translateX(-20px) rotateY(45deg);
  @include backgroundSec('soc/icon-pen.png');
  -webkit-box-reflect: below 0px -webkit-linear-gradient(bottom, rgba(255,255,255,0.3) 0%, transparent 40%, transparent 100%);
}
.chat-item {
  margin-bottom: 25px;
}
.avatar {
  position: absolute;
  width: 70px;
  height: 70px;
  border-radius: 50%;
}
.user-q {
  min-height: 80px;
  padding: 0 120px;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  .avatar {
    right: 0;
    top: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    &.default {
      background-image: url('~@/assets/img/soc/icon-avatar.png') !important;
    }
  }
  .msg {
    word-break: break-all;
    max-height: 100%;
    padding: 20px;
    border-radius: 6px;
    font-size: 22px;
    font-family: PingFang SC;
    background: rgba(172, 193, 222, 1);
    font-weight: 400;
    line-height: 1.3;
    color: #1B1F22;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      top: 18px;
      left: 100%;
      border-style: solid;
      border-color: transparent;
      border-left-width: 22px;
      border-left-color: rgba(172, 193, 222, 1);
      border-top-width: 9px;
      border-top-color: transparent;
      border-bottom-width: 9px;
      border-bottom-color: transparent;
      border-right-width: 0;
    }
  }
}
.gpt-a {
  min-height: 100px;
  padding: 18px 120px;
  padding-left: 110px;
  position: relative;
  .avatar {
    top: 20px;
    left: 0;
    @include backgroundSec('soc/icon-gpt.png');
    .icon-thinking {
      position: absolute;
      width: 39px;
      height: 34px;
      @include backgroundSec('soc/icon-thinking.png');
      right: -19px;
      top: -25px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 8px;
      .dot {
        width: 7px;
        height: 4px;
        border: 2px;
        margin: 0 1px;
        background-color: #000000;
        &.dot-1 { animation: dotAnimate1 infinite 1s; }
        &.dot-2 { animation: dotAnimate2 infinite 1s; }
        &.dot-3 { animation: dotAnimate3 infinite 1s; }
      }
    }
  }
  .rich-wrap {
    width: 584px;
    padding: 15px;
    position: relative;
    font-size: 0;
    .rich-body {
      padding: 5px 10px 0;
      position: relative;
      word-break: break-word;
      .disclaimer {
        font-size: .12rem;
        font-family: Adobe Heiti Std;
        color: #5d5d5e;
        .btn_correct {
          display: inline-block;
          background-color: #9ABBCD;
          padding: 0 5px;
          margin-left: 5px;
          color: #1B1F22;
          border-radius: 1px;
          cursor: pointer;
        }
      }
      .problem-box {
        display: flex;
        justify-content: end;
        .problem {
          font-size: .13rem;
          text-align: center;
          margin: 20px 0 0;
          padding: 5px 2px;
          width: 28%;
          color: #5d5d5e;
          cursor: pointer;
          border: 1px solid #5d5d5e;
          border-radius: 3px;
        }
      }
    }
    .rich-wrap-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: 6px;
      border: 1px solid rgba(0, 0, 0, 0.33);
      background: rgba(24, 37, 47, 1);
      &::after {
        content: "";
        position: absolute;
        top: 20px;
        right: 100%;
        border-style: solid;
        border-color: transparent;
        border-right-width: 22px;
        border-right-color: rgba(24, 37, 47, 1);
        border-top-width: 9px;
        border-top-color: transparent;
        border-bottom-width: 9px;
        border-bottom-color: transparent;
        border-left-width: 0;
      }
    }
    .question {
      word-break: break-all;
      font-size: 22px;
      font-family: PingFang SC;
      font-weight: normal;
      color: #D3AD5B;
      padding-bottom: 10px;
    }
    .answer {
      font-family: PingFang SC;
      color: #9ABBCD;
      padding-bottom: 1px;
      position: relative;
       font-size: 22px;
      font-weight: 400;
      line-height: 1.3;
      &.gpt-answer {
        // 前端提交工单的兜底话术中的span标签样式
        ::v-deep span {
          text-decoration: underline;
          color: #D3AD5B;
          cursor: pointer;
        }
        &.empty {
          height: 33px;
          position: relative;
          &::after {
            background: none !important;
          }
        }
        &.empty::after,
        &:not(.finish)>:not(ol):not(ul):not(pre):last-child::after {
          @include writtingCss();
        }

        ::v-deep(.ql-align-right) {
          text-align: right;
        }
        ::v-deep(.ql-align-center) {
          text-align: center;
        }
        ::v-deep(img) {
          margin: 5px auto;
          max-width: 100%;
        }
        ::v-deep(a) {
          font-size: 22px;
          color: rgba(225, 86, 11, 1);
          text-decoration: underline;
          cursor: pointer;
          &:active, &:visited {
            color: rgba(225, 86, 11, 1);
          }
          i {
            display: inline-block;
            width: 18px;
            height: 18px;
            @include backgroundSec('soc/icon-link.png');
          }
        }
      }
      ::v-deep(p) {
        margin-bottom: 5px;
        position: relative;
      }
      ::v-deep(ol),
      ::v-deep(ul) {
        margin-bottom: 5px;
        position: relative;
      }
      ::v-deep(li) {
        list-style: disc;
        list-style-position: inside;
        position: relative;
        p {
          display: inline;
        }
      }
      .recommend-item {
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
  .divider {
    width: 100%;
    height: 1px;
    margin: 15px 0 10px;
    @include backgroundSec('soc/bg-line.png');
  }
}

@supports (selector(:has(*))) {
  .gpt-a .rich-wrap .answer.gpt-answer:not(.finish)::v-deep(ol:last-child>li:last-child:not(:has(*>li))::after) {
    // @include writtingCss();
  }
}
@keyframes dotAnimate1 {
  0% { background-color: rgba(0, 0, 0, 1); }
  50% { background-color: rgba(0, 0, 0, 0); }
  99% { background-color: rgba(0, 0, 0, 1); }
}
@keyframes dotAnimate2 {
  0% { background-color: rgba(0, 0, 0, .5); }
  25% { background-color: rgba(0, 0, 0, 1); }
  75% { background-color: rgba(0, 0, 0, 0); }
  100% { background-color: rgba(0, 0, 0, .5); }
}
@keyframes dotAnimate3 {
  0% { background-color: rgba(0, 0, 0, 0); }
  50% { background-color: rgba(0, 0, 0, 1); }
  100% { background-color: rgba(0, 0, 0, 0); }
}

@media all and (orientation : portrait) {
  .gpt-a {
    .rich-wrap {
      width: 70vw;
    }
  }
}
</style>
<style lang="scss">
@media all and (orientation: portrait) {
  .van-image-preview {
    width: 100% !important;
  }
}
</style>
<style lang="scss" scoped>
.isShowChatClass {
  :deep(span) {
    // display: table;
    // margin: 30px auto;
    // padding: 10px 30px;
    // background: url("~@/assets/img/conversation/field-btn.png") no-repeat center center;
    // background-size: 100% 100%;
    // font-family: PingFangSC, PingFang SC;
    // font-weight: 500;
    // font-size: 22px;
    // color: #313940!important;
    // text-align: center;
    // cursor: pointer;
    // text-decoration: none!important;
  }
}
</style>
