<template>
  <div class="dislike-wrapper">
    <div v-if="Array.isArray(JSON.parse(props.msgItem.answer))">
      <div class="title">{{ $t('text_not_solve_reason') }}</div>
      <div class="reason-list">
        <div
          class="reason-item"
          v-for="(item, index) in JSON.parse(props.msgItem.answer)"
          :key="index"
          @click.stop="changeReason(item.key)"
        >
          <div class="reason-item__icon" :class="{ 'select': item.key === selectReason }"></div>
          <span>{{ item.desc }}</span>
        </div>
        <div class="reason-submit" @click.stop="submit">
          <auto-font-size :text="$t('text_submit')"></auto-font-size>
        </div>
      </div>
    </div>
    <div v-else class="reason-end">
      <span v-if="props.msgItem.catList?.length && props.msgItem.selectReason === 'DislikeUnResolved'">{{ $t('text_dislike_ticket') }}<span @click.stop="goToTickets" style="color:#F5C133;text-decoration:underline;">{{ $t('text_submit_cstickets') }}</span></span>
      <span v-else>{{ $t('text_dislike_tip') }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, reactive, toRefs, defineEmits, getCurrentInstance } from 'vue'
import { newDislikeSave } from '@/api/smart'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'

interface dataT {
  isRequesting: boolean,
  selectReason: string
}
export default defineComponent({
  name: 'Dislike'
})
</script>
<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const { t: $t } = useI18n()
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const props = defineProps<{
  msgItem: Record<string, unknown> & {
    catList: string[],
  }
}>()

const data: dataT = reactive({
  isRequesting: false,
  selectReason: ''
})
const emits = defineEmits(['dislikeSuccess'])

const changeReason = (val: string) => {
  data.selectReason = val
}
// 提交原因
const submit = () => {
  if (data.isRequesting) return
  if (!data.selectReason) {
    Toast($t('text_reason_select'))
    return
  }
  data.isRequesting = true
  const params = {
    message_id: props.msgItem.id,
    reason_key: data.selectReason
  }
  newDislikeSave(params).then((res: Record<string, unknown>) => {
    // Toast($t('text_reason_get'))
    setLog({
      type: 'submit_dislike_reason_trigger',
      message_id: props.msgItem.id,
      is_succeed: 'true',
      reason_key: data.selectReason
    })
    emits('dislikeSuccess', selectReason.value)
  }).catch((err: string) => {
    Toast(err)
    setLog({
      type: 'submit_dislike_reason_trigger',
      message_id: props.msgItem.id,
      is_succeed: 'false',
      reason_key: selectReason
    })
  }).finally(() => {
    data.isRequesting = false
  })
}

const goToTickets = () => {
  router.push({
    path: '/newcs/answersDetail',
    query: {
      dislike_tickets: JSON.parse(props.msgItem.catList)
    }
  })
}

const { selectReason } = toRefs(data)
</script>

<style lang="scss" scoped>
.dislike-wrapper {
  .title {
    padding-bottom: 15px;
    font-size: 20px;
    border-bottom: 1px solid rgba(98, 101, 109, 0.36);
    word-break: break-word;
  }
  .reason-list {
    padding-bottom: 60px;
    .reason-item {
      padding: 8px 0;
      font-size: 20px;
      color: #E9BE75;
      word-break: break-word;
      display: flex;
      align-items: center;
      span {
        margin-left: 10px;
        cursor: pointer;
      }
      &__icon {
        width: 20px;
        height: 20px;
        background: url('~@/assets/img/icon_not_select.png') no-repeat;
        background-size: 100% 100%;
      }
      .select {
        width: 20px;
        height: 20px;
        background: url('~@/assets/img/icon_select.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .reason-submit {
      font-size: 20px;
      width: 80px;
      height: 32px;
      line-height: 32px;
      border-radius: 3px;
      position: absolute;
      padding: 0px 5px;
      right: 5px;
      bottom: 5px;
      color: #EADDBF;
      background: rgb(76, 78, 79);
      text-align: center;
    }
  }
  .reason-end {
    font-size: 20px;
    color: #A9A292;
    padding-bottom: 10px;
  }
}
</style>