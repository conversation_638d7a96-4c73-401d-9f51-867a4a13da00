<!--
 * @Author: chao.wu
 * @Date: 2024-05-29 16:21:58
 * @Last Modified by: chao.wu
 * @Last Modified time: Do not edit
-->
<template>
  <van-popup :show="props.show" closeable close-icon="close" teleport="body" @close="closePopup" :style="{ height: 'auto', width: '50%', background: '#324852' }">
    <div class="popup-title">{{ $t('text_correct_content') }}</div>
    <van-form class="form-wrapper">
      <div class="label-container">
        <div
          v-for="v in labelList"
          :key="v.id"
          class="label"
          :class="{ select: labels === v.id }"
          @click="selectLabel(v.id)"
        >
          {{ v.text }}
        </div>
      </div>
      <van-field
        ref="textarea"
        style="width: 95%;"
        v-model="content"
        type="textarea"
        rows="4"
        autosize
        maxlength="200"
        show-word-limit
        :placeholder="$t('text_enter_correction')"
      />
    </van-form>
    <div class="button-wrapper">
      <div class="submit-btn" @click="handleAppraise">{{ $t("text_submit") }}</div>
    </div>
  </van-popup>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'CorrectPopup'
})
</script>

<script setup lang="ts">
import { getCorrection } from '@/api/new'
import { defineProps, defineEmits, ref, reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'
const { t: $t } = useI18n()

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  question: {
    type: String,
    default: ''
  },
  answer: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['close'])
const closePopup = () => {
  emits('close')
}

interface dataT {
  labels: number | null,
	content: string,
  locked: boolean
}
const data: dataT = reactive({
  labels: null,
  content: '',
  locked: false
})
const { labels, content } = toRefs(data)
const selectLabel = (label: number) => {
  data.labels = label
}

const labelList = [
  { id: 1, text: $t('text_content_error') },
  { id: 2, text: $t('text_incomplete') },
  { id: 3, text: $t('DislikeOther') }
]
const textarea = ref()
const handleAppraise = () => {
  // 解决安卓机器点击提交按钮触发键盘弹起的问题
  textarea.value.blur()
  if (data.locked) {
    // '评价提交中\n请稍后'
    Toast($t('text_submiting'))
    return
  }
  if (!data.labels) {
    Toast($t('text_select_reason'))
    return
  }
  if (!data.content) {
    Toast($t('text_enter_correction'))
    return
  }
  const params = {
    question: props.question,
    answer: props.answer,
    error_type: data.labels,
    content: data.content
  }
  data.locked = true
  getCorrection(params).then(() => {
    Toast($t('text_success'))
    data.locked = false
    closePopup()
  }).catch((err: string) => {
    Toast(err)
    data.locked = false
  })
}
</script>

<style lang="scss" scoped>
.popup-title {
  text-align: center;
  font-size: .22rem;
  margin-top: 25px;
  color: #9ABBCD;
}
.label-container {
  font-size: .18rem;
	.label {
		width: auto;
		float: left;
		margin: 25px 0px 20px 20px;
		padding: 5px 10px;
		color: #9ABBCD;
		border: 2px solid #9ABBCD;
		&.select {
			color: #D3AD5B;
			border: 2px solid #D3AD5B;
		}
	}
}
.form-wrapper {
	& ::v-deep(.van-cell) {
		background-color: rgba(124, 155, 160, 0.1);
		border: 2px solid #a0b8c5;
    margin: 0 auto;
		padding: 10px;
		color: #9ABBCD;
	}
	& ::v-deep(.van-field__control) {
    padding: 5px 0;
		font-size: .18rem;
    line-height: .23rem;
		color: #cbd3d7;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #a0b8c5;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #a0b8c5;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #a0b8c5;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #a0b8c5;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #a0b8c5;
		}
	}
}
</style>
<style lang="scss">
@media all and (orientation: portrait) {
  .van-popup {
    // width: 90% !important;
  }
}
</style>
