<template>
  <!-- 一级二级卡片 -->
  <div class="card_box">
    <!-- 卡片提示语 -->
    <div v-if="props.msgItem.card_prompt_title" class="card_prompt">{{ props.msgItem.card_prompt_title }}</div>
    <!-- show_type === 1 则以卡片形式展示数据 -->
    <div v-if="props.msgItem.show_type === 1" class="cards_wraper">
      <div :class="['card_item', animateMark === index ? 'rubberBand' : '']"
        v-for="(item, index) in props.msgItem.children" :key="index"
        @click.stop.prevent="cardClick(item, index)">
        <div style="position:relative;height:100%;width:100%">
          <div class="card_img" :style="{ 'background-image': `url(${item.card_image})` }"></div>
          <div class="card_text">
            <auto-font-size :text="item.card_title"></auto-font-size>
          </div>
        </div>
      </div>
    </div>
    <!-- 否则则以列表形式展示数据 -->
    <div class="card_list_wraper" v-else>
      <p class="list_item" v-for="(item, index) in props.msgItem.children" :key="index"
        @click="cardClick(item)">
        {{ index + 1 }}.
        {{ item.card_title }}
      </p>
    </div>
  </div>
  <!-- 三级数据 -->
  <!-- 因为在interceptor.ts中有自助查询接口的 历史逻辑 特殊处理了返回内容，所以有code的可作为判断条件 且这里没有好的判断条件 -->
  <div v-if="props.msgItem.code === 0" class="content-box">
    <table class="content" cellspacing="0" v-if="props.msgItem.data.detail?.length">
      <tr class="row head">
        <th class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ item.desc }}</th>
      </tr>
      <tr class="row" v-for="(v, k) in props.msgItem.data.detail" :key="k">
        <td class="item" v-for="item in props.msgItem.data.meta" :key="item.key">{{ v[item.key.substring(20, item.key.length-4)] }}</td>
      </tr>
    </table>
    <span v-else class="title">{{ $t('text_no_last_day_record', { num: filterDay }) }}</span>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, reactive, toRefs, defineEmits } from 'vue'
interface dataT {
  animateMark: number
}
export default defineComponent({
  name: 'Card'
})
</script>
<script lang="ts" setup>
const props = defineProps<{
  msgItem: any
}>()
const data: dataT = reactive({
  animateMark: -1
})
// 获取session中的查询天数
const filterDay = sessionStorage.getItem('filterDay')

const emit = defineEmits<{
  (event: 'item-click', itemInfo: Record<string, unknown>): void
}>()
// 卡片点击
const cardClick = (item: Record<string, unknown>, index?: number) => {
  if (data.animateMark !== -1) return false
  if (index !== undefined) {
    data.animateMark = index
  }
  emit('item-click', item)
  setTimeout(() => {
    data.animateMark = -1
  }, 600)
}

const { animateMark } = toRefs(data)
</script>

<style lang="scss" scoped>
.card_title {
  font-size: 18px;
  margin-top: 10px;
  margin-bottom: 20px;
  color: #CECFC9;
  font-weight: 200;
  overflow: hidden;
}
.card_prompt {
  font-size: 20px;
  color: #CECFC9;
}
.cards_wraper {
  display: flex;
  flex-wrap: wrap;
}
.card_item {
  width: 130px;
  margin: 15px 8px 50px;
  .card_img {
    width: 100px;
    height: 100px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
  }
  .card_text {
    width: 130px;
    height: 40px;
    line-height: 40px;
    position: absolute;
    margin-top: 10px;
    background: url('~@/assets/img/card_t.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
    box-sizing: border-box;
    padding: 0px 10px;
    font-size: 18px;
  }
}
.list_item {
  font-size: 20px;
  color: #F5C133;
  font-weight: 300;
  text-decoration: underline;
  line-height: 23px;
  margin-top: 13px;
}
@-webkit-keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  15% {
    -webkit-transform: scale3d(1.05, 0.75, 1);
    transform: scale3d(1.05, 0.75, 1);
  }
  30% {
    -webkit-transform: scale3d(0.75, 1.05, 1);
    transform: scale3d(0.75, 1.05, 1);
  }
  45% {
    -webkit-transform: scale3d(1.05, 0.85, 1);
    transform: scale3d(1.05, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  15% {
    -webkit-transform: scale3d(1.05, 0.75, 1);
    transform: scale3d(1.05, 0.75, 1);
  }
  30% {
    -webkit-transform: scale3d(0.75, 1.05, 1);
    transform: scale3d(0.75, 1.05, 1);
  }
  45% {
    -webkit-transform: scale3d(1.05, 0.85, 1);
    transform: scale3d(1.05, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
  -webkit-animation-duration: 0.6s;
  animation-duration: 0.6s;
}

.content-box {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto;
  &::-webkit-scrollbar {
    display: block;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #d6cccc; /* 设置滚动条轨道的背景颜色 */
  }
  &::-webkit-scrollbar-thumb {
    background: #888; /* 设置滚动条滑块的背景颜色 */
  }
  .content {
    background-color: rgba(0, 0, 0, .3);
    .row {
      height: 50px;
      font-size: 20px;
      &:nth-child(odd) {
        background: #363636;
      }
      .item {
        text-align: left;
        word-break: keep-all;
        line-height: 30px;
        white-space: nowrap;
        padding-left: 30px;
      }
      .item:first-child {
        padding-left: 10px;
      }
      .item:last-child {
        padding-right: 10px;
      }
    }
  }
  .title {
    font-size: 20px;
    color: #A9A292;
  }
}
</style>