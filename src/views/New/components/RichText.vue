<template>
  <div class="context" @click.stop="contentClick" v-html="props.msg"></div>
</template>

<script lang="ts">
import { defineProps, defineComponent } from 'vue'
import { useRouter } from 'vue-router'
import { ImagePreview } from 'vant'
export default defineComponent({
  name: 'RichText'
})
</script>
<script setup lang="ts">
const props = defineProps<{
  msg: string
  sourceType: string
}>()
const router = useRouter()
const contentClick = (event: Event) => {
  const ele = (event.target || event.srcElement) as HTMLElement
  if (ele.nodeName && ele.nodeName === 'A') {
    event.preventDefault()
    const jumpType = ele.className
    const jumpValue = ele.dataset.value
    // 站内文章跳转
    if (jumpType.indexOf('article-link') > -1) {
      // urlClickLog(props.sourceType, 'funbot_article', `${window.location.origin}/detail/${jumpValue}`)
      router.push({
        name: 'ArticleDetail',
        params: { id: jumpValue }
      })
    }
    // 站外跳转
    if (jumpType.indexOf('web-link') > -1) {
      // urlClickLog(props.sourceType, 'h5_page', jumpValue)
      // jumpValue && jsBridge.jumpToUrl(jumpValue)
    }
    // 游戏跳转
    if (jumpType.indexOf('game-goto') > -1) {
      // urlClickLog(props.sourceType, 'ingame', jumpValue)
      // jumpValue && jsBridge.jumpToGame(jumpValue)
    }
  }
  if (ele.nodeName && ele.nodeName === 'IMG') {
    const src = ele.getAttribute('src')
    if (src) {
      ImagePreview({
        images: [src],
        showIndex: false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.context {
  padding: 0 5px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  color: #9ABBCD;
  line-height: 1.5;
  font-size: 22px;

  ::v-deep(p) {
    margin-bottom: 15px;
    font-size: 22px;
  }
  ::v-deep(.ql-align-right) {
    text-align: right;
  }
  ::v-deep(.ql-align-center) {
    text-align: center;
  }
  ::v-deep(img) {
    margin: 5px auto;
    max-width: 100%;
  }
  ::v-deep(a.article-link) {
    font-size: 22px;
    color: rgba(22, 122, 185, 1);
    text-decoration: underline;
    cursor: pointer;
    &:active, &:visited {
      color: rgba(22, 122, 185, 1);
    }
  }
  ::v-deep(a.web-link) {
    font-size: 22px;
    color: rgba(225, 86, 11, 1);
    text-decoration: underline;
    cursor: pointer;
    &:active, &:visited {
      color: rgba(225, 86, 11, 1);
    }
    i {
      display: inline-block;
      width: 18px;
      height: 18px;
      @include backgroundSec('soc/icon-link.png');
    }
  }
  ::v-deep(a.game-goto) {
    font-size: 20px;
    color: #ffffff;
    display: inline-block;
    min-width: 91px;
    height: 30px;
    padding: 0 7px;
    line-height: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    // @include backgroundSec('bg-goto.png');
    cursor: pointer;
    border: 7.5px solid transparent;
    border-image: url('~@/assets/img/soc/bg-goto.png') 15 fill;
    &:active, &:visited {
      color: #ffffff;
    }
  }
}
</style>