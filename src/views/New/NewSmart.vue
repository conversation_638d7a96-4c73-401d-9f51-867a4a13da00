<template>
  <div class="bot-page">
    <div class="main">
      <div class="main-role"></div>
      <div class="container article-bg">
        <div class="nav-box" v-if="!state.global.isShowChat">
          <div class="nav-item" @click="goToHome"><auto-font-size :text="$t('tab_home')"></auto-font-size></div>
          <div class="nav-item active-nav"><auto-font-size :text="$t('tab_ai_new')"></auto-font-size></div>
        </div>
        <div class="wrap">
          <div class="scroll-box" ref="scrollBox" @touchstart="handleTouchStart">
            <ChatItem :isWelcome="true" :finish="true" :chat-item="{ answer: $t('sim_txt_first'), type: 'gpt' }"></ChatItem>
            <ChatItem v-for="(item, index) in chatHistory" :key="`${index}_${item.id}`" :chat-item="item" :finish="true" :avatar="chatState.avatar" @recommClick="(_, type) => sendGptQuestion(_, type)" @dislikeOptList="dislikeOptList" @problemUnres="problemUnres"></ChatItem>
            <ChatItem v-if="chatState.crtChat.question" :chat-item="chatState.crtChat" :finish="chatState.isFinish" :show-content="chatState.showContent" :avatar="chatState.avatar"></ChatItem>
          </div>
        </div>
        <!-- <div class="icon-down" v-if="chatState.isShowBottomBtn" @click="(chatState.canScroll = true) && smoothToBottom()"></div> -->
      </div>
    </div>
    <BottomWrap @submit="(_, type) => sendGptQuestion(_, type || 'chat_custom')" @hotWordClick="_ => sendGptQuestion(_, 'chat_keyword')"></BottomWrap>
  </div>
</template>

<!--
  聊天界面, 支持 文章/GPT 问答
  页面接收 query
    q           - 问题内容
    sourceType  - 来源(用于打点)
    type        - 类型(article/gpt)
    id          - 文章id ( 当 type=article 时，查询文章用 )
 -->

<script setup lang="ts">
import BottomWrap from './components/BottomWrap.vue'
import ChatItem from './components/SmartComp/ChatItem.vue'
import { useScroll, useThrottleFn } from '@vueuse/core'
import { reactive, watch, nextTick, onMounted, onUnmounted, onActivated, onDeactivated, ref, Ref, getCurrentInstance } from 'vue'
import type { TChatItem } from '@/enum/types'
import { getCards, getSelfQuery, dislikeOpt } from '@/api/smart'
import { getArticleDetail } from '@/api/new'
// import { chatResponeseTimeLog, returnAnswerLog } from '@/utils/soc'
import store from '@/store'
import { crypto } from '@/utils/crypto'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { Toast } from 'vant'
import { hasI18nKey } from '@/utils'
import { debouncedEventReport } from '@/server/interceptor'

const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
setLog({
	event: 'elfin_load_done',
  position: 'faq',
  result: 'success'
})

const { t: $t, messages: $messages, locale } = useI18n()
const messagesValue = $messages.value as Record<string, Record<string, string>>
const currentLocale = locale.value as string
const messagesLocale = messagesValue[currentLocale] as Record<string, string>
console.log('messagesLocale', messagesLocale)
const { userInfo } = store.state

const { state } = useStore()
// 修复json_data类型错误
interface JsonData {
  total_pay?: number;
  uid?: number;
  [key: string]: any;
}

let json_data: JsonData = {};
try {
  if (userInfo.json_data) {
    const decodedStr = decodeURIComponent(userInfo.json_data);
    if (decodedStr) {
      json_data = JSON.parse(decodedStr);
    }
  }
} catch (error) {
  console.error('解析json_data出错:', error);
  json_data = {};
}

// const uidFlag = json_data && json_data.uid ? Number(json_data.uid) : 0
// const userId = userInfo.uid || userInfo.fp_uid || userInfo.account_id || uidFlag
const chatHistory = ref([]) as Ref<TChatItem[]>
const crtChatHistory = ref({}) as Ref<TChatItem>
const controller = ref(null) as any
const pushHistory = (data: TChatItem) => {
  // if (JSON.stringify(chatHistory.value).length > 1024 * 1024 * 2 || chatHistory.value.length > 100) {
  //   chatHistory.value.shift()
  // }
  chatHistory.value.push(JSON.parse(JSON.stringify(data)))
  nextTick(() => smoothToBottom())
}

if (crtChatHistory.value.question) {
  pushHistory(crtChatHistory.value)
  crtChatHistory.value = {} as any
}
onActivated(() => {
  canUseScrollTo.value = !!scrollBox.value?.scrollTo
  initChat()
  // window.history.replaceState({
  //   path: window.location.origin + window.location.pathname
  // }, '', window.location.origin + window.location.pathname)
})
onDeactivated(() => {
  clearTimeout(timer)
})
console.log('NewSmart isShowChat', state.global.isShowChat)
const defaultChatState = {
  crtChat: {} as TChatItem,
  showContent: '',
  isFinish: false,
  timer: null as any,
  canScroll: true,
  avatar: '',
  isShowBottomBtn: false,
  reportData: null as any
}
const chatState = reactive({
  ...defaultChatState
})
const resetChatState = () => {
  chatState.crtChat = {} as any
  chatState.showContent = ''
}
const answerTimeStep = 100

// 平滑滚动到底部
const scrollBox = ref<HTMLElement>()
const canUseScrollTo = ref(false)
const scrollToFn = (top: number, behavior: ScrollBehavior = 'smooth') => {
  if (scrollBox.value) {
    if (canUseScrollTo.value) {
      scrollBox.value.scrollTo({ top: top, behavior: behavior })
    } else {
      scrollBox.value.scrollTop = top
    }
  }
}
const { directions, arrivedState, y } = useScroll(scrollBox)
const smoothToBottom = useThrottleFn((behavior: ScrollBehavior = 'smooth') => {
  if (scrollBox.value && chatState.canScroll) {
    scrollToFn(scrollBox.value.scrollHeight + 50, behavior)
  }
}, 50, false, true)

const loading = ref(false)

// H5临时兼容，后续删除
const sendArticleQuestion = (q: string, sourceType: string = '', id?: number) => {
  loading.value = true
  // const nowTime = new Date().getTime()
  getArticleDetail({ art_id: Number(id) })
    .then(async (res: any) => {
      if (!res.art_title) return
      // chatResponeseTimeLog(new Date().getTime() - nowTime, 'article')
      chatState.crtChat = {
        type: 'article',
        question: res.art_title,
        answer: '',
        like: undefined,
        sourceType: sourceType,
        id: 'null',
        answerMode: 'article',
        // is_default: 0
      }
      await nextTick()
      smoothToBottom('auto')
      chatState.crtChat.id = res.art_id
      chatState.crtChat.answer = res.art_content || ''
      // returnAnswerLog(chatState.crtChat)
      pushHistory(chatState.crtChat)
      resetChatState()
      nextTick(() => smoothToBottom())
    })
    .catch(() => {
      pushHistory(chatState.crtChat)
      resetChatState()
    })
    .finally(() => loading.value = false)
}

// 未命中次数计数：连续两次未回答上，给提工单的兜底话术，然后清空重新计数
let unMatchCount = 0
// answer为UNK且if_game_question为1，计数，否则清空
let unkUnMatchCount = 0
let answerRichcatList: string[] = []

// 检查函数：检查某个语料键是否存在
const checkKeyExists = (key: string): boolean => {
  return key in messagesLocale;
}

// 随机数优化：确保随机数是1或2，而不总是2
// 使用时间戳的最后一位数字来随机，如果是偶数则为1，奇数则为2
const getRandomNum = (): number => {
  // 获取当前时间戳最后一位
  const timestamp = new Date().getTime();
  const lastDigit = timestamp % 10;
  // 根据奇偶性返回1或2
  return lastDigit % 2 === 0 ? 1 : 2;
}

// gpt 问答， 返回后逐字显示结果
const sendGptQuestion = async (q: string, sourceType = '') => {
  if (loading.value) return
  chatState.canScroll = true
  if (!loading.value) {
    if (!chatState.isFinish && chatState.showContent.length > 0) {
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      pushHistory(crtChatHistory.value)
      crtChatHistory.value = {} as any
      chatState.crtChat = { ...defaultChatState.crtChat }
      chatState.showContent = ''
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => smoothToBottom(), 300)
    }
  }
  chatState.crtChat = {
    type: 'gpt',
    question: q,
    answer: '',
    like: undefined,
    sourceType: sourceType,
    id: 'null',
    answerMode: '',
    // is_default: 0,
    guideMark: false,
    guideContent: '',
    noShowLike: false,
    disclaimer: false,
    problemUnresolved: false,
    recomQuestion: []
  }

  await nextTick()
  smoothToBottom('auto')

  chatState.isFinish = false
  loading.value = true
  try {
    const abortController = new AbortController()
    controller.value = abortController
    const _S = 'Uc9Ud64Uf6Uc517354797809Uc27Ue6Ud69Uc70Ub053UaUd3Uc03608UdUa522Ub7160174'
    const apiKey = 'UcUd0893Uf053UaUd3Uc03608UdUa522Ub71601744UdUa9UfUf4855226U'
    const WXTimestamp = new Date().getTime()
		const params = {
      // jump_test: 1, // 表示不走gpt，回退到自研算法中
      query: q,
			json_data: JSON.stringify(json_data),
			scene: userInfo.scene ? Number(userInfo.scene) : 0,
			uuid: userInfo.device_id,
			device_type: userInfo.device_type ?? decodeURIComponent(userInfo.device_type),
			os_version: userInfo.os_version,
			rom_gb: userInfo.rom_gb,
			remain_rom: userInfo.remain_rom,
			app_version: userInfo.app_version,
			ram_mb: userInfo.ram_mb,
			network_info: userInfo.network_info,
			subchannel: userInfo.subchannel,
			pkgChannel: userInfo.pkgChannel,
			role_id: userInfo.role_id ? decodeURIComponent(userInfo.role_id) : '',
			nickname: userInfo.role_name ? decodeURIComponent(userInfo.role_name) : userInfo.name ? decodeURIComponent(userInfo.name) : '',
      total_pay: userInfo.pay_amount ? Number(userInfo.pay_amount) : json_data && json_data?.total_pay ? Number(json_data?.total_pay) : 0,
			ts: userInfo.openid ? WXTimestamp : userInfo.ts ? Number(userInfo.ts) : 0,
			game_id: userInfo.gameid ? Number(userInfo.gameid) : 0,
			sid: userInfo.sid,
			fpid: userInfo.fpid ? Number(userInfo.fpid) : 0,
			uid: userInfo.uid ? Number(userInfo.uid) : 0,
			os: userInfo.os,
			channel: userInfo.channel,
			lang: userInfo.lang,
			sdk_version: userInfo.sdk_version,
			game_token: userInfo.game_token,
			track_key: userInfo.openid ? '' + userInfo.uid + WXTimestamp : userInfo.track_key,
			country_code: userInfo.country_code ? userInfo.country_code : '',
			// fpx兼容字段
			fpx_app_id: userInfo.fpx_app_id ? userInfo.fpx_app_id : '',
			account_id: userInfo.account_id ? decodeURIComponent(userInfo.account_id) : '',
			fp_uid: userInfo.fp_uid ? userInfo.fp_uid : '',
			// 微信下游戏兼容字段
			openid: userInfo.openid ? userInfo.openid : '',
      // 私域打开客服字段
      zone_token: userInfo.zone_token,
      zone_from: userInfo.zone_from,
      log_source: userInfo.log_source,
      funplus_id: userInfo.funplus_id,
      properties: userInfo.properties,
    }

    const sign = crypto(apiKey, JSON.stringify(params), _S)
    // const nowTime = new Date().getTime()
    // 增加用户体验，随机展示引导语，等待接口返回答案
    // const randomGuides = 'guides' + Math.floor(Math.random() * 5)
    const randomGuides = 'guides0' // 引导语只有一种
    const guides = `${$t(randomGuides)}\n`
    chatState.crtChat.answer = guides
    chatState.crtChat.guideContent = guides
    chatState.crtChat.guideMark = true
    const guidesTimer = setInterval(() => {
      if (chatState.showContent === guides) {
        clearInterval(guidesTimer)
        chatState.crtChat.guideMark = false
        return
      }
      chatState.showContent = guides.substring(0, chatState.showContent.length + Math.ceil(Math.random() * 3 + 1))
    }, 300)
    const response = (await fetch('/backend/v3/elfin/chatstream', {
      method: 'POST',
      headers: {
        sign,
        'api-key': apiKey,
        lang: userInfo.lang,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params),
      signal: abortController.signal
    })) as any
    console.log("response", response);
    const headersObj: Record<string, string> = {};
    try {
      response.headers.forEach((value: string, key: string) => {
        headersObj[key] = value;
      });
    } catch (error) {
      console.error("headers转换失败", error);
    }
    console.log("headersObj", headersObj);
    // 存储每次请求结构体, 在请求结果返回后，上报请求结构体
    chatState.reportData = {
      traceId: response.headers?.get('trace-id') || '',
      res: {},
      req: params,
      headers: headersObj,
      url: response?.url
    }

    /*
      * dc兜底话术优化方案- 获取game_project，判断游戏对应的兜底话术
      * 1. 获取game_project - dc.global.prod
      * 2. 场景1:游戏外提问 - answer_type为unk，if_game_question为2，此时output_content为text_unknown_script_out_${gameProject}_${randomNum}
      * 3. 场景2:游戏内提问 -首次 - answer_type为unk，if_game_question为1，且unMatchCount不等于2，此时output_content为text_unknown_script_in_${gameProject}_${randomNum}
      * 4. 场景3: 模糊提问 - answer_type为vague_question，此时output_content为text_vague_question_script_${gameProject}_${randomNum}
      * 5. 兜底，如果game_project为空，则还是走原逻辑
    */
    const gameProject = response.headers.get('Game_project') || '' // mo_global  dc.global.prod
    console.log('response headers gameProject', gameProject)
    const Mid = response.headers.get('Message_id') ? response.headers.get('Message_id') : (new Date().getTime()).toString()
    // vague_question：模糊回答，不展示赞踩按钮
    // 或者：智能客服后台-词库维护-关联工单 设置是否去掉评价按钮 0：展示 1：不展示
    // 或者：后台 知识管理-评价信息 1展示 2不展示 3展示New
    if (response.headers.get('Answer_type') === 'vague_question' || response.headers.get('Need_appraise') === '1' || response.headers.get('Answer_rich_show_eval') === '2') {
      chatState.crtChat.noShowLike = true
    }
    chatState.crtChat.showEval = response.headers.get('Answer_rich_show_eval') // 只有命中知识库的内容 1展示 2不展示 3展示New；1，3才展示点踩原因列表和结束语；其它回答点踩不展示
    // gpt回答：openai/azure/豆包 需要显示免责文案+纠错按钮
    if (response.headers.get('Answer_type') === 'openai' || response.headers.get('Answer_type') === 'azure' || response.headers.get('Answer_type') === 'doubao') {
      chatState.crtChat.disclaimer = true
    }

    // 展示New 绑定工单分类列表（点踩原因提交后 出现提单入口）
    if (response.headers.get('Answer_rich_show_cat') !== '[]') {
      answerRichcatList = response.headers.get('Answer_rich_show_cat')
    } else answerRichcatList = []
    // dc兜底话术优化方案改动- 获取game_project，判断游戏对应的兜底话术
    if(gameProject) {
      const randomNum = getRandomNum();
      console.log('优化后的随机数:', randomNum); // 调试输出

      const AnswerType = response.headers.get('Answer_type') || '';
      const ifGameQuestion = response.headers.get('if_game_question') || '';

      // 构建语料键
      const textUnknownScriptIn = `text_unknown_script_in_${gameProject}_${randomNum}`;
      const textUnknownScriptOut = `text_unknown_script_out_${gameProject}_${randomNum}`;
      const textVagueQuestionScript = `text_vague_question_script_${gameProject}_${randomNum}`;

      // 预先检查语料是否存在
      const hasUnknownScriptIn = checkKeyExists(textUnknownScriptIn);
      const hasUnknownScriptOut = checkKeyExists(textUnknownScriptOut);
      const hasVagueQuestionScript = checkKeyExists(textVagueQuestionScript);

      console.log(`语料检查结果: 游戏内=${hasUnknownScriptIn}, 游戏外=${hasUnknownScriptOut}, 模糊=${hasVagueQuestionScript}`);

      if(AnswerType === 'unk') {
        // 不知道回答 去掉赞踩
        chatState.crtChat.noShowLike = true;
        // 不知道的场景，判断是否是游戏内问题：1游戏内提问 2游戏外提问

        // 游戏外提问统一走兜底话术，且不展示点赞，不展示免责文案
        if (ifGameQuestion === '2') {
          chatState.crtChat.noShowLike = true
          chatState.crtChat.disclaimer = false
          // 检查语料是否存在，不存在则使用默认语料
          const outputContent = (hasUnknownScriptOut ? textUnknownScriptOut : 'text_unknown_script');
          console.log('游戏外提问 答案key', outputContent, '存在性:', hasUnknownScriptOut);

          const responseJson = {code: 0, message: 'Answer', data: {
            output_content: $t(outputContent),
            from: AnswerType,
            message_id: Mid // 前端记录点赞用
          }};

          parseJsonResponse(responseJson, AnswerType, 'if_game_question');
          return;
        }

        let outputContent = '';

        // 游戏内提问
        if (ifGameQuestion === '1') {
          unMatchCount++;
          // 检查语料是否存在，不存在则使用默认语料
          if (unMatchCount === 2) {
            outputContent = 'text_unmatch';
          } else {
            outputContent = hasUnknownScriptIn ? textUnknownScriptIn : 'text_unknown_script';
          }
          console.log('游戏内提问 答案key', outputContent, '存在性:', hasUnknownScriptIn);
        }

        const responseJson = {code: 0, message: 'Answer', data: {
          output_content: $t(outputContent),
          from: 'unk',
          message_id: Mid // 前端记录点赞用
        }};

        parseJsonResponse(responseJson, 'unk', 'if_game_question');

        if (unMatchCount === 2) unMatchCount = 0
        return;
      }

      // vague_question - 模糊问题处理
      if (AnswerType === 'vague_question') {
        // 使用安全的方式获取语料
        const outputKey = hasVagueQuestionScript ? textVagueQuestionScript : 'text_vague_cover';
        console.log("模糊提问 答案key", outputKey, '存在性:', hasVagueQuestionScript);

        const responseJson = {code: 0, message: 'Answer', data: {
          output_content: $t(outputKey),
          from: 'vague_question',
          message_id: Mid // 前端记录点赞用
        }};

        parseJsonResponse(responseJson, 'vague_question', 'vague_question');
        return;
      }
    }

    if (response.headers.get('Answer_type') === 'unk') {
      // 不知道回答 去掉赞踩
      chatState.crtChat.noShowLike = true
      // 不知道的场景，判断是否是游戏内问题：1游戏内提问 2游戏外提问
      // 游戏内提问 连续两次未回答上，给提工单的兜底话术，然后清空重新计数
      if (response.headers.get('if_game_question') === '1') unMatchCount++
      const responseJson = {code: 0, message: 'Answer', data: {
        output_content: unMatchCount === 2 ? $t('text_unmatch') : $t('text_unknown_script'),
        from: 'unk',
        message_id: Mid // 前端记录点赞用
      }}
      parseJsonResponse(responseJson, 'unk', 'if_game_question')
      if (unMatchCount === 2) unMatchCount = 0
      return
    }
    // 推荐内容: 字段为空 or 有值
    if (response.headers.get('Rec_questions')) {
      // 禁止点赞踩
      chatState.crtChat.noShowLike = true
      // 后端编码空格会变成+号且无法解决，前端解码依然是+号，所以需要替换回来
      const questionsTemp = (decodeURIComponent(response.headers.get('Rec_questions')).slice(1, -1)).replace(/\+/g, ' ')
      chatState.crtChat.recomQuestion = questionsTemp.split('|||')
    }

    // 自助查询-获取卡片信息
    // Answer_card_id 代表一级/二级卡片的id
    if (response.headers.get('Answer_card_id')) {
      try {
        const res = await getCards({
          card_id: Number(response.headers.get('Answer_card_id'))
        })
        const responseJson = {code: 0, message: 'Answer', data: {
          output_content: JSON.stringify(res),
          from: 'selfService',
          message_id: Mid
        }}
        parseJsonResponse(responseJson, 'selfService', 'selfService')
        return
      } catch (error) {
        console.error(error)
      }
    }
    // 这是三级 点击请求历史数据
    if (response.headers.get('Answer_query_url_desc')) {
      sessionStorage.setItem('filterDay', response.headers.get('Answer_query_filter'))
      try {
        const resWait = await getSelfQuery({
          query_filter: Number(response.headers.get('Answer_query_filter')),
          query_url_desc: response.headers.get('Answer_query_url_desc')
        })
        // ss老定制客服历史逻辑：第一次请求这个接口比较慢 code === 10 数据排队处理中，间隔5s 请求两次接口才能确保拿到数据，第二次查同样的有缓存 可能只要请求一次
        if (resWait.code === 10) {
          Toast(resWait.msg)
          setTimeout(async () => {
            const res = await getSelfQuery({
              query_filter: Number(response.headers.get('Answer_query_filter')),
              query_url_desc: response.headers.get('Answer_query_url_desc')
            })
            const responseJson = {code: 0, message: 'Answer', data: {
              output_content: JSON.stringify(res),
              from: 'selfService',
              message_id: Mid
            }}
            parseJsonResponse(responseJson, 'selfService', 'selfService')
          }, 5000)
        } else {
          const responseJson = {code: 0, message: 'Answer', data: {
            output_content: JSON.stringify(resWait),
            from: 'selfService',
            message_id: Mid
          }}
          parseJsonResponse(responseJson, 'selfService', 'selfService')
        }
        return
      } catch (error) {
        console.error(error)
      }
    }

    const successMark = response.headers.get('Success_mark')
    if (successMark === '0') { // 0：逻辑失败(网络错误) 1：成功 2：未匹配结果
      setLog({
        event: 'elfin_ask_ans',
        action: sourceType,
        query: q,
        result: 'fail'
      })
      // crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
      resetChatState()
      // pushHistory(crtChatHistory.value)
      crtChatHistory.value = {} as any
      chatState.isFinish = true
      isScrolling.value = false
      loading.value = false
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      setTimeout(() => {
        chatState.canScroll = true
        smoothToBottom()
      }, 300)
      Toast($t('network_err'))
      return
    }
    const contentType = response.headers.get('Content-Type')
    // loading.value = false
    // const resTime = new Date().getTime() - nowTime
    if (contentType.includes('application/json')) {
      // chatResponeseTimeLog(resTime, 'history_database')
      const responseJson = await response.json()
      parseJsonResponse(responseJson, q, sourceType)
    } else if (contentType.includes('text/event-stream')) {
      // chatResponeseTimeLog(resTime, 'gpt')
      parseStreamResponse(response, q, sourceType)
    }
  } catch (error) {
    // crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
    resetChatState()
    // pushHistory(crtChatHistory.value)
    crtChatHistory.value = {} as any
    chatState.isFinish = true
    isScrolling.value = false
    loading.value = false
    chatState.timer && clearInterval(chatState.timer)
    chatState.timer = null
    setTimeout(() => {
      chatState.canScroll = true
      smoothToBottom()
    }, 300)
    Toast($t('network_err'))
  }
}
// 正常格式返回
const parseJsonResponse = ({ code, data }: { code: number, message: string, data: any }, q: string, sourceType: string) => {
  if (code !== 0) {
    crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
    resetChatState()
    pushHistory(crtChatHistory.value)
    crtChatHistory.value = {} as any
    chatState.isFinish = true
    isScrolling.value = false
    return
  }

  const res = data
  // 问答结果打点 (Json格式返回,有答案)
  setLog({
    event: 'elfin_ask_ans',
    action: sourceType,
    query: q,
    result: 'success',
    answer: res.output_content,
    is_replied: 1,
    replied_from: res.from,
    message_id: res.message_id,
    // 是否展示赞踩打点(兼容历史记录，之前的都展示) 0：展示 1：不展示
    isShowLike: chatState.crtChat.noShowLike ? 1 : 0,
    // 是否展示免责文案&纠错按钮(兼容历史记录，之前的都不展示) 1：展示 0：不展示
    isDisclaimer: chatState.crtChat.disclaimer ? 1 : 0
  })
  chatState.crtChat.answer += res.output_content
  chatState.crtChat.id = res.message_id
  chatState.crtChat.answerMode = res.from
  chatState.crtChat.from = res.from
  chatState.crtChat.catList = answerRichcatList
  crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
  // 请求结果返回后，上报请求结构体
  if (chatState.reportData) {
    chatState.reportData.res = chatState.crtChat.answer
    debouncedEventReport(chatState.reportData)
  }
  chatState.timer = setInterval(() => {
    // 若返回结果早于引导语展示，则等待引导语展示完毕
    if (chatState.crtChat.guideMark) return
    // 返回答案以后去除引导语
    if (chatState.showContent.indexOf(chatState.crtChat.guideContent as string) > -1) {
      chatState.crtChat.answer = chatState.crtChat.answer.replace(chatState.crtChat.guideContent as string, '')
      chatState.showContent = chatState.showContent.replace(chatState.crtChat.guideContent as string, '')
      crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
    }

    if (chatState.showContent === chatState.crtChat.answer) {
      loading.value = false
      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      pushHistory(crtChatHistory.value)
      crtChatHistory.value = {} as any
      resetChatState()
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => {
        chatState.canScroll = true
        smoothToBottom()
      }, 300)
      return
    }
    // 如果是 自助查询/点踩原因列表/点击问题未解决按钮返回的话术，直接展示全部，不逐字展示，这样展示速度会快很多
    if (sourceType === 'selfService' || sourceType === 'Dislike' || sourceType === 'problemUnresolved') {
      chatState.showContent = chatState.crtChat.answer
    } else {
      chatState.showContent = chatState.crtChat?.answer?.substring(0, chatState.showContent.length + Math.ceil(Math.random() * 3 + 1)) || ''
    }
    smoothToBottom()
  }, answerTimeStep)
}
// 流式格式返回
const parseStreamResponse = async (response: any, q: string, sourceType: string) => {
  // 获取各个字段
  chatState.crtChat.id = response.headers.get('Message_id')
  chatState.crtChat.answerMode = response.headers.get('From')
  chatState.crtChat.from = response.headers.get('From')
  // chatState.crtChat.is_default = Number(response.headers.get('Is_default'))
  chatState.crtChat.answer = ''
  // chatState.showContent = ''
  // crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat)) 当回答未完成时，不保存到历史记录中
  const data = response.body
  // 创建一个可读流，来读取字节流
  const reader = data.getReader()
  // 创建一个解码器，用于将字节流转换为字符串
  const decoder = new TextDecoder('utf-8')
  // 判断是否已经读取完毕
  let done = false
  chatState.timer = setInterval(async () => {
    // 若返回结果早于引导语展示，则等待引导语展示完毕
    if(chatState.crtChat.guideMark) return
    // 返回答案以后去除引导语
    if (chatState.showContent.indexOf(String(chatState.crtChat.guideContent)) > -1) {
      chatState.showContent = chatState.showContent.replace(String(chatState.crtChat.guideContent), '')
    }
    // 读取下一块数据
    try {
      const { value, done: readerDone } = await reader.read()
      if (value && !readerDone) {
        // 将字节流转换为字符串
        const char = decoder.decode(value)
        if (char === '\n' && chatState.showContent.endsWith('\n')) return
        if (char) {
          chatState.showContent += char
          chatState.crtChat.answer += char
        }
        smoothToBottom()
      }
      done = readerDone
    } catch (error) {
      done = true
    }
    if (done && chatState.crtChat.answer) {
      // (已修改为下面的case: 把之前返回contact的场景都变成UNK了)ST-AI精灵白名单灰度问题-处理方案："算法openai回答中会有联系客服，但是并无入口"：将算法返回的标识<span>contact</span>替换为"提交工单"
      if (chatState.crtChat.answer.indexOf('UNK') !== -1) {
        // 含有"提交工单"的回答不展示赞踩；词库、知识库除外(赞踩展示后台可控制)
        chatState.crtChat.noShowLike = true
        // 此case不展示赞踩 免责 纠错
        chatState.crtChat.disclaimer = false
        // 有推荐内容
        if (chatState.crtChat.recomQuestion?.length) {
          chatState.showContent = chatState.crtChat.answer.replace('UNK', $t('text_sorry_suggest'))
          chatState.crtChat.problemUnresolved = true
        } else {
          const ifGameQuestion = response.headers.get('if_game_question') || '' // 1:游戏内 2:游戏外
          const gameProject = response.headers.get('Game_project') || '' // mo_global  dc.global.prod
          const randomNum = getRandomNum()
          // 构建语料键
          const textUnknownScriptIn = `text_unknown_script_in_${gameProject}_${randomNum}`;
          const textUnknownScriptOut = `text_unknown_script_out_${gameProject}_${randomNum}`;

          // 预先检查语料是否存在
          const hasUnknownScriptIn = checkKeyExists(textUnknownScriptIn);
          const hasUnknownScriptOut = checkKeyExists(textUnknownScriptOut);

          console.log(`回答是UNK，语料检查结果: 游戏内=${hasUnknownScriptIn}, 游戏外=${hasUnknownScriptOut}`);

          // UNK-游戏外提问统一走兜底话术，且不展示点赞，不展示免责文案
          if (ifGameQuestion === '2') {
            chatState.crtChat.noShowLike = true
            chatState.crtChat.disclaimer = false
            // 检查语料是否存在，不存在则使用默认语料
            const outputContent = (hasUnknownScriptOut ? textUnknownScriptOut : 'text_unknown_script');
            console.log('回答是UNK，游戏外提问 答案key', outputContent, '存在性:', hasUnknownScriptOut);
            chatState.showContent = chatState.crtChat.answer.replace('UNK', $t(outputContent))
            // return;
          }

          // 如果是游戏内，则开始计数
          if (ifGameQuestion === '1') {
            unkUnMatchCount += 1
            let outputContent = ''
            // 如果是首次，则返回游戏内-首次提示语，否则展示兜底话术
            if (unkUnMatchCount === 2) {
              outputContent = $t('text_manual_handle')+`<span style="color:#F5C133;text-decoration:underline;">${$t('text_submit_cstickets')}</span>`
              unkUnMatchCount = 0
              console.log('游戏内提问-第二次 答案key，展示兜底话术且包含工单入口');
            } else {
              outputContent = hasUnknownScriptIn ? textUnknownScriptIn : 'text_unknown_script';
              console.log('游戏内提问-第一次 答案key，展示游戏内-首次提示语', outputContent)
            }
            chatState.showContent = chatState.crtChat.answer.replace('UNK', $t(outputContent))
          }

          // chatState.showContent = chatState.crtChat.answer.replace('UNK', $t('text_manual_handle')+`<span style="color:#F5C133;text-decoration:underline;">${$t('text_submit_cstickets')}</span>`)
        }
      }
      // 命中词库 并且 词库关联了工单分类(任意层级)  拼接"提交工单"（这里赞踩展示后台可控制，上面Need_appraise）
      // answer_ticket-触发自动化流程-"点击此处继续解决您的问题"
      if ((response.headers.get('Answer_type') === 'words_library' || response.headers.get('Answer_type') === 'answer_ticket') && response.headers.get('Cat_id') !== '0') {
        const catIdFlag = response.headers.get('Cat_id')
        // catNameFlag: true-后台知识库'知识管理'配置的关联工单(有可能该工单关联了自动化流程，但是只有三级) false-词库提交工单(任意层级，最后三级工单也可能关联自动化流程)
        const catNameFlag: boolean = response.headers.get('Answer_type') === 'answer_ticket'
        chatState.showContent += `<span style="color:#F5C133;text-decoration:underline;margin: 0 2px;" class="${state.global.isShowChat ? 'submit_cstickets-btn' : ''}" catId="${catIdFlag}">${$t(catNameFlag ? 'auto_flow_tip_text' : 'text_submit_cstickets')}</span>`
      }
      loading.value = false
      // 如果流式回答未结束时退出页面，则将回答保存到localStorage中
      // localStorage.setItem('crtChatHistory', JSON.stringify(chatState.crtChat))

      chatState.timer && clearInterval(chatState.timer)
      chatState.timer = null
      chatState.crtChat.answer = chatState.showContent
      // returnAnswerLog(chatState.crtChat)
      crtChatHistory.value = JSON.parse(JSON.stringify(chatState.crtChat))
      pushHistory(crtChatHistory.value)

      // 问答结果打点 (流式返回,有答案)
      setLog({
        event: 'elfin_ask_ans',
        action: sourceType,
        query: q,
        result: 'success',
        answer: chatState.crtChat.answer,
        is_replied: 1,
        replied_from: chatState.crtChat.answerMode,
        message_id: chatState.crtChat.id,
        // 是否展示赞踩打点(兼容历史记录(之前兼容localStorage记录 后面改成keep-alive形式了)，之前的都展示) 0：展示 1：不展示
        isShowLike: chatState.crtChat.noShowLike ? 1 : 0,
        // 是否展示免责文案&纠错按钮(兼容历史记录(同上)，之前的都不展示) 1：展示 0：不展示
        isDisclaimer: chatState.crtChat.disclaimer ? 1 : 0,
        // 推荐内容打点 1：有推荐内容 0：无推荐内容
        isRecommend: chatState.crtChat.recomQuestion?.length ? 1 : 0,
        // 推荐的内容
        recommendContent: chatState.crtChat.recomQuestion
      })

      // 请求结果返回后，上报请求结构体
      if (chatState.reportData) {
        chatState.reportData.res = chatState.crtChat.answer
        debouncedEventReport(chatState.reportData)
      }
      crtChatHistory.value = {} as any
      resetChatState()
      chatState.isFinish = true
      isScrolling.value = false
      setTimeout(() => {
        chatState.canScroll = true
        smoothToBottom()
      }, 300)
    }
  }, answerTimeStep)
}

// 当前回答变更时，滚动到底部
watch(() => chatState.showContent, () => {
  smoothToBottom('auto')
})

// 准确监听是否是回答问题中 用户滑动，而不是页面重排重绘的滚动行为
const isScrolling = ref<boolean>(false)
const handleTouchStart = () => {
  isScrolling.value = true
}
// 回答问题中用户滑动时，可自动滚动变为false
watch(() => directions.top, (v) => {
  if (v && !chatState.isFinish && isScrolling.value) chatState.canScroll = false
})
// 用户滚动到底部时，可自动滚动变为 true
watch(() => arrivedState.bottom, (v) => {
  v && (chatState.canScroll = true) && (isScrolling.value = false)
})
watch(y, () => {
  if (scrollBox.value && y.value < scrollBox.value.scrollHeight - (scrollBox.value.offsetHeight * 2)) chatState.isShowBottomBtn = true
  else chatState.isShowBottomBtn = false
})

// 推送内容返回的富文本文章，会出现图片异步加载导致不能正常滚动到底部，加一个定时器来循环到达底部的逻辑
let timer: any
let timerTimes = 0
const setInitScroll = () => {
  if (!scrollBox.value) return
  timer = setTimeout(() => {
    timerTimes += 1
    scrollBox.value && scrollToFn(scrollBox.value.scrollHeight + 50, 'auto')
    if (timerTimes <= 10 && !chatState.crtChat.type && chatState.canScroll) setInitScroll()
    else clearTimeout(timer)
  }, 300);
}
onUnmounted(() => {
  clearTimeout(timer)
})

const route = useRoute()
const initChat = () => {
  scrollBox.value && scrollToFn(scrollBox.value.scrollHeight + 50, 'auto')
  // 检测是否为跳转来的
  if (route.query.q) {
    const query = { ...route.query } as any
    if (query.type === 'gpt') {
      sendGptQuestion(query.q, query.sourceType)
    } else if (query.type === 'article') {
      sendArticleQuestion(query.q, query.sourceType, query.id)
    }
  }
  // 更新去除url中的参数,但是不能记录history
  router.replace({
    query: {}
  })
  setInitScroll()
}
const router = useRouter()
const goToHome = () => {
  router.replace({
    name: 'HomePage'
  })
}

// 知识管理-评价-展示new 点踩后展示点踩原因列表
const dislikeOptList = async (need: boolean, reason: string, mid: string = (new Date().getTime()).toString()) => {
  try {
    let contentObj = {}
    if (need) {
      contentObj = await dislikeOpt({})
    }
    const responseJson = {code: 0, message: 'Dislike', data: {
      output_content: JSON.stringify(contentObj),
      from: 'Dislike',
      message_id: mid
    }}
    parseJsonResponse(responseJson, 'Dislike', 'Dislike')
    chatState.crtChat.selectReason = reason
    return
  } catch (error) {
    console.error(error)
  }
}

// 点击未解决按钮后 展示话术
const problemUnres = () => {
  const responseJson = {code: 0, message: 'Answer', data: {
    output_content: $t('text_unmatch'),
    from: 'problemUnresolved',
    message_id: (new Date().getTime()).toString()
  }}
  parseJsonResponse(responseJson, 'problemUnresolved', 'problemUnresolved')
  chatState.crtChat.type = 'gpt'
  chatState.crtChat.noShowLike = true
}

onMounted(() => {
  chatHistory.value = []
  // canUseScrollTo.value = !!scrollBox.value?.scrollTo
  // initChat()
  // window.history.replaceState({
  //   path: window.location.origin + window.location.pathname
  // }, '', window.location.origin + window.location.pathname)
})
</script>

<style lang="scss" scoped>
.bot-page {
  position: relative;
  width: 100%;
  height: 100%;
  padding-bottom: 118px;
  .main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 118px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
  }
  .main-role {
    height: 531px;
    width: 398px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 1;
    left: -160px;
  }
  .container {
    width: 907px;
    height: 542px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: -130px;
    box-sizing: border-box;
    margin-bottom: 18px;
    padding: 26px 34px 11px;
    position: relative;
    right: 68px;
    .nav-box {
      width: 131px;
      position: absolute;
      top: 40px;
      right: -130px;
      .nav-item {
        box-sizing: border-box;
        width: 100%;
        height: 43px;
        padding: 0px 4px;
        margin-bottom: 10px;
        @include backgroundSec('soc/bg-nav.png');
        text-align: center;
        line-height: 43px;
        font-size: 22px;
        color: #B4CCE4;
        font-family: Source Han Serif CN;
        cursor: pointer;
        &.active-nav {
          color: #1B1F22;
          @include backgroundSec('soc/bg-nav-active.png');
        }
      }
    }
    .wrap {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-start;
    }
    .scroll-box {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
    .icon-down {
      position: absolute;
      right: 40px;
      bottom: 20px;
      width: 45px;
      height: 45px;
      @include backgroundSec('soc/icon-down.png');
    }
  }
}

@media all and (orientation : portrait) {
  .bot-page {
    .main {
      margin-top: calc(env(safe-area-inset-top) + var(--head-bar-height) + 75px) !important;
    }
    .main-role {
      display: none;
    }
    .container {
      margin: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 30px;
      right: 0;
      .nav-box {
        display: flex;
        width: 100%;
        position: absolute;
        top: -60px;
        left: 10px;
        .nav-item {
          box-sizing: border-box;
          width: 135px;
          height: 43px;
          padding: 0px 4px;
          margin-left: 20px;
          @include backgroundSec('soc/bg-nav.png');
          text-align: center;
          line-height: 43px;
          font-size: 24px;
          color: #B4CCE4;
          font-family: Source Han Serif CN;
          cursor: pointer;
        }
      }
    }
  }
}

.bot-page {
  :deep(.submit_cstickets-btn-wrapper) {
    // display: flex;
    // justify-content: flex-start;
    // margin: 30px 0;
    // &.flexcenter {
    //   justify-content: center;
    // }
    // .submit_cstickets-btn {
    //   display: inline-block;
    //   padding: 10px 30px;
    //   background: url("~@/assets/img/conversation/field-btn.png") no-repeat center center;
    //   background-size: 100% 100%;
    //   font-family: PingFangSC, PingFang SC;
    //   font-weight: 500;
    //   font-size: 22px;
    //   color: #313940!important;
    //   text-align: center;
    //   cursor: pointer;
    //   text-decoration: none!important;
    // }
  }
}
</style>
