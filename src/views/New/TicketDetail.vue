<template>
	<div class="answers-detail">
		<div class="main-role"></div>
		<div class="content-body article-bg">
			<div class="content">
				<van-collapse v-model="activeNames" :border="false">
					<!-- 表单信息 -->
					<van-collapse-item :name="'0'" :is-link="false" :border="false">
						<template #title>
							<div class="title" @click="imgRotate(0)" v-if="form.category">
								<span class="nav-select-img" :class="[imgState[0]?'open-rotate':'close-rotate']"></span>
								<span>{{ form.category }}</span>
							</div>
						</template>
						<div class="form">
							<div v-for="(value, name, index) in form.filed" :key="index">
								<div class="form-label">
									<div class="icon"></div>
									<span v-if="name !== 'isUid'">{{ name }}</span>
								</div>
								<div v-if="Array.isArray(value)" class="form-content">
									<div class="media-form-box">
										<div
											v-if="value.length && value[0].file_type === 'image'"
											class="img-wrap"
										>
											<!-- <img
												v-for="(img, index) in value"
												:key="index"
												:src="img.url"
												@click="previewImg(img)"
											/> -->
											<div v-for="(item, index) in value" :key="index">
												<img v-if="item.url.startsWith('https://kg-web-cdn')" :src="item.url" class="field-image" @click="previewImg(img)" />
												<span v-else>{{ item.url }}</span>
											</div>
										</div>
										<div
											v-if="value.length && value[0].file_type === 'video'"
											class="video-preview"
										>
											<fp-video
												:src="value[0].url"
											></fp-video>
										</div>
									</div>
								</div>
								<div v-else-if="name !== 'isUid'" class="form-content">
									<div class="text-form-box" v-html="value"></div>
								</div>
							</div>
						</div>
					</van-collapse-item>
					<!-- 对话信息展示区 -->
					<van-collapse-item class="ticketdetail-chat-info" name="chatInfo" :is-link="false" :border="false" v-if="form.commu?.length">
						<template #title>
							<div class="title" @click="chatImg = !chatImg">
								<span class="nav-select-img" :class="[chatImg?'open-rotate':'close-rotate']"></span>
								<span>{{ $t("text_talk_info") }}</span>
							</div>
						</template>
						<div class="ticketdetail-chat-info-content" v-for="(v, k) in form.commu" :key="k">
							<!-- 重开信息 -->
							<div class="reopen-info" v-if="v.op_type === 13">
								<span>{{ $t("text_title_info") }}<span class="title-time">{{ v.created_at }}</span></span>
							</div>
							<div class="chat-item" v-if="v.role === 1">
								<div class="text-form-box remark-textarea chat-box-player" v-html="v.content"></div>
								<div class="avatar chat-player"></div>
								<div class="media-form-box">
									<!-- 图片补充 -->
									<div v-if="v.picture" class="form-labels">
										<div class="icon"></div>
										<span>{{ $t("text_img_add") }}</span>
									</div>
									<div v-if="v.picture" class="img-wrap">
										<img
											v-for="(item, index) in v.picture.split(',')"
											:key="index"
											:src="item"
											@click="previewPlayerImg(item)"
										/>
									</div>
									<!-- 视频补充 -->
									<div v-if="v.video" class="form-labels">
										<div class="icon"></div>
										<span>{{ $t("text_video_add") }}</span>
									</div>
									<div v-if="v.video" class="video-preview">
										<span v-for="(item, index) in v.video.split(',')"
											:key="index"
											:src="item">
												<fp-video
												:src="item"
												></fp-video>
										</span>
									</div>
								</div>
							</div>
							<div class="chat-item" v-if="v.role === 2">
								<div class="avatar chat-customer"></div>
								<div class="text-form-box remark-textarea chat-box-customer" v-html="v.content"></div>
							</div>
						</div>
					</van-collapse-item>
					<!-- 客服回复展示区 -->
					<div
						v-for="(reply, index) in form.replenish"
						:key="index"
						class="reply-wrapper ticketdetail-reply-wrapper"
					>
						<van-collapse-item :name="`${index+1}`" :is-link="false" :border="false">
							<template #title>
								<div class="title" @click="imgRotate(index+1)">
									<span class="nav-select-img" :class="[imgState[index+1]?'open-rotate':'close-rotate']"></span>
									<span>{{ $t("text_reply") }}<span class="title-time">{{ reply.created_at }}</span></span>
								</div>
							</template>
							<div class="form-content">
								<div class="text-form-box remark-textarea" v-html="reply.remark"></div>
							</div>
							<!-- 玩家补填过的信息展示 -->
							<template v-if="reply.op === 7 && reply.fill_content">
								<div class="title reply">
									<img src="~@/assets/img/ft.png">
									<span>{{ $t("text_add_proof") }}</span>
								</div>
								<template v-if="reply.fill_content">
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_txt_add") }}</span>
									</div>
									<div class="form-content">
										<div class="text-form-box">
											{{ reply.fill_content }}
										</div>
									</div>
								</template>
								<template
									v-if="
										reply.files &&
										JSON.parse(reply.files) &&
										JSON.parse(reply.files).find(item => item.file_type === 'image')
									"
								>
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_img_add") }}</span>
									</div>
									<div class="form-content">
										<div class="media-form-box">
											<div class="img-wrap">
												<img
													v-for="(img, index) in getImg(JSON.parse(reply.files))"
													:key="index"
													:src="img.url"
													alt=""
													@click="previewImg(img)"
												/>
											</div>
										</div>
									</div>
								</template>
								<template
									v-if="
										reply.files &&
										JSON.parse(reply.files) &&
										JSON.parse(reply.files).find(item => item.file_type === 'video')
									"
								>
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_video_add") }}</span>
									</div>
									<div class="form-content">
										<div class="media-form-box">
											<div class="video-preview">
												<fp-video
													:src="getVideo(JSON.parse(reply.files))"
												></fp-video>
											</div>
										</div>
									</div>
								</template>
							</template>
						</van-collapse-item>
					</div>
					<!-- 重开信息展示区 -->
					<div
						v-for="(reply, index) in form.reopen"
						:key="index"
						class="reply-wrapper"
					>
						<van-collapse-item :name="`${form.replenish!.length+1+index}`" :is-link="false" :border="false">
							<template #title>
								<div class="title" @click="imgRotate(form.replenish!.length+1+index)">
									<span class="nav-select-img" :class="[imgState[form.replenish!.length+1+index]?'open-rotate':'close-rotate']"></span>
									<span>{{ $t("text_title_info") }}<span class="title-time">{{ reply.created_at }}</span></span>
								</div>
							</template>
							<template v-if="reply.fill_content">
								<template v-if="reply.fill_content">
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_txt_add") }}</span>
									</div>
									<div class="form-content">
										<div class="text-form-box">
											{{ reply.fill_content }}
										</div>
									</div>
								</template>
								<template
									v-if="
										reply.files &&
										JSON.parse(reply.files) &&
										JSON.parse(reply.files).find(item => item.file_type === 'image')
									"
								>
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_img_add") }}</span>
									</div>
									<div class="form-content">
										<div class="media-form-box">
											<div class="img-wrap">
												<img
													v-for="(img, index) in getImg(JSON.parse(reply.files))"
													:key="index"
													:src="img.url"
													alt=""
													@click="previewImg(img)"
												/>
											</div>
										</div>
									</div>
								</template>
								<template
									v-if="
										reply.files &&
										JSON.parse(reply.files) &&
										JSON.parse(reply.files).find(item => item.file_type === 'video')
									"
								>
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ $t("text_video_add") }}</span>
									</div>
									<div class="form-content">
										<div class="media-form-box">
											<div class="video-preview">
												<fp-video
													:src="getVideo(JSON.parse(reply.files))"
												></fp-video>
											</div>
										</div>
									</div>
								</template>
							</template>
							<!-- 重开处理结果 回复区；重开回复中有：客服回复或者打回补填的表单展示 -->
							<div
								v-for="(item, index) in reply.replenish"
								:key="index"
								class="reply-wrapper"
							>
								<div class="title">
									<span>{{ $t("text_reopen_result") }}<span class="title-time">{{ item.created_at }}</span></span>
								</div>
								<div class="form-content">
									<div class="text-form-box remark-textarea" v-html="item.remark"></div>
								</div>
								<!-- 玩家补填过的信息展示 -->
								<template v-if="item.op === 7 && item.fill_content">
									<div class="title reply">
										<img src="~@/assets/img/ft.png">
										<span>{{ $t("text_add_proof") }}</span>
									</div>
									<template v-if="item.fill_content">
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_txt_add") }}</span>
										</div>
										<div class="form-content">
											<div class="text-form-box">
												{{ item.fill_content }}
											</div>
										</div>
									</template>
									<template
										v-if="
											item.files &&
											JSON.parse(item.files) &&
											JSON.parse(item.files).find(item => item.file_type === 'image')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_img_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="img-wrap">
													<img
														v-for="(img, index) in getImg(JSON.parse(item.files))"
														:key="index"
														:src="img.url"
														alt=""
														@click="previewImg(img)"
													/>
												</div>
											</div>
										</div>
									</template>
									<template
										v-if="
											item.files &&
											JSON.parse(item.files) &&
											JSON.parse(item.files).find(item => item.file_type === 'video')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_video_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="video-preview">
													<fp-video
														:src="getVideo(JSON.parse(item.files))"
													></fp-video>
												</div>
											</div>
										</div>
									</template>
								</template>
							</div>
						</van-collapse-item>
					</div>
					<!-- 重开信息填写区 -->
					<van-collapse-item name="reopen" :is-link="false" :border="false" v-if="reopenInfo">
						<template #title>
							<div class="title" @click="reopenImg = !reopenImg">
								<span class="nav-select-img" :class="[reopenImg?'open-rotate':'close-rotate']"></span>
								<span>{{ $t("text_title_info") }}</span>
							</div>
						</template>
						<reopen-ticket :catIdFromParent="(form.cat_id as number)" @show-success="showSuccess"></reopen-ticket>
					</van-collapse-item>
				</van-collapse>
				<!-- 底部提示区 -->
				<!-- 显示是否解决tips：appraise:后台设置可以评价+ relate_cat:客服回复处理完成+ reopen_disabled:超过重开次数+不支持重开：true+ hideSelf点击未解决按钮后隐藏自己
					否则直接显示下面的提交评价按钮 -->
				<!-- 工单v4.3.1：除了超时关闭单，其它都显示是否解决tip -->
				<!-- 改：soc新工单，超时关闭也显示是否解决 只能引导去评价 不能重开 -->
        <!-- 超时关闭提醒 -->
        <div v-if="form.overtime_reply" class="close-tips">{{ form.overtime_reply }}, {{ $t('text_auto_close_tip') }}: {{ form.overtime }}</div>
				<div v-if="overtimeClose" class="close-tips">{{ $t("text_time_close") }}</div>
				<div v-if="(form.appraise && !form.relate_cat && !hideSelf) || (overtimeClose && form.appraise && !hideSelf)" class="tips resolve-box">
					<div class="mini-button-left-text">{{ $t('text_problem_resolve') }}</div>
					<div class="mini-button-box">
						<div class="mini-button-yes" @mousedown.stop.prevent="goToAppraise(1)" @touchstart.stop.prevent="goToAppraise(1)"><auto-font-size :text="$t('text_button_resolved')"></auto-font-size></div>
						<div class="mini-button-no" @mousedown.stop.prevent="goToNext" @touchstart.stop.prevent="goToNext"><auto-font-size :text="$t('text_button_unresolved')"></auto-font-size></div>
					</div>
				</div>
				<div v-if="unResolved" class="tips resolve-box">
					<div class="mini-button-left-text">{{ $t('text_sorry_help') }}</div>
					<div class="mini-button-box">
						<div class="mini-button-no" @mousedown.stop.prevent="goToAppraise(2)" @touchstart.stop.prevent="goToAppraise(2)"><auto-font-size :text="$t('btn_none')"></auto-font-size></div>
						<div class="mini-button-yes" @mousedown.stop.prevent="goToReopen" @touchstart.stop.prevent="goToReopen"><auto-font-size :text="$t('btn_conversation')"></auto-font-size></div>
					</div>
				</div>
				<!-- reopen_disabled: 超过重开次数或者不支持重开：true：展示去评价，否则展示重开 -->
				<div v-if="form.appraise && !form.relate_cat && form.reopen_disabled && toEvaluate" @click="spanToAppraise" class="tips historyRecordsFont" v-html="$t('not_resolved_appraise')"></div>
				<div v-if="showSuccessTips && !submit" @click="goToHistory" class="tips historyRecordsFont" v-html="$t('text_tips_success')"></div>

				<div v-if="submit" class="tips">
					<div class="historyRecordsFont" @click="goToHistory" v-html="$t('text_receive_question')"></div>
				</div>
				<div v-if="accept" class="tips">{{ $t("text_resolve_wait") }}</div>
				<div v-if="secondLine" class="tips">{{ $t("text_upgrade_pro") }}</div>
				<div v-if="checked" class="tips">{{ $t("text_upgrade_highpro") }}</div>
				<div v-if="completed" class="tips">
					<span style="margin-right: 5px;">{{ $t("text_fill_close") }}</span>
					<span v-if="count > 0" style="color: #f7bf28;">{{ ' [' + count + 's]' }}</span>
				</div>
				<div v-if="form.evidence" class="evidence-tips" v-html="$t('text_complete_close') + '<span class=time>' + getCompleteTime() + '</span>'"></div>
				<div v-if="form.evidence" class="button-wrapper">
					<div class="submit-btn complete-btn" @mousedown.stop.prevent="goToComplete" @touchstart.stop.prevent="goToComplete"><auto-font-size :text="$t('text_fill_data')"></auto-font-size></div>
				</div>
				<!-- 显示提交评价按钮：appraise:后台设置可以评价+relate_cat:客服回复处理完成+reopen_disabled:超过重开次数+不支持重开：true
					否则显示上面的是否解决tips -->
				<!-- <div v-if="form.appraise && !form.relate_cat && form.reopen_disabled" class="button-wrapper">
					<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
						<div class="fp-button" @mousedown.stop.prevent="goToAppraise(0)" @touchstart.stop.prevent="goToAppraise(0)" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("btn_appraise") }}</div>
					</div>
				</div> -->
				<div v-if="appraised" class="tips">
					<span style="margin-right: 5px;">{{ $t("text_receive_rate") }}</span>
					<span v-if="count > 0" style="color: #f7bf28;">{{ ' [' + count + 's]' }}</span>
				</div>
				<div v-if="form.relate_cat" class="button-wrapper">
					<div class="submit-btn question-btn" @mousedown.stop.prevent="goToQuestion" @touchstart.stop.prevent="goToQuestion"><auto-font-size :text="$t('btn_push_after_completion')"></auto-font-size></div>
				</div>
				<!-- 底部提示，点击回首页 -->
				<div @click="goToHome" v-if="isShowTips" class="tips historyRecordsFont" v-html="$t('text_goto_home')"></div>
				<!-- 聊天框隐藏条件：单子处理完成-done为true：回复&关单 or 拒单 or 超时关闭 -->
				<!-- 临时 兼容新老工单：ticket_sys_type -->
				<div v-if="!form.done && form.category && form.ticket_sys_type" class="textarea_box">
					<van-field
						v-model="com_content"
						type="textarea"
						rows="2"
						autosize
						maxlength="200"
						show-word-limit
						:placeholder="$t('text_supplement')"
					/>
					<div class="form-labels">
						<div class="icon"></div>
						<span>{{ $t("text_img_add") }}</span>
					</div>
					<img-upload
						:isH5Upload="isPC || isPrivZoneH5"
						class="form-item"
						@success="uploadImg"
						@remove="removeImg"
					></img-upload>
					<div class="form-labels">
						<div class="icon"></div>
						<span>{{ $t("text_video_add") }}</span>
					</div>
					<video-upload
						:isH5Upload="isPC || isPrivZoneH5"
						class="form-item"
						@success="uploadVideo"
						@remove="removeVideo"
					></video-upload>
					<div class="button-wrapper">
						<div class="submit-btn chat-btn" @mousedown.stop.prevent="handleChatInfo" @touchstart.stop.prevent="handleChatInfo">{{ $t("text_submit") }}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script lang="ts">
import { defineComponent, nextTick, ref, reactive, onMounted, toRefs, computed, getCurrentInstance } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from "vue-router"
import { ImagePreview } from 'vant'
import { useI18n } from 'vue-i18n'
import ReopenTicket from './components/AnswerComp/ReopenTicket.vue'
import { Toast } from 'vant'
import { ticketDetail, appraiseFeedback, playerSendMsg } from '@/api/tickets'
import { formatDate } from '@/utils'
import { browser } from '@/utils'
import { useDebounceFn } from '@vueuse/core'

interface dataT {
	imgList: Array<unknown>,
	videoList: Array<unknown>,
	pushAnimate: boolean,
	ticketId: number,
	from?: unknown,
	form: Record<string, unknown> & {
		filed?: string | Record<string, unknown>,
		replenish?: Array<Record<string, unknown> & {
			files: string,
			created_at: string
		}>,
		reopen?: Array<Record<string, unknown> & {
			files: string,
			created_at: string,
			replenish?: Array<Record<string, unknown> & {
				files: string,
				created_at: string
			}>
		}>,
		commu?: Array<Record<string, unknown> & {
			content: string,
			created_at: string,
			role: number
		}>,
	},
	timer?: unknown,
	count: number,
	loading: boolean
}
export default defineComponent({
	name: 'TicketDetail',
	components: { ReopenTicket }
})
</script>
<script setup lang="ts">
// eslint-disable-next-line
const { appContext } = getCurrentInstance() as any
const { state, commit } = useStore()
const userInfo = computed(() => state.userInfo)
const setLog = appContext.config.globalProperties.$utils.basicLog
const route = useRoute()
const router = useRouter()
const { t: $t } = useI18n()
const com_content = ref<string>('')
const browserIsPC = browser.version.isWindows || false
console.log('New TicketDetail browserIsPC', browserIsPC)
const data: dataT = reactive({
	imgList: [],
	videoList: [],
	ticketId: -1,
	form: {},
	count: -1,
	pushAnimate: false,
	loading: true
})
commit('setLoadingCount', 1)
// 出现submit(反馈已收到，您可点击我的历史服务记录，查看处理进度)条件：
// 首次成功提交表单，无客服回复，data.form.done(处理完成)为false，data.form.transfer为1
const submit = computed(() => {
	return (
		((data.form.replenish && (data.form.replenish).length === 0) ||
		data.form.replenish === null)&&
		!data.form.done &&
		data.form.transfer === 1
	)
})
// 出现accept(您提交的问题已经介入处理中啦，可能需要一定的处理时间，请耐心等候~)条件：
// 成功提交表单，无客服回复，data.form.done为false，data.form.transfer为2
// 新工单1.0新增：只要客服回复了，就不展示accept
const accept = computed(() => {
	return (
		data.form.replenish &&
		data.form.replenish.length === 0 &&
		!data.form.done &&
		data.form.transfer === 2 &&
		!data.form.commu?.some(obj => obj.role === 2)
	)
})
// 出现completed(补填完成后)条件：
// 客服回复过，客服回复的最后一条中的op为7，客服回复的最后一条中的fill_content不为空，data.form.done为false
const completed = computed(() => {
	return (
		data.form.replenish &&
		data.form.replenish.length !== 0 &&
		data.form.replenish[data.form.replenish.length - 1].op === 7 &&
		data.form.replenish[data.form.replenish.length - 1].fill_content &&
		!data.form.done
	)
})
// 出现appraised(已收到评价)条件：
// 客服处理完成，该工单未评价过，data.form.closed为1，data.form.csi不为0
const appraised = computed(() => {
	return data.form.done && !data.form.appraise && data.form.closed === 1 && data.form.csi !== 0
})
// 出现secondLine(问题过于复杂，升级为专家处理)条件：
// 客服未处理完成，未补填，未评价，data.form.transfer为3
const secondLine = computed(() => {
	return (
		!data.form.done &&
		!data.form.evidence &&
		!data.form.appraise &&
		data.form.transfer === 3 &&
		!completed.value &&
		!appraised.value
	)
})
// 出现checked(问题过于复杂，升级为高级专家处理)条件：
// 客服未处理完成，未补填，未评价，data.form.transfer为4
const checked = computed(() => {
	return (
		!data.form.done &&
		!data.form.evidence &&
		!data.form.appraise &&
		data.form.transfer === 4 &&
		!completed.value &&
		!appraised.value
	)
})
// 出现overtimeClose(未及时补充 工单关闭)条件：
// 客服处理完成，data.form.closed为3
const overtimeClose = computed(() => {
	return data.form.done && data.form.closed === 3
})

// 处理结果页面加载打点
setLog({
	button: 0,
	action: 'loading',
	result: 1,
	position: 'wo_result'
})

onMounted(() => {
	data.from = route.query.from
	if (route.query.ticketId) {
		data.ticketId = +route.query.ticketId
		getTicketDetail()
	} else {
		console.log('缺少重要参数')
	}
	// // H5兼容ios12 监听所有input blur
	// const inputsElement = [...document.getElementsByTagName('input')]
	// inputsElement.forEach(element => {
	// 	if (element) {
	// 		element.onblur = () => {
	// 			setTimeout(() => {
	// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
	// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
	// 			}, 300)
	// 		}
	// 	}
	// })
})
// 关闭webview
// const closeSDK = () => {
// 	appContext.config.globalProperties.$utils.jsBridge('close')
// }
const getTicketDetail = () => {
	ticketDetail({
		ticket_id: data.ticketId
	}).then((res: dataT["form"]) => {
		data.loading = false
		data.form = res
		data.form.filed = JSON.parse(data.form.filed as string)
		data.form.replenish?.forEach(() => imgState.push(false))
		// 判断是否展开对话信息；有对话信息则展开 + 展开除了对话信息外的最后一项
		if (data.form.commu?.length) {
			activeNames.value.push('chatInfo')
			chatImg.value = true
		}
		// 计算最后一项的:name值，进入页面只展开最后一项
		const flag: number = (data.form.replenish ? (data.form.replenish as []).length : 0) + (data.form.reopen ? (data.form.reopen as []).length : 0)
		const flagStr: string = flag.toString()
		activeNames.value.push(flagStr)
		imgState[flag] = true

		// 数据加载完成后，滚动到最后一个回复或聊天记录
		nextTick(() => {
			const scrollToTarget = () => {
				const contentBodyBox = document.querySelector('.content-body .content') as HTMLElement
				if (!contentBodyBox) return
				// 查找聊天信息区域
				const chatInfoContainer = document.querySelector('.ticketdetail-chat-info') as HTMLElement

				// 计算内容区域的可视高度
				const contentVisibleHeight = contentBodyBox.clientHeight
				let scrollPosition = 0

				if (chatInfoContainer && data.form.commu?.length) {
					// 获取聊天区域的顶部位置
					const chatInfoTop = chatInfoContainer.offsetTop
					// 获取聊天区域的高度
					const chatInfoHeight = chatInfoContainer.offsetHeight

					console.log('聊天区域 - chatInfoTop:', chatInfoTop)
					console.log('聊天区域 - contentVisibleHeight:', contentVisibleHeight)
					console.log('聊天区域 - chatInfoHeight:', chatInfoHeight)

					// 计算滚动位置：将聊天区域放在可视区域底部
					scrollPosition = chatInfoTop - contentVisibleHeight + chatInfoHeight
				}

				console.log('最终 scrollPosition:', scrollPosition)

				// 滚动到计算出的位置
				contentBodyBox.scrollTo({
					top: Math.max(0, scrollPosition),
					behavior: 'smooth'
				})
			}

			// 立即尝试滚动
			scrollToTarget()
		})
	}, (res: string) => {
		data.loading = false
		Toast(res)
	}).catch((err: string) => {
		data.loading = false
		Toast(err)
	}).finally(() => {
    commit('setLoadingCount', 0)
  })
	if (data.from === 'complete' || data.from === 'appraise')  {
		const TIME_COUNT = 3
		if (!data.timer) {
			data.count = TIME_COUNT
			data.timer = setInterval(() => {
				if (data.count > 0 && data.count <= TIME_COUNT) {
					data.count--
				} else {
					clearInterval(data.timer as number)
					data.timer = null
					// 关闭H5(WX小游戏不关闭)
					// if (userInfo.value.openid === undefined || userInfo.value.openid === null) {
					// 	if (userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai')) {
					// 		// 兼容AI SDK 关闭webview逻辑
					// 		window.location.href = 'fpcapi://openai?method=redirect&scene=1&target=close'
					// 	} else {
					// 		closeSDK()
					// 	}
					// }
					router.push('/newcs')
				}
			}, 1000)
		}
	}
}
const isPC = JSON.parse(sessionStorage.getItem('isPC') ?? 'false')

// 对私域上传做的特判处理：
// 私域 h5上传
const isPrivZone = computed(() => userInfo.value?.zone_from === 'privZone')
const isInWebview = computed(() => state.globalEnvConfig.isInWebview)
// 私域 且 不是端内webview 则 使用h5上传；如果是私域 且 是端内webview 则 使用SDK桥接上传
const isPrivZoneH5 = computed(() => isPrivZone.value && !isInWebview.value)

const previewImg = (img: Record<string, string>) => {
	ImagePreview({
		images: [img.url],
		closeable: isPC,
		showIndex: false,
		teleport: 'body',
	})
}
const previewPlayerImg = (img: Record<string, string>) => {
	ImagePreview({
		images: [img],
		closeable: isPC,
		showIndex: false,
		teleport: 'body',
	})
}
const getImg = (fileList: Array<Record<string, string>>) => {
	const imgList = fileList.filter(file => file.file_type === 'image')
	return imgList
}
const getVideo = (fileList: Array<Record<string, unknown>>) => {
	const video = fileList.find(file => file.file_type === 'video')
	return video?.url
}
// 上传图片
const uploadImg = (path: Array<string>) => {
	data.imgList.push(...path)
}
// 删除图片
const removeImg = (index: number) => {
	data.imgList.splice(index, 1)
}
// 上传视频
const uploadVideo = (path: Array<string>) => {
	data.videoList.push(...path)
}
// 删除视频
const removeVideo = () => {
	data.videoList.splice(0, 1)
}
const goToComplete = () => {
	// 跳转补填按钮打点
	setLog({
		button: 1,
		action: 'click',
		result: 1,
		position: 'need_fullfill'
	})
	data.pushAnimate = true
	router.push({
		path: '/newcs/ticketComplete',
		query: {
			ticketId: data.ticketId
		}
	})
}
const goToAppraise = (val: number) => {
	if (val) {
		// 点击 已解决、算了 按钮打点
		setLog({
			position: val === 1 ? 'button_resolved' : 'button_drop',
			ticket_id: data.ticketId, // 关联的工单id
			click_time: new Date().getTime(), // 什么时间
			action: 'click', // 点击
			cat_id: data.form.cat_id // 问题分类id
		})
		if (val === 1) {
			// 后端接口统计已解决 未解决点击，报表用
			appraiseFeedback({
				ticket_id: data.ticketId,
				resolved: 1 // 是否解决 1：已解决 2：未解决
			}).catch((err: string) => { Toast(err) })
		}
	} else {
		// 点击评价按钮打点(历史逻辑：这里通用版有 定制版没有打点)
		setLog({
			button: 1,
			action: 'click',
			result: 1,
			position: 'wo_result'
		})
		data.pushAnimate = true
	}

	// 临时 兼容新老工单
	router.push({
		path: data.form.ticket_sys_type ? '/newcs/ticketAppraise' : '/newcs/oldticketAppraise',
		query: {
			ticketId: data.ticketId
		}
	})
}
const chatImg = ref<boolean>(false)
const reopenImg = ref<boolean>(false)
const showSuccessTips = ref<boolean>(false)
const showSuccess = () => {
	getTicketDetail()
	nextTick(() => {
		showSuccessTips.value = true
		reopenInfo.value = false
	})
}
const activeNames = ref<string[]>([])
let imgState = reactive([false])
const imgRotate = (i: number) => {
	imgState[i] = !imgState[i]
}
// 未解决按钮
let unResolved = ref<boolean>(false)
let toEvaluate = ref<boolean>(false) // 展示抱歉未解决，去评价tip
let hideSelf = ref<boolean>(false)
const goToNext = () => {
	// 点击未解决按钮打点
	setLog({
		position: 'button_unresolved',
		ticket_id: data.ticketId,
		click_time: new Date().getTime(),
		action: 'click',
		cat_id: data.form.cat_id
	})
	appraiseFeedback({
		ticket_id: data.ticketId,
		resolved: 2 // 是否解决 1：已解决 2：未解决
	}).catch((err: string) => { Toast(err) })
	// reopen_disabled: 超过重开次数或者不支持重开：true：展示去评价，否则展示重开
	data.form.reopen_disabled ? toEvaluate.value = true : unResolved.value = true
	hideSelf.value = true
}
// 重开按钮
const reopenInfo = ref<boolean>(false)
const goToReopen = () => {
	// 点击重开按钮打点
	setLog({
		position: 'button_reopen',
		ticket_id: data.ticketId, // 关联的工单id
		click_time: new Date().getTime(), // 什么时间
		action: 'click', // 点击
		cat_id: data.form.cat_id // 问题分类id
	})
	unResolved.value = false
	reopenInfo.value = true
	activeNames.value = ['reopen'] // 所有模块都收起，只展开重开填写模块
	reopenImg.value = true
  imgState.length = 0
}
const getCompleteTime = () => {
	if (data.form.evidence && data.form.replenish) {
		const length = data.form.replenish.length
		const replyTime = data.form.replenish[length - 1].created_at
		const endTime = new Date(replyTime.replace(/-/g, '/')).getTime() + 24 * 60 * 60 * 1000
		return formatDate(endTime)
	} else {
		return ''
	}
}
// 重新提交工单跳转工单填写页
const goToQuestion = () => {
	data.pushAnimate = true
	router.push({
		path: '/newcs/answersDetail',
		query: {
			cat_id: data.form.relate_cat as number,
			from_ticket_id: data.form.ticket_id as number
		}
	})
}
const goToHistory = (e: Event) => {
	if((e.target as HTMLElement).localName === 'span') {
		router.push({
			path: '/newcs/newHistory'
		})
	}
}
const goToHome = (e: Event) => {
	if ((e.target as HTMLElement).localName === 'span') {
		router.push({
			path: '/newcs'
		})
	}
}
const spanToAppraise = (e: Event) => {
	if((e.target as HTMLElement).localName === 'span') {
		// 点击去评价按钮打点
		setLog({
			position: 'button_goto_appraise',
			ticket_id: data.ticketId, // 关联的工单id
			click_time: new Date().getTime(), // 什么时间
			action: 'click', // 点击
			cat_id: data.form.cat_id // 问题分类id
		})
		router.push({
			path: data.form.ticket_sys_type ? '/newcs/ticketAppraise' : '/newcs/oldticketAppraise',
			query: {
				ticketId: data.ticketId
			}
		})
	}
}
// 和客服对话
const isShowTips = ref<boolean>(false)
const handleChatInfo = () => {
	console.log('handleChatInfo submit com_content.value', com_content.value)
	if (!com_content.value) {
    Toast($t('text_cant_empty'))
    return
  }
	const picArr = data.imgList.map(item => item.url).join(',')
	const videoArr = data.videoList.map(item => item.url).join(',')
	commit('setLoadingCount', 1)
	playerSendMsg({
		ticket_id: data.ticketId,
		content: com_content.value,
		picture: picArr,
		video: videoArr
	}).then(() => {
		getTicketDetail()
		com_content.value = ''
		data.imgList.length = 0
		data.videoList.length = 0
		location.reload()
		isShowTips.value = true
	}).catch((err: string) => {
		Toast(err)
	}).finally(() => commit('setLoadingCount', 0))
}

const { form, count } = toRefs(data)
</script>

<style lang="scss" scoped>
// 修改vant Collapse折叠面板的默认样式
::v-deep .van-collapse-item .van-collapse-item__wrapper {
	overflow: inherit;
}
::v-deep .van-collapse-item__title,
::v-deep .van-collapse-item__content {
	all: inherit;
}
// 图标的展开关闭旋转
.close-rotate {
	transition: all 0.5s;
}
.open-rotate {
	transform: rotate(-180deg);
	transition: all 0.5s;
}
.nav-select-img {
	display: block;
	background-image: url('~@/assets/img/collapse.png');
	background-size: 100% 100%;
	height: 24px;
	width: 24px;
	float: left;
	margin: 17px 8px;
}

.content {
	word-wrap: break-word;
	word-break: break-all;
	overflow: auto;
	font-size: 24px;
	margin-top: 20px;
	color: #9ABBCD;
	.form-content {
		padding-bottom: 20px;
		overflow: auto;
	}
	.form-labels {
		margin: 10px 0;
	}
	.form-label {
		margin-left: 2%;
		margin-bottom: 10px;
	}
	.text-form-box {
		background: rgba(5,5,6,0.26);
		border: 1px solid #445960;
		padding: 10px 20px;
		width: 97.7%;
		margin-left: 2%;
		box-sizing: border-box;
		font-size: 24px;
		word-break: break-word;
	}
	.remark-textarea {
		white-space: pre-wrap;
		& ::v-deep img {
			width: 100%;
			height: auto;
		}
	}
	.media-form-box {
		width: 97.7%;
		margin-left: 2%;
	}
	.title {
		margin: 0px 0px 15px 0px;
		box-sizing: border-box;
		width: 100%;
		padding: 5px 5px 5px 8px;
		height: auto;
		overflow: auto;
		line-height: 58px;
    font-family: Source Han Serif CN;
		font-weight: 400;
		font-size: 24px;
		color: #D4AD5B;
		text-shadow: 0px 1px 0px rgba(0,0,0,0.6);
		background: rgba(5, 5, 6, 0.26);

		// img {
		// 	display: block;
		// 	height: 24px;
		// 	width: 24px;
		// 	float: left;
		// 	margin: 17px 8px;
		// }
		.title-time {
			font-size: 22px;
			opacity: 0.6;
			margin-left: 16px;
		}
	}
	.evidence-tips {
		width: 80%;
		text-align: left;
		margin: 20px auto;
	}
	.close-tips {
		width: 80%;
		text-align: center;
		margin: 50px auto;
	}
	.tips {
		width: 80%;
		margin: 50px auto;
		text-align: center;
		bottom: 60px;
		word-break: normal;
	}
}
.img-wrap {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	img {
		width: 250px;
		height: 250px;
		object-fit: cover;
		display: block;
		padding: 10px;
	}
}
.video-preview {
	width: 40vw;
	// height: 40vh;
	position: relative;
	.video {
		width: 100%;
		height: 100%;
		display: block;
	}
}
.textarea_box {
	::v-deep(.van-cell) {
		background: rgba(5, 5, 6, 0.26);
		border: 1px solid #182033;
		padding: 10px 20px;
	}
	::v-deep(.van-cell::after) {
		border-bottom: none;
	}
	::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}
}
// 对话信息
.chat-item {
	position: relative;
	margin: 25px 0;
	.avatar {
		position: absolute;
		top: 0;
		width: 60px;
		height: 60px;
		border-radius: 50%;
	}
	.chat-customer {
		background-image: url('~@/assets/img/soc/icon-gpt.png');
		background-size: cover;
	}
	.chat-player {
		right: 0;
		background-image: url('~@/assets/img/soc/icon-avatar.png');
		background-size: cover;
	}
	.chat-box-customer {
		min-height: 60px;
		width: 89%;
		margin-left: auto;
		::v-deep img {
			max-width: 30%;
		}
	}
	.chat-box-player {
		min-height: 60px;
		width: 87%;
		margin-right: auto;
	}
	& ::v-deep a {
    text-decoration: underline;
    color: #4e6ef2;
  }
}
// 修改上传图片、视频组件样式
::v-deep .img-wrap .img-upload,
::v-deep .video-wrap .video-upload,
::v-deep .pc-box {
	width: 150px;
	height: 150px;
	border: 1px solid #445960;
	background: rgba(5, 5, 6, 0.26) url('~@/assets/img/soc/plus.webp') no-repeat center;
	background-size: 70px;
}

@media all and (orientation : portrait) {
	.img-wrap {
		::v-deep img {
			width: 150px;
			height: 150px;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			top: 0;
			right: 0;
		}
	}
	.video-wrap {
		::v-deep .video-preview {
			// width: 65vw;
			// height: 18vh;
		}
		::v-deep .delete {
			width: 50px;
			height: 50px;
			right: -50px;
		}
	}
}

.reopen-info {
	color: #D4AD5B;
	margin-left: 2%;
	> span {
		display: flex;
		align-items: center;
		gap: 10px;
	}
}
</style>
<style lang="scss">
.historyRecordsFont {
	margin-right: 5px;
	span {
		color: #ffffff;
		cursor: pointer;
		text-decoration: underline;
		text-underline-offset: 0.03rem;
	}
}
</style>
