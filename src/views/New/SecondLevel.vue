<template>
  <div class="answers-detail">
    <div class="main-role"></div>
    <div class="article-bg second-level">
      <div class="main-title">{{ title }}</div>
      <!-- 卡片 -->
      <div v-if="!!secondCardsList[0]?.image_url" class="cards-wapper">
        <dl class="sec-card" v-for="(v, k) in secondCardsList" :key="k" @click="goAnswersDetail(v, k)">
          <dt><img :src="v.image_url" /></dt>
          <div class="sec-line"></div>
          <dd><auto-font-size :text="v.image_title"></auto-font-size></dd>
        </dl>
      </div>
      <!-- 列表 -->
      <div v-else class="list-wapper">
        <div class="list-item" v-for="(v, k) in secondCardsList" :key="k" @click="goAnswersDetail(v, k)">
          <img src="~@/assets/img/point.png"><span>{{ v.image_title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { secondCards } from '@/api/new'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'SecondLevel'
})

</script>
<script setup lang="ts">
import type { TNewCard } from '@/enum/types'
import { Toast } from 'vant'
const { commit } = useStore()
const route = useRoute()
const router = useRouter()
const { temp_id, card_idx, title } = route.query
const secondCardsList = ref([])
commit('setLoadingCount', 1)
secondCards({
  temp_id: +(temp_id as string),
  card_idx: card_idx
}).then((res: []) => {
  secondCardsList.value = res
}).catch((err: string) => {
  Toast(err)
}).finally(() => {
  commit('setLoadingCount', -1)
})

const goAnswersDetail = (v: TNewCard, index: number) => {
  // card_group 1：关联子卡片-图片 2：关联知识 3：关联表单分类 4：关联子卡片-列表；这里只有(后台只能选择)2 3
  if (v.card_group === 2) {
    router.push({
      path: '/newcs/articleDetail',
      query: {
        art_id: v.art_id,
        card_group: v.card_group
      }
    })
  } else if (v.card_group === 3) {
    router.push({
      path: '/newcs/answersDetail',
      query: {
        cat_id: v.ticket_cat_id
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.second-level {
  height: 652px;
  width: 907px;
  margin-bottom: 21px;
  margin-left: -40px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 25px 50px;
  display: flex;
  flex-direction: column;
  .main-title {
    font-family: Source Han Serif CN;
    font-weight: 400;
    font-size: 24px;
    color: #D4AD5B;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.6);
  }
  .cards-wapper {
    display: block;
    margin-top: 20px;
    flex: 1;
    overflow-y: auto;
  }
  .sec-card {
    width: 236px;
    height: 162px;
    display: inline-block;
    @include backgroundSec('soc/sec-card-bg.png');
    margin: 0px 0px 27.5px 49px;
    &:nth-child(3n+1) {
      margin-left: 0px;
    }
    dt {
      box-sizing: border-box;
      width: 100%;
      display: block;
      padding-top: 18px;
      img {
        width: 55px;
        height: 55px;
        display: block;
        margin: 0px auto 0;
      }
    }
    .sec-line {
      content: "";
      display: block;
      height: 2px;
      width: 152px;
      @include backgroundSec('soc/sec-card-line.png');
      margin: 18px auto 0;
    }
    dd {
      display: block;
      width: 90%;
      margin: 0 auto;
      box-sizing: border-box;
      padding: 0 10px;
      font-weight: 400;
      font-size: 20px;
      color: #E2C885;
      line-height: 59px;
    }
  }
  .list-wapper {
    margin-top: 20px;
    .list-item {
      display: flex;
      align-items: center;
      margin: 20px 0;
      font-size: 24px;
      color: #E2C885;
      img {
        width: 40px;
        height: 40px;
        margin: 0px 5px 0 0;
        align-self: flex-start;
      }
    }
  }
}

@media all and (orientation: portrait) {
  .second-level {
    height: calc(100% - env(safe-area-inset-top) - var(--head-bar-height) - 15px) !important;
    margin: 0;
    .cards-wapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-content: flex-start;
      gap: 20px;
      padding: 20px;
    }
    .sec-card {
      width: calc(50% - 10px);
      margin: 0;
    }
  }
}
</style>