<template>
  <div class="answers-detail">
    <div class="main-role"></div>
    <div class="content-body article-bg" id="t-container">
      <div class="content-detail" id="t-wrap">
        <div class="t-items">
          <div class="title">
            <img src="~@/assets/img/group.png">
            <span>{{ $t("text_selection_sort_sub", {q: parentLabel}) }}</span>
          </div>
          <div class="list">
            <div class="list-item" v-for="(v, k) in categoryList" :key="k" @click="handleCategoryClick(v)">
              <div style="float:left">
                <img src="~@/assets/img/point.png"><span>{{ v.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'ClassifyQuestionThird'
})
</script>

<script setup lang="ts">
import { reactive, toRefs, onMounted, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { getRelationCats } from '@/api/tickets'
import { Toast } from 'vant'

interface CategoryItem {
  id?: number,
  label?: string,
  level?: number,
  tpl_id?: number,
  children?: Array<CategoryItem>,
  relate_type?: number
}

interface StateType {
  categoryList: Array<CategoryItem>,
  parentLabel: string
}

const router = useRouter()
const route = useRoute()
const { t: $t } = useI18n()
const { commit } = useStore()
const state = reactive<StateType>({
  categoryList: [],
  parentLabel: route.query.parentLabel as string || ''
})

// 页面加载打点
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
setLog({
  button: 0,
  action: 'loading',
  result: 1,
  position: 'que_classify_third'
})

onMounted(() => {
  loadThirdCategoryData()
})

// 加载三级分类数据
const loadThirdCategoryData = () => {
  if (!route.query.parentId) {
    Toast('参数错误')
    return
  }

  commit('setLoadingCount', 1)

  const params = {
    fork_cat_ids: [Number(route.query.parentId)]
  }

  getRelationCats(params).then((res: Array<CategoryItem>) => {
    commit('setLoadingCount', 0)

    // 处理数据，寻找子分类
    if (res && res.length > 0 && res[0].children) {
      state.categoryList = res[0].children
    } else {
      state.categoryList = []
      Toast('没有子分类数据')
    }
  }, (err: string) => {
    commit('setLoadingCount', 0)
    Toast(err)
  }).catch((err: string) => {
    commit('setLoadingCount', 0)
    Toast(err)
  })
}

// 处理分类点击事件
const handleCategoryClick = (item: CategoryItem) => {
  // 分类点击打点
  setLog({
    button: item.id,
    action: 'click',
    result: 1,
    position: 'que_classify_third'
  })

  // 三级分类点击直接跳转到问题解答页
  router.push({
    path: '/newcs/answersDetail',
    query: {
      cat_id: item.id
    }
  })
}

const { categoryList, parentLabel } = toRefs(state)
</script>

<style lang="scss" scoped>
.title {
  box-sizing: border-box;
  width: 100%;
  padding: 10px;
  line-height: 30px;
  color: #cecfc9;
  font-size: 24px;
  border-radius: 2px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  img {
    display: block;
    height: 30px;
    width: 30px;
    float: left;
    opacity: 0.8;
    margin-right: 6px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 55px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.list-item {
  color: #E9C86F;
  overflow: auto;
  font-size: 22px;
  line-height: 35px;
  vertical-align: middle;
  margin: 20px auto;
  cursor: pointer;
  img {
    display: block;
    height: 35px;
    width: 35px;
    float: left;
    margin-right: 5px;
  }
}
</style>
