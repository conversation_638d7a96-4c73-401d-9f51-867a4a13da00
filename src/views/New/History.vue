<template>
  <div class="ticket-bg">
    <div class="ticket-wrapper">
      <div class="history-content" v-if="loadingCount < 1">
        <table class="tableList" cellspacing="0" v-if="historyList.length > 0">
          <tr class="row head">
            <th class="item">{{ $t("text_created_at") }}</th>
            <th class="item">{{ $t("text_ticket") }}</th>
            <th class="item">{{ $t("text_category") }}</th>
            <th class="item">{{ $t("text_progress") }}</th>
            <th class="item">{{ $t("text_detail") }}</th>
          </tr>
          <tr class="row" v-for="(v, k) in historyList" :key="k">
            <td class="item time">{{ v.created_at }}</td>
            <td class="item t_id">{{ v.ticket_id }}</td>
            <td class="item">{{ v.category }}</td>
            <td class="item">
              <div class="position_box">
                <span>
                  {{ progressEnum[v.progress] }}
                  <div class="red_point" v-if="v.read === 0"></div>
                </span>
              </div>
            </td>
            <td class="item">
              <div class="more" @click="showDetails(v.ticket_id)">
                {{ $t("btn_view") }}
              </div>
            </td>
          </tr>
        </table>
        <div v-else class="emptybox">
          <div>{{ $t("text_none_record") }}</div>
          <!-- <div class="a" @click="goauto">{{ $t("btn_submit_question") }}</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from "vue-router"
import { Toast } from 'vant'
import { getHistoryList } from '@/api/tickets'
import { useStore } from 'vuex'

interface dataT {
  historyList: Array<{
    ticket_id: number,
    created_at: string,
    category: string,
    progress: number,
    read: number
  }>
}
export default defineComponent({
  name: 'NewHistory'
})
</script>
<script setup lang="ts">
const { commit, state } = useStore()
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const loadingCount = computed(() => state.loadingCount)
commit('setLoadingCount', 1)
const router = useRouter()
const { t: $t } = useI18n()
const data: dataT = reactive({
  historyList: []
})
const progressEnum = computed((): {
  [key: number]: string
} => {
  return {
    1: $t('text_done'),
    2: $t('text_need_complete'),
    3: $t('text_in_progress'),
    4: $t('text_timeout'),
    5: $t('text_tips_processing'),
    6: $t('text_done')
  }
})

setLog({
  event: 'elfin_load_done',
  position: 'ticket_history',
  result: 'success'
})

getHistoryList({}).then((res: dataT['historyList']) => {
  // 历史工单加载成功打点
  data.historyList = res
}, (res: string) => {
  Toast(res)
}).catch((err: string) => {
  Toast(err)
}).finally(() => {
  commit('setLoadingCount', 0)
})
const showDetails = (id: number) => {
  router.push({ name: 'TicketDetail', query: { ticketId: id } })
}
const { historyList } = toRefs(data)
</script>

<style lang="scss" scoped>
.ticket-bg {
  height: 100%;
  width: 100%;
  position: relative;
}
.ticket-wrapper {
  width: 1296px;
  height: 652px;
  @include backgroundSec('soc/bg-home.png');
  position: absolute;
  bottom: 16px;
  left: 50%;
  margin-left: -648px;
  box-sizing: border-box;
  padding: 33px;
}
.history-content {
	width: 100%;
	height: 100%;
	overflow: auto;

	.tableList {
		width: 100%;
		table-layout: automatic;
    border-collapse: separate;
    border-spacing: 0px 10px;
		.row {
      background-color: rgba(5, 5, 6, 0.1);
			.item {
				vertical-align: middle;
				text-align: center;
				padding: 15px 5px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        font-size: 20px;
        color: #A9A9A9;
				.more {
					width: 140px;
					height: 44px;
					line-height: 42px;
					position: relative;
					@include backgroundSec('soc/history-tb-btn.png');
					font-family: Adobe Heiti Std;
          font-weight: bold;
          font-size: 20px;
          color: #FFFFFF;
					text-transform: uppercase;
					margin: 0 auto;
					box-sizing: border-box;
				}
        .position_box {
          span {
            display: inline-block;
            position: relative;
            .red_point {
              width: 10px;
              height: 10px;
              @include backgroundSec('soc/red_p.png');
              position: absolute;
              top: -2px;
              right: -10px;
            }
          }
        }
			}
			&.head {
        @include backgroundSec('soc/history-tb-head.png');
				.item {
					font-family: Adobe Heiti Std;
          font-weight: normal;
          font-size: 24px;
          color: #FFFFFF;
				}
			}
		}
	}
}
.emptybox {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 26px;
  .a {
    text-decoration: underline;
    color: #f7bf28;
    margin-top: 20px;
  }
}

@media all and (orientation: portrait) {
  .ticket-wrapper {
    height: calc(100% - env(safe-area-inset-top) - var(--head-bar-height) - 15px);
    width: 100%;
    left: 0;
    bottom: 0;
    margin: 0;
  }
}
</style>