<template>
	<div class="answers-detail">
		<div class="main-role"></div>
		<div class="content-body article-bg">
			<div class="content">
				<van-form :show-error="false" class="form-wrapper">
					<div class="appraise">
						<div class="text">
							<div class="required"><auto-font-size :text="$t('text_appraise')"></auto-font-size></div>
							<span v-if="showChildStar">
								<div class="required"><auto-font-size :text="$t('text_appraise_attitude')"></auto-font-size></div>
								<div class="required"><auto-font-size :text="$t('text_appraise_speed')"></auto-font-size></div>
								<div class="required"><auto-font-size :text="$t('text_appraise_treatment')"></auto-font-size></div>
							</span>
						</div>
						<div class="star-box">
							<div v-for="(star, index) in 5" :key="index" class="star" :class="{ light: star <= selectNum }" @click="selectStar(index)"></div>
							<span v-if="showChildStar">
								<div v-for="(star, index) in 5" :key="index+'2'" class="star" :class="{ light: star <= selectNum2 }"
									@click="selectStarChild(index, 2)"></div>
								<div v-for="(star, index) in 5" :key="index+'3'" class="star" :class="{ light: star <= selectNum3 }"
									@click="selectStarChild(index, 3)"></div>
								<div v-for="(star, index) in 5" :key="index+'4'" class="star" :class="{ light: star <= selectNum4 }"
									@click="selectStarChild(index, 4)"></div>
							</span>
						</div>
					</div>
					<!-- 意见输入 -->
					<van-field
						ref="textarea"
						class="form-item"
						v-model="content"
						type="textarea"
						rows="5"
						autosize
						maxlength="200"
						show-word-limit
						:placeholder="$t('text_suggest_important')"
					/>
				</van-form>
				<!-- 提交按钮 -->
				<div class="button-wrapper" v-if="!isShowTips">
					<div class="submit-btn" @click="handleAppraise">{{ $t("text_submit") }}</div>
        </div>
				<!-- 底部提示，点击回首页 -->
				<div @click="goToHome" v-if="isShowTips" class="tips-gohome" v-html="$t('text_goto_home')"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
	import { defineComponent, onMounted, reactive, toRefs, ref, getCurrentInstance } from 'vue'
	import { useI18n } from 'vue-i18n'
	import { Toast } from 'vant'
	import { useRoute, useRouter } from 'vue-router'
	import { appraiseTicket } from '@/api/tickets'
	interface dataT {
		[key: string]: unknown,
		ticketId?: number,
		selectNum: number,
		selectNum2: number,
		selectNum3: number,
		selectNum4: number,
		content: string,
		locked: boolean
	}
	export default defineComponent({
		name: 'oldTicketAppraise'
	})
</script>
<script setup lang="ts">
	const route = useRoute()
	const textarea = ref()
	const router = useRouter()
	const { t: $t } = useI18n()
	const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
	const data: dataT = reactive({
		selectNum: 0,
		selectNum2: 0,
		selectNum3: 0,
		selectNum4: 0,
		content: '',
		locked: false
	})
	const isShowTips = ref<boolean>(false)
	// 评价页面加载打点
  setLog({
    event: 'elfin_load_done',
    position: 'ticket_appraise',
    result: 'success'
  })


	onMounted(() => {
		if (route.query.ticketId) {
			data.ticketId = +route.query.ticketId
		} else {
			console.log('缺少重要参数')
		}
		// // H5兼容ios12 监听所有input blur
		// const inputsElement = [...document.getElementsByTagName('input')]
		// inputsElement.forEach(element => {
		// 	if (element) {
		// 		element.onblur = () => {
		// 			setTimeout(() => {
		// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
		// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
		// 			}, 300)
		// 		}
		// 	}
		// })
	})

	const showChildStar = ref<boolean>(false)
	const selectStar = (num: number) => {
		showChildStar.value = true
		data.selectNum = num + 1
		data.selectNum2 = data.selectNum3 = data.selectNum4 = num < 2 ? 0 : data.selectNum
	}
	const selectStarChild = (num: number, type: number) => {
		data['selectNum' + type] = num + 1
	}
	const handleAppraise = () => {
		// 解决安卓机器点击提交按钮触发键盘弹起的问题
		textarea.value.blur()
		if (data.locked) {
			// '评价提交中\n请稍后'
			Toast($t('text_submiting'))
			return
		}
		if (data.selectNum === 0 || data.selectNum2 === 0 || data.selectNum3 === 0 || data.selectNum4 === 0) {
			Toast($t('text_select_count'))
			return
		}
		const params = {
			ticket_id: data.ticketId,
			appraise: data.selectNum,
			service_rating: data.selectNum2,
      service_time_rating: data.selectNum3,
      service_solution_rating: data.selectNum4,
			remark: data.content
		}
		data.locked = true
		appraiseTicket(params).then(() => {
			// 评价成功打点
			setLog({
				button: 1,
				action: 'click',
				result: 1,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			data.locked = false
			isShowTips.value = true
		}, (res: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			Toast(res)
			data.locked = false
		}).catch((err: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				attitude_score: data.selectNum2,
				speed_score: data.selectNum3,
				treatment_score: data.selectNum4
			})
			Toast(err)
			data.locked = false
		})
	}
	const goToHome = (e: Event) => {
		if ((e.target as HTMLElement).localName === 'span') {
			router.push({
				path: '/newcs'
			})
		}
	}

	const { selectNum, selectNum2, selectNum3, selectNum4, content } = toRefs(data)
</script>

<style lang="scss" scoped>
.appraise {
	display: flex;
	align-items: center;
	font-size: 24px;
	color: #9ABBCD;
	margin-top: 20px;
	.text {
		margin-right: 10px;
		.required {
			word-break: break-all;
			height: 45px;
			line-height: 40px;
			max-width: 600px;
			.auto-font-size {
				text-align: right;
			}
		}
	}
}
.tips-gohome {
	width: 80%;
	margin: 30px auto;
	text-align: center;
	bottom: 50px;
	word-break: normal;
	font-size: 24px;
	color: #9ABBCD;
}
.form-item {
	margin-top: 20px;
}
.form-wrapper {
	& ::v-deep(.van-form) {
		font-size: 24px;
	}

	& ::v-deep(.van-cell) {
		background: rgba(5, 5, 6, 0.26);
		border: 1px solid #182033;
		padding: 15px 20px;
	}

	& ::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}
}
.star-box {
	width: 200px;
}
.star {
	display: inline-block;
	width: 37px;
	height: 37px;
	background: url("~@/assets/img/star.png") no-repeat;
	background-size: cover;
	&.light {
		background: url("~@/assets/img/light-star.png") no-repeat;
		background-size: cover;
	}
}

@media all and (orientation : portrait) {
	.appraise {
		.text {
			margin-right: 5px;
			.required {
				word-break: break-all;
				max-width: 420px;
			}
		}
		.star-box {
			width: 190px;
		}
	}
}
</style>
<style lang="scss">
.tips-gohome span {
  color: #ffffff;
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 0.05rem;
}
</style>
