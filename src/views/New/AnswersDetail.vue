<template>
  <div class="answers-detail">
    <div class="main-role"></div>
    <div class="content-body article-bg" id="t-container">
      <div class="content-detail" id="t-wrap" v-if="ticketDataList.length">
        <component
					v-for="(v, k) in ticketDataList"
					:key="k"
					:ref="v.module_group === 1003 ? v.process_session : 'comps'"
					@item="itemClick"
					@autoToTicket="toTicket"
					@updateScroll="updataScroll"
					@submitSuccess="createTicketSuccess"
					:itemData="v"
					:is="v.level === 3 ? v.relate_type === 2 ? autoFlowMap[v.module_group] : 'TicketForm' : 'TicketList'"
					:fromTicketId="fromTicketId"
				></component>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, onUpdated, nextTick, getCurrentInstance, ComponentInternalInstance, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { sceneEntrance, getRelationCats, getTplInfo, autoFlow, flowAndTemplate } from '@/api/tickets'
import { flowAndTemplateConversation } from '@/api/conversation'
import ticketComps from './components/AnswerComp'
import autoFlowComps from './components/AutoFlowComp'
import { Toast } from 'vant'
export default defineComponent({
	name: 'AnswersDetail',
	components: {
		...ticketComps,
		...autoFlowComps
	}
})
interface autoFlowDataT {
	process_session?: string,
	process_id?: number,
	node_id?: number,
	cat_id?: number,
	cat_name?: string,
	module_group?: number,
	fields?: Record<string, unknown & string> | string
}
interface ticketDataT {
	id?: number,
	label?: string,
	level?: number,
	tpl_id?: number,
	index: number,
	children?: ticketDataListT,
	process_id?: number,
  relate_type?: number,
  module_group?: number,
  process_session?: string
}
type ticketDataListT = Array<ticketDataT>
interface dataT {
	ticketDataList: Array<ticketDataT & autoFlowDataT>,
	fromTicketId: number,
	autoFlowMap: Record<number, string>
}
</script>
<script setup lang="ts">
const router = useRouter()
const route = useRoute()
const { t: $t } = useI18n()
const { commit, dispatch, state } = useStore()
commit('setLoadingCount', 1)
const data: dataT = reactive({
	ticketDataList: [],
	fromTicketId : 0,
	autoFlowMap: {
		1000: 'AutoRichText', // 流程富文本
		1001: 'AutoTickets', // 流程工单
		1002: 'AutoUserSelect', // 流程用户输入
		1003: 'AutoUserInput' // 流程映射
	}
})
const hasActiveConversation = computed(() => state.conversation.hasActiveConversation)
const conversationCatId = computed(() => state.conversation.catId)
const conversationId = computed(() => state.conversation.conversationId)
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const { proxy } = getCurrentInstance() as ComponentInternalInstance
// 页面加载打点
setLog({
	button: 0,
	action: 'loading',
	result: 1,
	position: 'que_type'
})

onUpdated(() => {
	updataScroll()
})
if (route.query.from_ticket_id) {
	data.fromTicketId = +route.query.from_ticket_id
}
if (route.query.cat_id) {
	const catId = +route.query.cat_id
	console.log('AnswersDetail isShowChat', state.global.isShowChat)
	// 新增智能客服跳转工单增加自动化流程逻辑
	const method = state.global.isShowChat ? flowAndTemplateConversation : flowAndTemplate
	method({
		cat_id: catId
	}).then((res: Record<string, unknown>) => {
		console.log('egress/cat/info res', res)
		dispatch('global/setConversationCatInfo', res)
		// 如果存在活跃会话且cat_id与当前cat_id相同，则跳转原会话页
		let params = {}
		console.log('hasActiveConversation.value', hasActiveConversation.value)
		console.log('conversationCatId.value', conversationCatId.value)
		if (hasActiveConversation.value && conversationCatId.value === catId) {
			console.log('存在活跃会话且cat_id与当前cat_id相同，conversationId是', conversationId.value)
			params = {
				conversation_id: conversationId.value
			}
			setLog({
				event: 'continueConversation',
				position: 'answersDetail',
				action: '2',
				result: 'success',
				conversation_id: conversationId.value,
				timestamp: new Date().getTime()
    	})
		}
			// 如果isShowChat为true且res.chat_or_form为1表示命中工单会话，跳转到工单对话页面
		if (state.global.isShowChat && res.chat_or_form === 1) {
			// router.push跳转方式会导致返回时，页面又回到当前页面，所以需要使用不添加路由记录的方式
			router.replace({
				path: '/newcs/ticketConversation',
				query: {
					cat_id: catId,
					origin: route.query.origin || '1',
					from: location.href,
					...params
				}
			})
			commit('setLoadingCount', 0)
			return
		}

		if (res.relate_type === 2) {
			// 由于后端接口自动化流程和工单数据中均含有fields字段，且重名，但是数据格式不一致，所以需要做区分
			if (res.fields === '') {
				res.fields = {}
			}
			autoFlow(res).then((res: autoFlowDataT) => {
				data.ticketDataList.push({
					index: data.ticketDataList.length,
					level: 3,
					relate_type: 2,
					...res
				})
				commit('setLoadingCount', 0)
			})
		} else if (res.relate_type === 1) {
			getTplInfo({
				cat_id: catId
			}).then((res: Record<string, unknown>) => {
				const item = {
					id: catId,
					level: 3,
					tpl_id: res.tpl_id as number,
					index: data.ticketDataList.length
				}
				data.ticketDataList.push(item)
				commit('setLoadingCount', 0)
			})
		}
	}).catch((err: string) => {
		commit('setLoadingCount', 0)
		console.log('egress/cat/info err', err)
	}).finally(() => {
		commit('setLoadingCount', 0)
	})
} else {
	let params = {}, getCatsUrl = null
	// 如果有level_id，那就是聊天页"提交工单"，关联工单不同层级分类
	if (route.query.level_id) {
		params = { fork_cat_ids: [Number(route.query.level_id)] }
		getCatsUrl = getRelationCats(params)
	} else {
		// 获取入口信息, 判断是否是智能客服点踩后跳转的工单需求
		params = route.query.dislike_tickets && route.query.dislike_tickets.length > 0 ? {
			fork_cat_ids: (route.query.dislike_tickets as Array<string>).map((item: string | number) => {
				return +item
			})
		} : {}
		getCatsUrl = sceneEntrance(params)
	}
	getCatsUrl.then((res: ticketDataListT) => {
		commit('setLoadingCount', 0)
		// 前端处理数据，看返回数据是否只有一个分支，如果只有一个分支，则只要最后一层的对象
		const isOne = (list: Array<ticketDataT>): false | ticketDataT => {
			if (list.length === 1) {
				if (list[0].children) {
					return isOne(list[0].children)
				} else {
					return list[0]
				}
			} else {
				return false
			}
		}
		// 处理后数组则为树状层级筛选，如果为对象则直接渲染表单
		const rdata = isOne(res)
		if (rdata === false) {
			if (res.length > 0) {
				data.ticketDataList.push({
					level: 0,
					index: data.ticketDataList.length,
					children: res
				})
			}
		} else {
			const item = {
				...rdata,
				index: data.ticketDataList.length
			}
			data.ticketDataList.push(item)
		}
		// 如果直接关联的是三级分类，并且是自动化流程，则直接请求自动化流程数据
		if (rdata !== false && rdata.relate_type === 2) {
			const { id, label, ...rest } = rdata
			rest.cat_id = id
			rest.cat_name = label
			autoFlow(rest).then((res: autoFlowDataT) => {
				data.ticketDataList.push({
					index: data.ticketDataList.length,
					level: 3,
					relate_type: 2,
					...res
				})
				commit('setLoadingCount', 0)
			})
		}
	}, (res: string) => {
		commit('setLoadingCount', 0)
		Toast(res)
	}).catch((err: string) => {
		commit('setLoadingCount', 0)
		Toast(err)
	}).catch((err: string) => {
		commit('setLoadingCount', 0)
		console.log('egress/cat/info err', err)
	}).finally(() => {
		commit('setLoadingCount', 0)
	})
}
// 工单分类点击逻辑 + 自动化流程逻辑
const itemClick = (item: ticketDataT & autoFlowDataT): void => {
	const mark = data.ticketDataList.some(t => {
		return t.id && t.id === item.id
	})
	if (mark) {
		// Toast.fail('内容已存在\n请勿重复选择')
		Toast.fail($t("text_had"))
	} else {
		// 工单分类list点击打点
		setLog({
			button: item.id,
			action: 'click',
			result: 1,
			position: 'que_type'
		})
		// 如果添加的数据为表单且已经存在表单，则删除原表单（产品要求只能有一个表单）
		if (item.level === 3 && item.relate_type === 1) { // 增加判断，区分是触发表单还是触发自动化流程
			const findex = data.ticketDataList.findIndex(e => {
				if (e.level === 3) {
					return e.index
				}
			})
			if (findex > -1) {
				data.ticketDataList.splice(findex, 1)
				for(let i = findex; i < data.ticketDataList.length; i++) {
					data.ticketDataList[i].index = i
				}
			}
		}
		// 解决ticketDataList长度不变导致不触发ticket form组件初始化
		nextTick(() => {
			item.index = data.ticketDataList.length
			if (item.relate_type === 1) {
				// 工单分类或表单内容
				data.ticketDataList.push(item)
				autoScroll(item.level, item.tpl_id)
			} else if (item.relate_type === 2) {
				// 自动化流程内容
				let requestData: Record<string, unknown> = {}
				// 三级分类跳转到自动化流程的数据格式和自动化流程的数据格式不一样，需要做区分
				if (item.process_session) {
					requestData = {...item}
				} else {
					requestData = {
						cat_id: item.id,
						process_id: item.process_id,
						relate_type: item.relate_type,
						cat_name: item.label
					}
				}
				autoFlow(requestData).then((res: autoFlowDataT) => {
					data.ticketDataList.push({
						index: data.ticketDataList.length,
						level: 3,
						relate_type: 2,
						...res
					})
					autoScroll(item.level, item.tpl_id)
				}, (res: string) => {
					Toast(res)
					// eslint-disable-next-line
					const r = proxy?.$refs[(requestData.process_session as string)] as any
					r[0].reset()
				})
			}
		})
	}
}
// 点选后 滚动到底部 (有自动化流程 和 '词库'关联任意层级分类 和 两次未命中 列表的点选滚动)
const autoScroll = (level?: number, tpl_id?: number|boolean) => {
	const container = document.querySelector('#t-wrap')
	if (level === 3 && tpl_id) {
		// 由于表单组件初始化需要时间，所以需要延迟滚动 才能拿到表单组件的高度(只针对最后的表单组件)
		setTimeout(() => {
			container?.scrollTo({ top: container?.scrollHeight, behavior: 'smooth' })
		}, 800)
	} else {
		// 使用 requestAnimationFrame 来确保在滚动之前容器已经完全渲染
		requestAnimationFrame(() => {
			container?.scrollTo({ top: container?.scrollHeight, behavior: 'smooth' })
		})
	}
}
const updataScroll = (): void => {
	const container = document.getElementById('t-container')
	const t_wrapper = document.getElementById('t-wrap')
	if (data.ticketDataList.length < 1) return
	const index = data.ticketDataList[data.ticketDataList.length - 1].index
	if (container === null || t_wrapper === null) return
	// const newItemH = container.querySelectorAll('.t-items')[index]?.getBoundingClientRect().height
	const containerH = t_wrapper.scrollHeight
	// 首屏不滚动
	data.ticketDataList.length !== 1 && (container.scrollTop = containerH)
}
const createTicketSuccess = (ticket_id: number): void => {
	router.push({
		path: '/newcs/ticketDetail',
		query: {
			ticketId: ticket_id,
			from: 'question'
		}
	})
}
// 自动化流程跳转工单
const toTicket = (autoInfo: autoFlowDataT): void => {
	data.ticketDataList.push({
		id: autoInfo.cat_id,
		level: 3,
		tpl_id: (autoInfo.fields as Record<string, unknown>).tpl_id as number,
		index: data.ticketDataList.length,
		process_id: autoInfo.process_id,
		process_session: autoInfo.process_session
	})
	autoScroll(3, true)
}

const { ticketDataList, fromTicketId, autoFlowMap } = toRefs(data)
</script>

<style lang="scss" scoped>

</style>
