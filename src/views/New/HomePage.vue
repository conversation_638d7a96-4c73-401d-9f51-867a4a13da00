<template>
  <div class="answers-detail" v-if="vipUser">
    <div class="main-role"></div>
    <div class="article-bg vip-box">
      <div class="main-title">{{ baseInfo.temp_title }}</div>
      <div class="main-desc">{{ baseInfo.temp_desc }}</div>
      <div class="line"></div>
      <div class="history-btn" @click="toHistory">{{ $t('text_history') }}</div>
      <!-- 卡片模式 -->
      <div v-if="!!baseInfo.card_lists[0]?.image_url" class="cards-wapper">
        <dl class="sec-card" v-for="(v, k) in baseInfo.card_lists" :key="k" @click="cardClickHandle(v, k)">
          <dt><img :src="v.image_url" /></dt>
          <div class="sec-line"></div>
          <dd><auto-font-size :text="v.image_title"></auto-font-size></dd>
        </dl>
      </div>
      <!-- 列表模式 -->
      <div v-else class="list-wapper">
        <div class="list-item" v-for="(v, k) in baseInfo.card_lists" :key="k" @click="cardClickHandle(v, k)">
          <img src="~@/assets/img/point.png"><span>{{ v.image_title }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="home-page" v-else>
    <div class="main">
      <div class="main-role" :class="{ show: baseInfo.card_lists.length }"></div>
      <div class="container home-bg" :class="{ show: baseInfo.card_lists.length }">
        <div class="nav-box" v-if="!state.global.isShowChat">
          <div class="nav-item active-nav"><auto-font-size :text="$t('tab_home')"></auto-font-size></div>
          <div class="nav-item" @click="replaceToGpt"><auto-font-size :text="$t('tab_ai_new')"></auto-font-size></div>
        </div>
        <div class="wrap">
          <HomeSwiper></HomeSwiper>
          <HomeCards></HomeCards>
          <HomeArticles></HomeArticles>
        </div>
      </div>
    </div>
    <!-- 添加继续会话入口 -->
    <div class="continue-conversation-btn" @click="continueConversation" v-if="hasActiveConversation">{{ $t('text_button_continue_conversation') }}</div>
    <BottomWrap @hotWord-click="hotWordClick" @submit="submit"></BottomWrap>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, getCurrentInstance } from 'vue'
import type { TNewCard } from '@/enum/types'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'HomePage'
})
</script>
<script setup lang="ts">
import BottomWrap from './components/BottomWrap.vue'
import HomeSwiper from './components/HomePageComp/HomeSwiper.vue'
import HomeCards from './components/HomePageComp/HomeCards.vue'
import HomeArticles from './components/HomePageComp/HomeArticles.vue'
import { ref, onMounted, onUnmounted } from 'vue'
import { getContinueConversationStatus } from '@/api/conversation'
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
setLog({
	event: 'elfin_load_done',
  position: 'homepage',
  result: 'success'
})
const { state, commit } = useStore()
const baseInfo = computed(() => state.baseInfo)
const vipUser = JSON.parse(sessionStorage.getItem('vipUser') || baseInfo.value.vipUser || 'false')
const router = useRouter()
const replaceToGpt = () => {
  router.replace({
    name: 'NewSmart'
  })
}
const hotWordClick = (search: string) => {
  jumpToGpt(search, 'chat_keyword')
}
const submit = (search: string, sourceType: string) => {
  // 解决部分机型，在webview中跳转GPT页面后，点击回退按钮，首页轮播图不显示的问题
  setTimeout(() => {
    jumpToGpt(search, sourceType)
  }, 600)
}
const jumpToGpt = (txt: string, sourceType: string) => {
  router.push({
    name: 'NewSmart',
    query: {
      q: txt,
      sourceType,
      type: 'gpt'
    }
  })
}
const cardClickHandle = (v: TNewCard, index: number) => {
  // card_group 关联子卡片(1：图片 4：列表) 2：关联知识 3：关联表单分类
  if (v.card_group === 1 || v.card_group === 4) {
    router.push({
      name: 'SecondLevel',
      query: {
        temp_id: baseInfo.value.temp_id,
        card_idx: index,
        title: v.image_title
      }
    })
  } else if (v.card_group === 2) {
    router.push({
      path: '/newcs/articleDetail',
      query: {
        art_id: v.art_id,
        card_group: v.card_group
      }
    })
  } else if (v.card_group === 3) {
    router.push({
      path: '/newcs/answersDetail',
      query: {
        cat_id: v.ticket_cat_id
      }
    })
  }
}
const toHistory = () => {
  router.push({
    name: 'NewHistory'
  })
}
// 添加继续会话相关逻辑
const hasActiveConversation = ref(false)
const activeConversationId = ref('')
const activeCatId = ref(0)
// 添加定时器引用
const continueConversationTimer = ref<number | null>(null)

// 清除定时器的函数
const clearContinueConversationTimer = () => {
  if (continueConversationTimer.value) {
    clearTimeout(continueConversationTimer.value)
    continueConversationTimer.value = null
  }
}

onMounted(() => {
  console.log('HomePage global isShowChat', state.global.isShowChat)
  if (state.global.isShowChat) {
    getContinueConversationStatusFunc()
  }
})

const getContinueConversationStatusFunc = async () => {
  try {
    let isDebug = false;
    let res: any;

    const conversationId = sessionStorage.getItem('conversationId') || ''
    if (isDebug) {
      res = {
        code: 0,
        data: {
          has_active_conversation: true,
          cat_id: 1823,
          conversation_id: conversationId // 测试数据
        }
      }
    } else {
      res = await getContinueConversationStatus()
    }

    console.log('getContinueConversationStatusFunc res', res)
    if (res.code === 0) {
      const { has_active_conversation, cat_id, conversation_id } = res.data;
      if (has_active_conversation) {
        hasActiveConversation.value = true
        activeConversationId.value = conversation_id
        activeCatId.value = cat_id
        commit('conversation/SET_HAS_ACTIVE_CONVERSATION', true)
        commit('conversation/SET_CAT_ID', cat_id)
        commit('conversation/SET_CONVERSATION_ID', conversation_id)

        // 设置10分钟后自动隐藏继续会话入口
        clearContinueConversationTimer()
        continueConversationTimer.value = window.setTimeout(() => {
          hasActiveConversation.value = false
          commit('conversation/SET_CAT_ID', 0)
          commit('conversation/SET_CONVERSATION_ID', '')
          commit('conversation/SET_HAS_ACTIVE_CONVERSATION', false)
          clearContinueConversationTimer()
        }, 10 * 60 * 1000)

        // 继续会话入口展示时打点
        setLog({
          event: 'continueConversation',
          position: 'homePage',
          action: '1',
          result: 'success',
          conversation_id: conversation_id,
          timestamp: new Date().getTime()
        })
      }
    }
  } catch (error) {
    console.error('getContinueConversationStatusFunc error', error)
  }
}

// 在组件销毁时清除定时器
onUnmounted(() => {
  clearContinueConversationTimer()
})

const continueConversation = () => {
    // 继续会话入口点击时打点
    // event	事件标识	continueConversation
    // position	按钮所在页面位置	homePage
    // action	操作类型	2-click
    // result	操作结果	success
    // conversation_id	上次会话唯一标识	<上次会话 ID>
    // timestamp	操作时间	时间戳
    setLog({
      event: 'continueConversation',
      position: 'homePage',
      action: '2',
      result: 'success',
      conversation_id: activeConversationId.value,
      timestamp: new Date().getTime()
    })
    router.push({
      path: '/newcs/ticketConversation',
      query: {
        cat_id: activeCatId.value,
        conversation_id: activeConversationId.value,
        origin: '3'
      }
    })
}
</script>

<style lang="scss" scoped>
.home-page {
  position: relative;
  width: 100%;
  height: 100%;
  padding-bottom: 118px;
  .main {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 118px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
  }
  .main-role {
    height: 531px;
    width: 398px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    z-index: 1;
    left: -300px;
    opacity: 0;
    &.show {
      left: -160px;
      opacity: 1;
      transition: all .2s ease-in-out;
    }
  }
  .container {
    width: 907px;
    height: 542px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: -130px;
    box-sizing: border-box;
    margin-bottom: 18px;
    padding: 26px 34px 11px;
    position: relative;
    right: -260px;
    opacity: 0;
    .nav-box {
      width: 131px;
      position: absolute;
      top: 40px;
      right: -130px;
      .nav-item {
        box-sizing: border-box;
        width: 100%;
        height: 43px;
        padding: 0px 4px;
        margin-bottom: 10px;
        @include backgroundSec('soc/bg-nav.png');
        text-align: center;
        line-height: 43px;
        font-size: 24px;
        color: #B4CCE4;
        font-family: Source Han Serif CN;
        cursor: pointer;
        &.active-nav {
          color: #1B1F22;
          @include backgroundSec('soc/bg-nav-active.png');
        }
      }
    }
    &.show {
      right: 68px;
      opacity: 1;
      transition: all .2s ease-in-out;
    }
    .wrap {
      width: 100%;
      height: 100%;
      // overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-start;
    }
  }
}
.vip-box {
  height: 652px;
  width: 907px;
  margin-bottom: 21px;
  margin-left: -40px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 25px 50px;
  display: flex;
  flex-direction: column;

  .main-title {
    font-family: Source Han Serif CN;
    font-weight: 400;
    font-size: 34px;
    color: #D4AD5B;
    text-align: center;
    text-shadow: 0px 1px 0px rgba(0, 0, 0, 0.6);
  }
  .main-desc {
    font-family: Source Han Serif CN;
    font-weight: 400;
    font-size: 24px;
    color: #eaddbf !important;
    text-align: left;
    margin-top: 20px;
  }
  .history-btn {
    font-family: Source Han Serif CN;
    font-weight: 400;
    font-size: 24px;
    text-align: right;
    margin-top: 20px;
    cursor: pointer;
  }

  .cards-wapper {
    display: block;
    margin-top: 20px;
    // flex: 1;
    padding: 15px 15px 0px;
    overflow-y: auto;
    background-color: #2d2c2b;
  }

  .sec-card {
    width: 236px;
    height: 162px;
    display: inline-block;
    background-color: #595959 !important;
    margin: 0px 0px 15px 34px;

    &:nth-child(3n+1) {
      margin-left: 0px;
    }

    dt {
      box-sizing: border-box;
      width: 100%;
      display: block;
      padding-top: 18px;

      img {
        width: 55px;
        height: 55px;
        display: block;
        margin: 0px auto 0;
      }
    }

    .sec-line {
      content: "";
      display: block;
      height: 2px;
      width: 152px;
      @include backgroundSec('soc/sec-card-line.png');
      margin: 18px auto 0;
    }

    dd {
      display: block;
      width: 90%;
      margin: 0 auto;
      box-sizing: border-box;
      padding: 0 10px;
      font-weight: 400;
      font-size: 20px;
      color: #E2C885;
      line-height: 59px;
    }
  }

  .list-wapper {
    margin-top: 20px;

    .list-item {
      display: flex;
      align-items: center;
      margin: 20px 0;
      font-size: 24px;
      color: #E2C885;

      img {
        width: 40px;
        height: 40px;
        margin: 0px 5px 0 0;
        align-self: flex-start;
      }
    }
  }
  .line {
    content: "";
    display: block;
    height: 2px;
    width: 100%;
    @include backgroundSec('soc/bg-line.png');
    margin: 20px auto 0;
  }
}

// 竖版
@media all and (orientation : portrait) {
  .home-page {
    .main {
      margin-top: calc(env(safe-area-inset-top) + var(--head-bar-height) + 75px) !important;
    }
    .main-role {
      display: none;
    }
    .container {
      margin: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 30px;
      opacity: 0;
      .nav-box {
        display: flex;
        width: 100%;
        position: absolute;
        top: -60px;
        left: 10px;
        .nav-item {
          box-sizing: border-box;
          width: 135px;
          height: 43px;
          padding: 0px 4px;
          margin-left: 20px;
          @include backgroundSec('soc/bg-nav.png');
          text-align: center;
          line-height: 43px;
          font-size: 24px;
          color: #B4CCE4;
          font-family: Source Han Serif CN;
          cursor: pointer;
        }
      }
      &.show {
        right: 0;
        opacity: 1;
        transition: all .2s ease-in-out;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.continue-conversation-btn {
  position: absolute;
  // right: 180px;
  bottom: 160px;
  left: 50%;
  transform: translateX(-50%);
  margin-left: 400px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14px 20px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 22px;
  color: #3C3016;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  background: #D1AE5B;
  box-shadow: inset 0px 1px 0px 0px #FFD87D, inset 0px -1px 0px 0px #FFD87D, inset -1px 0px 0px 0px #FFD87D, inset 1px 0px 0px 0px #FFD87D;
  border-radius: 28px;
  cursor: pointer;
}

@media all and (orientation: portrait) {
  .continue-conversation-btn {
    right: 44px;
    bottom: 200px;
    left: 50%;
    transform: none;
    margin-left: 0;
  }
}
</style>
