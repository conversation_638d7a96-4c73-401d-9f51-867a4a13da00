<template>
	<div class="answers-detail">
		<div class="main-role"></div>
		<div class="content-body article-bg">
			<div class="content">
				<van-form :show-error="false" class="form-wrapper">
					<div class="title_box">
						<p class="text_title">{{ $t('text_question_survey') }}</p>
						<p>{{ $t('text_feedback') }}</p>
					</div>
					<div>
						<p class="radio_des">1、{{ $t('text_service_rating') }}</p>
						<van-radio-group v-model="selectNum" direction="horizontal" shape="dot">
							<van-radio v-for="v in radioList" :key="v.id" :name="v.id" checked-color="#D4AD5B" icon-size="0.16rem">{{ v.text }}</van-radio>
						</van-radio-group>
					</div>
					<div>
						<p class="radio_des">2、{{ $t('text_recommendation') }}</p>
						<van-radio-group v-model="recommendation_level" direction="horizontal" shape="dot">
							<van-radio v-for="v in radioList" :key="v.id" :name="v.id" checked-color="#D4AD5B" icon-size="0.16rem">{{ v.text }}</van-radio>
						</van-radio-group>
					</div>
					<div>
						<p class="radio_des">3、{{ $t('text_other_instructions') }}</p>
						<van-field
							ref="textarea"
							v-model="content"
							type="textarea"
							rows="3"
							autosize
							maxlength="200"
							show-word-limit
							:placeholder="$t('text_suggest_important')"
						/>
					</div>
				</van-form>
				<!-- 提交按钮 -->
				<div class="button-wrapper" v-if="!isShowTips">
					<div class="submit-btn" @click="handleAppraise">{{ $t("text_submit") }}</div>
        </div>
				<!-- 底部提示，点击回首页 -->
				<div @click="goToHome" v-if="isShowTips" class="tips-gohome" v-html="$t('text_goto_home')"></div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
	import { defineComponent, onMounted, reactive, toRefs, ref, getCurrentInstance } from 'vue'
	import { useI18n } from 'vue-i18n'
	import { Toast } from 'vant'
	import { useRoute, useRouter } from 'vue-router'
	import { appraiseTicket } from '@/api/tickets'
	interface dataT {
		[key: string]: unknown,
		ticketId?: number,
		selectNum: number | null,
		recommendation_level: number | null,
		content: string,
		locked: boolean
	}
	export default defineComponent({
		name: 'TicketAppraise'
	})
</script>
<script setup lang="ts">
	const route = useRoute()
	const textarea = ref()
	const router = useRouter()
	const { t: $t } = useI18n()
	const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
	const data: dataT = reactive({
		selectNum: null,
		recommendation_level: null,
		content: '',
		locked: false
	})
	const isShowTips = ref<boolean>(false)
	const radioList = [
		{ id: 5, text: $t('btn_radio_five') },
		{ id: 4, text: $t('btn_radio_four') },
		{ id: 3, text: $t('btn_radio_three') },
		{ id: 2, text: $t('btn_radio_two') },
		{ id: 1, text: $t('btn_radio_one') }
	]
	// 评价页面加载打点
  setLog({
    event: 'elfin_load_done',
    position: 'ticket_appraise',
    result: 'success'
  })

	onMounted(() => {
		if (route.query.ticketId) {
			data.ticketId = +route.query.ticketId
		} else {
			console.log('缺少重要参数')
		}
		// // H5兼容ios12 监听所有input blur
		// const inputsElement = [...document.getElementsByTagName('input')]
		// inputsElement.forEach(element => {
		// 	if (element) {
		// 		element.onblur = () => {
		// 			setTimeout(() => {
		// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
		// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
		// 			}, 300)
		// 		}
		// 	}
		// })
	})

	const handleAppraise = () => {
		// 解决安卓机器点击提交按钮触发键盘弹起的问题
		textarea.value.blur()
		if (data.locked) {
			// '评价提交中\n请稍后'
			Toast($t('text_submiting'))
			return
		}
		if (!data.selectNum || !data.recommendation_level) {
			Toast($t('text_select_count'))
			return
		}
		const params = {
			ticket_id: data.ticketId,
			appraise: data.selectNum,
			recommendation_level: data.recommendation_level,
			remark: data.content,
			// 以下3个为废弃字段，但是后端接口需要传值，都写1(新工单v1.0版本C端)
			service_rating: 1,
			service_time_rating: 1,
			service_solution_rating: 1
		}
		data.locked = true
		appraiseTicket(params).then(() => {
			// 评价成功打点
			setLog({
				button: 1,
				action: 'click',
				result: 1,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				recommendation_level: data.recommendation_level
			})
			data.locked = false
			isShowTips.value = true
		}, (res: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				recommendation_level: data.recommendation_level
			})
			Toast(res)
			data.locked = false
		}).catch((err: string) => {
			// 评价失败打点
			setLog({
				button: 1,
				action: 'click',
				result: 0,
				position: 'evaluation',
				appraisal_time: new Date().getTime(),
				score: data.selectNum,
				recommendation_level: data.recommendation_level
			})
			Toast(err)
			data.locked = false
		})
	}
	const goToHome = (e: Event) => {
		if ((e.target as HTMLElement).localName === 'span') {
			router.push({
				path: '/newcs'
			})
		}
	}

	const { selectNum, recommendation_level, content } = toRefs(data)
</script>

<style lang="scss" scoped>
.content {
	color: #D4AD5B;
	font-size: 24px;
	overflow: auto;
}
.title_box {
	text-align: center;
	.text_title {
		font-size: 30px;
	}
}
.radio_des {
	margin: 25px 0;
}
.tips-gohome {
	width: 80%;
	margin: 30px auto;
	text-align: center;
	bottom: 50px;
	word-break: normal;
	font-size: 24px;
	color: #9ABBCD;
}
.form-wrapper {
	& ::v-deep(.van-form) {
		font-size: 24px;
	}

	& ::v-deep(.van-cell) {
		background: rgba(5, 5, 6, 0.26);
		border: 1px solid #182033;
		padding: 15px 20px;
	}

	& ::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}
	& ::v-deep(.van-radio__label) {
		color: #D4AD5B;
	}
	& ::v-deep(.van-radio-group--horizontal) {
		height: 4vh;
	}
}
</style>
<style lang="scss">
.tips-gohome span {
  color: #ffffff;
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 0.05rem;
}
</style>
