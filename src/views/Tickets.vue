<template>
	<loading-wrap :loading="loading" @updateScroll="updataScroll">
		<div class="ticket-bg" id="t-container">
			<div class="ticket-wrapper" id="t-wrap" v-if="ticketDataList.length > 0">
				<div class="history-btn-box">
						<div class="history-btn"  :class="{ 'has-unread': isRead && readCount > 0 }" :data-unread="readCount" @click="goHistory">{{ $t('text_history') }}
							<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
								<g id="309&#x4E0B;&#x4E00;&#x9875;&#x3001;&#x524D;&#x8FDB;&#x3001;&#x67E5;&#x770B;&#x66F4;&#x591A;">
									<g id="icon/24/arrow/right">
										<path id="Vector" d="M12 24L20 16L12 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
									</g>
								</g>
							</svg>
						</div>
				</div>
				<component
					v-for="(v, k) in ticketDataList"
					:key="k"
					:ref="v.module_group === 1003 ? v.process_session : 'comps'"
					@item="itemClick"
					@autoToTicket="toTicket"
						@updateScroll="updataScroll"
						@submitSuccess="createTicketSuccess"
						:itemData="v"
						:is="v.level === 3 ? v.relate_type === 2 ? autoFlowMap[v.module_group] : 'TicketForm' : 'TicketList'"
						:fromTicketId="fromTicketId"
					></component>
			</div>
			<div v-else class="nodata">
				{{ $t("text_nodata") }}
			</div>
		</div>
	</loading-wrap>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, onUpdated, nextTick, getCurrentInstance, ComponentInternalInstance, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute } from 'vue-router'
import { sceneEntrance, getTplInfo, autoFlow, flowAndTemplate } from '@/api/tickets'
import ticketComps from '@/components/ticketComp'
import autoFlowComps from '@/components/autoFlowComp'
import LoadingWrap from '@/components/loadingWrap'
import { Toast } from 'vant'
import { checkUnread } from '@/api/smart'
export default defineComponent({
	name: 'Tickets',
	components: {
		...ticketComps,
		...autoFlowComps,
		LoadingWrap
	}
})
interface autoFlowDataT {
	process_session?: string,
	process_id?: number,
	node_id?: number,
	cat_id?: number,
	cat_name?: string,
	module_group?: number,
	fields?: Record<string, unknown & string> | string
}
interface ticketDataT {
	id?: number,
	label?: string,
	level?: number,
	tpl_id?: number,
	index: number,
	children?: ticketDataListT,
	process_id?: number,
  relate_type?: number
}
type ticketDataListT = Array<ticketDataT>
interface dataT {
	ticketDataList: Array<ticketDataT & autoFlowDataT>,
	loading: boolean,
	fromTicketId: number,
	autoFlowMap: Record<number, string>
}
</script>
<script setup lang="ts">
const router = useRouter()
const route = useRoute()
const { t: $t } = useI18n()
const data: dataT = reactive({
	ticketDataList: [],
	loading: true,
	fromTicketId : 0,
	autoFlowMap: {
		1000: 'AutoRichText', // 流程富文本
		1001: 'AutoTickets', // 流程工单
		1002: 'AutoUserSelect', // 流程用户输入
		1003: 'AutoUserInput' // 流程映射
	}
})
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const { proxy } = getCurrentInstance() as ComponentInternalInstance
const isRead = ref(false)
const readCount = ref(0)
checkUnread({}).then((res: Record<string, unknown>) => {
	console.log('checkUnread res', res)
  isRead.value = true
	readCount.value = res.notice_count as number || 0
}, () => {
  isRead.value = false
	readCount.value = 0
})
// 页面加载打点
setLog({
	button: 0,
	action: 'loading',
	result: 1,
	position: 'que_type'
})

onUpdated(() => {
	updataScroll()
})
if (route.query.from_ticket_id) {
	data.fromTicketId = +route.query.from_ticket_id
}
if (route.query.cat_id) {
	const catId = +route.query.cat_id
	// 新增智能客服跳转工单增加自动化流程逻辑
	flowAndTemplate({
		cat_id: catId
	}).then((res: Record<string, unknown>) => {
		if (res.relate_type === 2) {
			// 由于后端接口自动化流程和工单数据中均含有fields字段，且重名，但是数据格式不一致，所以需要做区分
			if (res.fields === '') {
				res.fields = {}
			}
			autoFlow(res).then((res: autoFlowDataT) => {
				data.ticketDataList.push({
					index: data.ticketDataList.length,
					level: 3,
					relate_type: 2,
					...res
				})
				data.loading = false
			})
		} else if (res.relate_type === 1) {
			getTplInfo({
				cat_id: catId
			}).then((res: Record<string, unknown>) => {
				const item = {
					id: catId,
					level: 3,
					tpl_id: res.tpl_id as number,
					index: data.ticketDataList.length
				}
				data.ticketDataList.push(item)
				data.loading = false
			})
		}
	})
} else {
	// 获取入口信息, 判断是否是智能客服点踩后跳转的工单需求
	const params = route.query.dislike_tickets && route.query.dislike_tickets.length > 0 ? {
		fork_cat_ids: (route.query.dislike_tickets as Array<string>).map((item: string | number) => {
			return +item
		})
	} : {}
	// 获取入口树-数据来源是 客服工单系统-问题分类配置
	sceneEntrance(params).then((res: ticketDataListT) => {
		data.loading = false
		// 前端处理数据，看返回数据是否只有一个分支，如果只有一个分支，则只要最后一层的对象
		const isOne = (list: Array<ticketDataT>): false | ticketDataT => {
			if (list.length === 1) {
				if (list[0].children) {
					return isOne(list[0].children)
				} else {
					return list[0]
				}
			} else {
				return false
			}
		}
		// 处理后数组则为树状层级筛选，如果为对象则直接渲染表单
		const rdata = isOne(res)
		if (rdata === false) {
			if (res.length > 0) {
				data.ticketDataList.push({
					level: 0,
					index: data.ticketDataList.length,
					children: res
				})
			}
		} else {
			const item = {
				...rdata,
				index: data.ticketDataList.length
			}
			data.ticketDataList.push(item)
		}
	}, (res: string) => {
		data.loading = false
		Toast(res)
	}).catch((err: string) => {
		data.loading = false
		Toast(err)
	})
}
// 工单分类点击逻辑 + 自动化流程逻辑
const itemClick = (item: ticketDataT & autoFlowDataT): void => {
	const mark = data.ticketDataList.some(t => {
		return t.id && t.id === item.id
	})
	if (mark) {
		// Toast.fail('内容已存在\n请勿重复选择')
		Toast.fail($t("text_had"))
	} else {
		// 工单分类list点击打点
		setLog({
			button: item.id,
			action: 'click',
			result: 1,
			position: 'que_type'
		})

		// 如果添加的数据为表单且已经存在表单，则删除原表单（产品要求只能有一个表单）
		if (item.level === 3 && item.relate_type === 1) {// 增加判断，区分是触发表单还是触发自动化流程
			const findex = data.ticketDataList.findIndex(e => {
				if (e.level === 3) {
					return e.index
				}
			})
			if (findex > -1) {
				data.ticketDataList.splice(findex, 1)
				for(let i = findex; i < data.ticketDataList.length; i++) {
					data.ticketDataList[i].index = i
				}
			}
		}
		nextTick(() => { // 解决ticketDataList长度不变导致不触发ticket form组件初始化
			item.index = data.ticketDataList.length
			if (item.relate_type === 1) {
				// 工单分类或表单内容
				data.ticketDataList.push(item)
			} else if (item.relate_type === 2) {
				// 自动化流程内容
				let requestData: Record<string, unknown> = {}
				// 三级分类跳转到自动化流程的数据格式和自动化流程的数据格式不一样，需要做区分
				if (item.process_session) {
					requestData = {...item}
				} else {
					requestData = {
						cat_id: item.id,
						process_id: item.process_id,
						relate_type: item.relate_type,
						cat_name: item.label
					}
				}
				autoFlow(requestData).then((res: autoFlowDataT) => {
					data.ticketDataList.push({
						index: data.ticketDataList.length,
						level: 3,
						relate_type: 2,
						...res
					})
				}, (res: string) => {
					Toast(res)
					// eslint-disable-next-line
					const r = proxy?.$refs[(requestData.process_session as string)] as any
					r[0].reset()
				})
			}
		})
	}
}
const updataScroll = (): void => {
	const container = document.getElementById('t-container')
	const t_wrapper = document.getElementById('t-wrap')
	if (data.ticketDataList.length < 1) return
	const index = data.ticketDataList[data.ticketDataList.length - 1].index
	if (container === null || t_wrapper === null) return
	const newItemH = container.querySelectorAll('.t-items')[index].getBoundingClientRect().height
	const containerH = t_wrapper.scrollHeight
	// 首屏不滚动
	data.ticketDataList.length !== 1 && (container.scrollTop = containerH - newItemH)
}
const createTicketSuccess = (ticket_id: number): void => {
	router.push({
		path: '/detail',
		query: {
			ticketId: ticket_id,
			from: 'question'
		}
	})
}
const goHistory = () =>{
	router.push('/history')
}
// 自动化流程跳转工单
const toTicket = (autoInfo: autoFlowDataT): void => {
	data.ticketDataList.push({
		id: autoInfo.cat_id,
		level: 3,
		tpl_id: (autoInfo.fields as Record<string, unknown>).tpl_id as number,
		index: data.ticketDataList.length,
		process_id: autoInfo.process_id,
		process_session: autoInfo.process_session
	})
}

const { ticketDataList, loading, fromTicketId, autoFlowMap } = toRefs(data)
</script>

<style lang="scss" scoped>
.history-btn-box {
	position: relative;
	margin-bottom: 13.34px;
	display: flex;
	width: 100%;
	justify-content: flex-end;
	align-items: center;
}
.nodata {
	color: #c2a862;
	font-size: 53.36px;
	margin: 133.4px auto 0px;
	text-align: center;
}

.history-btn {
	position: relative;
	display: flex;
	align-items: center;
	cursor: pointer;
	svg {
		position: relative;
		left: -10px;
		width: 44px;
		color: #c2a862;
	}
	&.has-unread {
		&:after {
			content: attr(data-unread);
			position: absolute;
			top: -10px;
			right: -10px;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 40px;
			height: 40px;
			box-sizing: border-box;
			padding-bottom: 2px;
			font-weight: 500;
			font-size: 24px;
			color: #FFF7E8;
			@include backgroundSec('history_count.png');
		}
	}
}

.red-p {
	width: 12px;
	&:after {
		content: '';
		display: block;
		position: absolute;
		top: -10px;
		right: 0;
		width: 12px;
		height: 12px;
		border-radius: 50%;
		@include backgroundSec('red_p.png');
	}
}
// 竖屏
@media (orientation: portrait) {
	.history-btn {
		svg {
			width: 104px;
			color: #787878;
		}
		&.has-unread {
			&:after {
				top: 0px;
				right: -20px;
				width: 64px;
				height: 64px;
				font-size: 40px;
				padding-bottom: 0px;
			}
		}
	}
	.red-p {
		width: 26px;
		&:after {
			width: 26px;
			height: 26px;
			top: 10px;
			right: 25px;
		}
	}
}

// pc 屏幕尺寸大于1024px
@media (min-width: 1024px) {
	.history-btn {
		svg {
			position: relative;
			left: -3px;
			width: 26px;
			color: #c2a862;
		}
		&.has-unread {
			&:after {
				top: -10px;
				right: -20px;
				width: 32px;
				height: 32px;
				font-size: 20px;
				padding-bottom: 0px;
			}
		}
	}
}
</style>
