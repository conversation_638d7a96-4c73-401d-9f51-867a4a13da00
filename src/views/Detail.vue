<template>
	<loading-wrap :loading="loading">
		<div class="ticket-bg">
			<div class="ticket-wrapper">
				<div class="content">
					<van-collapse v-model="activeNames" :border="false">
						<!-- 表单信息 -->
						<van-collapse-item :name="'0'" :is-link="false" :border="false">
							<template #title>
								<div class="title" @click="imgRotate(0)">
									<img src="~@/assets/img/collapse.png" :class="[imgState[0]?'open-rotate':'close-rotate']">
									<span>{{ form.category }}</span>
								</div>
							</template>
							<div class="form">
								<div v-for="(value, name, index) in form.filed" :key="index">
									<div class="form-label">
										<div class="icon"></div>
										<span>{{ name }}</span>
									</div>
									<div v-if="Array.isArray(value)" class="form-content">
										<div class="media-form-box">
											<div
												v-if="value.length && value[0].file_type === 'image'"
												class="img-wrap"
											>
												<img
													v-for="(img, index) in value"
													:key="index"
													:src="img.url"
													@click="previewImg(img)"
												/>
											</div>
											<div
												v-if="value.length && value[0].file_type === 'video'"
												class="video-preview"
											>
												<fp-video
													:src="value[0].url"
												></fp-video>
											</div>
										</div>
									</div>
									<div v-else class="form-content">
										<div class="text-form-box" v-html="value"></div>
									</div>
								</div>
							</div>
						</van-collapse-item>
						<!-- 暂时回复展示区 -->
						<van-collapse-item class="ticketdetail-chat-info" name="chatInfo" :is-link="false" :border="false" v-if="form.commu && form.commu.length">
							<template #title>
								<div class="title" @click="chatImg = !chatImg">
								<img src="~@/assets/collapse.png" :class="[chatImg ? 'open-rotate' : 'close-rotate']">
								<span>{{ $t("text_talk_info") }}</span>
								</div>
							</template>
							<div class="ticketdetail-chat-info-content" v-for="(v, k) in form.commu" :key="k">
								<!-- 重开信息 -->
								<div class="reopen-info" v-if="v.op_type === 13">
									<span>{{ $t("text_title_info") }}<span class="title-time">{{ v.created_at }}</span></span>
								</div>
								<div class="chat-item" v-if="v.role === 1">
								<div class="text-form-box remark-textarea chat-box-player" v-html="v.content"></div>
								<div class="avatar chat-player"></div>
								<div class="media-form-box">
									<!-- 图片补充 -->
									<div v-if="v.picture" class="form-labels">
									<div class="icon"></div>
									<span>{{ $t("text_img_add") }}</span>
									</div>
									<div v-if="v.picture" class="img-wrap">
									<img v-for="(item, index) in v.picture.split(',')" :key="index" :src="item"
										@click="previewImg({url: item})" />
									</div>
									<!-- 视频补充 -->
									<div v-if="v.video" class="form-labels">
									<div class="icon"></div>
									<span>{{ $t("text_video_add") }}</span>
									</div>
									<div v-if="v.video" class="video-preview">
									<span v-for="(item, index) in v.video.split(',')" :key="index" :src="item">
										<fp-video :src="item"></fp-video>
									</span>
									</div>
								</div>
								</div>
								<div class="chat-item" v-if="v.role === 2">
								<div class="avatar chat-customer"></div>
								<div class="text-form-box remark-textarea chat-box-customer" v-html="v.content"></div>
								</div>
							</div>
						</van-collapse-item>
						<!-- 客服回复展示区 -->
						<div
							v-for="(reply, index) in form.replenish"
							:key="index"
							class="reply-wrapper"
						>
							<van-collapse-item :name="`${index+1}`" :is-link="false" :border="false">
								<template #title>
									<div class="title" @click="imgRotate(index+1)">
										<img src="~@/assets/img/collapse.png" :class="[imgState[index+1]?'open-rotate':'close-rotate']">
										<span>{{ $t("text_reply") }}<span class="title-time">{{ reply.created_at }}</span></span>
									</div>
								</template>
								<div class="form-content">
									<div style="white-space: pre-wrap;" class="text-form-box remark-textarea" v-html="reply.remark"></div>
								</div>
								<!-- 玩家补填过的信息展示 -->
								<template v-if="reply.op === 7 && reply.fill_content">
									<div class="title reply">
										<img src="~@/assets/img/ft.png">
										<span>{{ $t("text_add_proof") }}</span>
									</div>
									<template v-if="reply.fill_content">
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_txt_add") }}</span>
										</div>
										<div class="form-content">
											<div class="text-form-box">
												{{ reply.fill_content }}
											</div>
										</div>
									</template>
									<template
										v-if="
											reply.files &&
											JSON.parse(reply.files) &&
											JSON.parse(reply.files).find(item => item.file_type === 'image')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_img_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="img-wrap">
													<img
														v-for="(img, index) in getImg(JSON.parse(reply.files))"
														:key="index"
														:src="img.url"
														alt=""
														@click="previewImg(img)"
													/>
												</div>
											</div>
										</div>
									</template>
									<template
										v-if="
											reply.files &&
											JSON.parse(reply.files) &&
											JSON.parse(reply.files).find(item => item.file_type === 'video')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_video_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="video-preview">
													<fp-video
														:src="getVideo(JSON.parse(reply.files))"
													></fp-video>
												</div>
											</div>
										</div>
									</template>
								</template>
							</van-collapse-item>
						</div>
						<!-- 重开信息展示区 -->
						<div
							v-for="(reply, index) in form.reopen"
							:key="index"
							class="reply-wrapper"
						>
							<van-collapse-item :name="`${form.replenish!.length+1+index}`" :is-link="false" :border="false">
								<template #title>
									<div class="title" @click="imgRotate(form.replenish!.length+1+index)">
										<img src="~@/assets/img/collapse.png" :class="[imgState[form.replenish!.length+1+index]?'open-rotate':'close-rotate']">
										<span>{{ $t("text_title_info") }}<span class="title-time">{{ reply.created_at }}</span></span>
									</div>
								</template>
								<template v-if="reply.fill_content">
									<template v-if="reply.fill_content">
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_txt_add") }}</span>
										</div>
										<div class="form-content">
											<div class="text-form-box">
												{{ reply.fill_content }}
											</div>
										</div>
									</template>
									<template
										v-if="
											reply.files &&
											JSON.parse(reply.files) &&
											JSON.parse(reply.files).find(item => item.file_type === 'image')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_img_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="img-wrap">
													<img
														v-for="(img, index) in getImg(JSON.parse(reply.files))"
														:key="index"
														:src="img.url"
														alt=""
														@click="previewImg(img)"
													/>
												</div>
											</div>
										</div>
									</template>
									<template
										v-if="
											reply.files &&
											JSON.parse(reply.files) &&
											JSON.parse(reply.files).find(item => item.file_type === 'video')
										"
									>
										<div class="form-label">
											<div class="icon"></div>
											<span>{{ $t("text_video_add") }}</span>
										</div>
										<div class="form-content">
											<div class="media-form-box">
												<div class="video-preview">
													<fp-video
														:src="getVideo(JSON.parse(reply.files))"
													></fp-video>
												</div>
											</div>
										</div>
									</template>
								</template>
								<!-- 重开处理结果 回复区；重开回复中有：客服回复或者打回补填的表单展示 -->
								<div
									v-for="(item, index) in reply.replenish"
									:key="index"
									class="reply-wrapper"
								>
									<div class="title">
										<span>{{ $t("text_reopen_result") }}<span class="title-time">{{ item.created_at }}</span></span>
									</div>
									<div class="form-content">
										<div style="white-space: pre-wrap;" class="text-form-box remark-textarea" v-html="item.remark"></div>
									</div>
									<!-- 玩家补填过的信息展示 -->
									<template v-if="item.op === 7 && item.fill_content">
										<div class="title reply">
											<img src="~@/assets/img/ft.png">
											<span>{{ $t("text_add_proof") }}</span>
										</div>
										<template v-if="item.fill_content">
											<div class="form-label">
												<div class="icon"></div>
												<span>{{ $t("text_txt_add") }}</span>
											</div>
											<div class="form-content">
												<div class="text-form-box">
													{{ item.fill_content }}
												</div>
											</div>
										</template>
										<template
											v-if="
												item.files &&
												JSON.parse(item.files) &&
												JSON.parse(item.files).find(item => item.file_type === 'image')
											"
										>
											<div class="form-label">
												<div class="icon"></div>
												<span>{{ $t("text_img_add") }}</span>
											</div>
											<div class="form-content">
												<div class="media-form-box">
													<div class="img-wrap">
														<img
															v-for="(img, index) in getImg(JSON.parse(item.files))"
															:key="index"
															:src="img.url"
															alt=""
															@click="previewImg(img)"
														/>
													</div>
												</div>
											</div>
										</template>
										<template
											v-if="
												item.files &&
												JSON.parse(item.files) &&
												JSON.parse(item.files).find(item => item.file_type === 'video')
											"
										>
											<div class="form-label">
												<div class="icon"></div>
												<span>{{ $t("text_video_add") }}</span>
											</div>
											<div class="form-content">
												<div class="media-form-box">
													<div class="video-preview">
														<fp-video
															:src="getVideo(JSON.parse(item.files))"
														></fp-video>
													</div>
												</div>
											</div>
										</template>
									</template>
								</div>
							</van-collapse-item>
						</div>
						<!-- 重开信息填写区 -->
						<van-collapse-item name="reopen" :is-link="false" :border="false" v-if="reopenInfo">
							<template #title>
								<div class="title" @click="reopenImg = !reopenImg">
									<img src="~@/assets/img/collapse.png" :class="[reopenImg?'open-rotate':'close-rotate']">
									<span>{{ $t("text_title_info") }}</span>
								</div>
							</template>
							<reopen-ticket :catIdFromParent="(form.cat_id as number)" @show-success="showSuccess"></reopen-ticket>
						</van-collapse-item>
					</van-collapse>
					<!-- 底部提示区 -->
					<!-- 显示是否解决tips：appraise:后台设置可以评价+ relate_cat:客服回复处理完成+ reopen_disabled:超过重开次数+不支持重开：true+ hideSelf点击未解决按钮后隐藏自己
						否则直接显示下面的提交评价按钮 -->
					<!-- 工单v4.3.1：除了超时关闭单，其它都显示是否解决tip -->
					<div v-if="form.appraise && !form.relate_cat && !hideSelf" class="tips resolve-box">
						<div class="mini-button-left-text">{{ $t('text_problem_resolve') }}</div>
						<div class="mini-button-box">
							<div class="mini-button-yes" @mousedown.stop.prevent="goToAppraise(1)" @touchstart.stop.prevent="goToAppraise(1)">{{ $t('text_button_resolved') }}</div>
							<div class="mini-button-no" @mousedown.stop.prevent="goToNext" @touchstart.stop.prevent="goToNext">{{ $t('text_button_unresolved') }}</div>
						</div>
					</div>
					<div v-if="unResolved" class="tips resolve-box">
						<div class="mini-button-left-text">{{ $t('text_tips_reopen') }}</div>
						<div class="mini-button-box">
							<div class="mini-button-no" @mousedown.stop.prevent="goToAppraise(2)" @touchstart.stop.prevent="goToAppraise(2)">{{ $t('text_button_drop') }}</div>
							<div class="mini-button-yes" @mousedown.stop.prevent="goToReopen" @touchstart.stop.prevent="goToReopen">{{ $t('text_button_reopen') }}</div>
						</div>
					</div>
					<!-- reopen_disabled: 超过重开次数或者不支持重开：true：展示去评价，否则展示重开 -->
					<div v-if="form.appraise && !form.relate_cat && form.reopen_disabled && toEvaluate" @click="spanToAppraise" class="tips historyRecordsFont" v-html="$t('not_resolved_appraise')"></div>
					<div v-if="showSuccessTips && !submit" @click="goToHistory" class="tips historyRecordsFont" v-html="$t('text_tips_success')"></div>

					<div v-if="submit" class="tips">
						<div class="historyRecordsFont" @click="goToHistory" v-html="$t('text_receive_question')"></div>
					</div>
					<div v-if="accept" class="tips">{{ $t("text_resolve_wait") }}</div>
					<div v-if="secondLine" class="tips">{{ $t("text_upgrade_pro") }}</div>
					<div v-if="checked" class="tips">{{ $t("text_upgrade_highpro") }}</div>
					<div v-if="completed" class="tips">
						<span style="margin-right: 5px;">{{ $t("text_fill_close") }}</span>
						<span v-if="count > 0" style="color: #f7bf28;">{{ ' [' + count + 's]' }}</span>
					</div>
					<div v-if="form.evidence" class="evidence-tips" v-html="$t('text_complete_close') + '<span class=time>' + getCompleteTime() + '</span>'"></div>
					<div v-if="form.evidence" class="button-wrapper">
						<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
							<div class="fp-button" @mousedown.stop.prevent="goToComplete" @touchstart.stop.prevent="goToComplete" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("text_fill_data") }}</div>
						</div>
					</div>
					<!-- 显示提交评价按钮：appraise:后台设置可以评价+relate_cat:客服回复处理完成+reopen_disabled:超过重开次数+不支持重开：true
						否则显示上面的是否解决tips -->
					<!-- <div v-if="form.appraise && !form.relate_cat && form.reopen_disabled" class="button-wrapper">
						<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
							<div class="fp-button" @mousedown.stop.prevent="goToAppraise(0)" @touchstart.stop.prevent="goToAppraise(0)" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("btn_appraise") }}</div>
						</div>
					</div> -->
					<div v-if="appraised" class="tips">
						<span style="margin-right: 5px;">{{ $t("text_receive_rate") }}</span>
						<span v-if="count > 0" style="color: #f7bf28;">{{ ' [' + count + 's]' }}</span>
					</div>
					<div v-if="form.relate_cat" class="button-wrapper">
						<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
							<div class="fp-button" @mousedown.stop.prevent="goToQuestion" @touchstart.stop.prevent="goToQuestion" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("btn_push_after_completion") }}</div>
						</div>
					</div>
          <!-- 超时关闭提醒 -->
          <div v-if="form.overtime_reply" class="close-tips">{{ form.overtime_reply }}, {{ $t('text_auto_close_tip') }}: {{ form.overtime }}</div>
					<div v-if="overtimeClose" class="close-tips">{{ $t("text_time_close") }}</div>
					<!-- 老工单C端兼容swift新工单后台，增加多轮对话功能 -->
					<div v-if="!form.done && form.category && form.ticket_sys_type" class="textarea_box">
					<van-field
						v-model="com_content"
						type="textarea"
						rows="2"
						autosize
						maxlength="200"
						show-word-limit
						:placeholder="$t('text_supplement')"
					/>
					<div class="form-labels">
						<div class="icon"></div>
						<span>{{ $t("text_img_add") }}</span>
					</div>
					<img-upload
						:isH5Upload="isWXGame || isFromAI"
						class="form-item"
						@success="uploadImg"
						@remove="removeImg"
					></img-upload>
					<div class="form-labels">
						<div class="icon"></div>
						<span>{{ $t("text_video_add") }}</span>
					</div>
					<video-upload
						:isH5Upload="isWXGame || isFromAI"
						class="form-item"
						@success="uploadVideo"
						@remove="removeVideo"
					></video-upload>
					<div class="button-wrapper">
						<div class="submit-btn" @click="handleChatInfo">{{ $t("text_submit") }}</div>
					</div>
				</div>
				</div>
			</div>
		</div>
	</loading-wrap>
</template>
<script lang="ts">
import { useI18n } from 'vue-i18n'
import { defineComponent, nextTick, ref, reactive, onMounted, toRefs, computed, getCurrentInstance } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from "vue-router"
import { ImagePreview } from 'vant'
import LoadingWrap from '@/components/loadingWrap'
import ReopenTicket from '@/components/ticketComp/ReopenTicket.vue'
import { Toast } from 'vant'
import { ticketDetail, appraiseFeedback, playerSendMsg } from '@/api/tickets'
import { formatDate } from '../utils'
import { browser } from '@/utils'
import { useDebounceFn } from '@vueuse/core'
interface dataT {
	imgList: Array<unknown>,
	videoList: Array<unknown>,
	pushAnimate: boolean,
	ticketId: number,
	from?: unknown,
	form: Record<string, unknown> & {
		filed?: string | Record<string, unknown>,
		replenish?: Array<Record<string, unknown> & {
			files: string,
			created_at: string
		}>,
		reopen?: Array<Record<string, unknown> & {
			files: string,
			created_at: string,
			replenish?: Array<Record<string, unknown> & {
				files: string,
				created_at: string
			}>
		}>,
		commu?:Array<Record<string, unknown> & {
			role: number,
			content: string,
			picture: string,
			video: string
		}>
	},
	timer?: unknown,
	count: number,
	loading: boolean
}
export default defineComponent({
	name: 'Detail',
	components: { LoadingWrap, ReopenTicket }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
// eslint-disable-next-line
const { appContext } = getCurrentInstance() as any
const { state, commit } = useStore()
const userInfo = computed(() => state.userInfo)
const isWXGame = computed(() => userInfo.value && 'openid' in userInfo.value )
const isFromAI = computed(() => userInfo.value && userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai'))
const setLog = appContext.config.globalProperties.$utils.basicLog
const route = useRoute()
const router = useRouter()
const browserIsPC = browser.version.isWindows || false
console.log('Detail browserIsPC', browserIsPC)
const data: dataT = reactive({
	imgList: [],
	videoList: [],
	ticketId: -1,
	form: {},
	count: -1,
	pushAnimate: false,
	loading: true
})
const submit = computed(() => {
	return (
		((data.form.replenish && (data.form.replenish).length === 0) ||
		data.form.replenish === null)&&
		!data.form.done &&
		data.form.transfer === 1
	)
})
const accept = computed(() => {
	return (
		data.form.replenish &&
		data.form.replenish.length === 0 &&
		!data.form.done &&
		data.form.transfer === 2
	)
})
const completed = computed(() => {
	return (
		data.form.replenish &&
		data.form.replenish.length !== 0 &&
		data.form.replenish[data.form.replenish.length - 1].op === 7 &&
		data.form.replenish[data.form.replenish.length - 1].fill_content &&
		!data.form.done
	)
})
const appraised = computed(() => {
	return data.form.done && !data.form.appraise && data.form.closed === 1 && data.form.csi !== 0
})
const secondLine = computed(() => {
	return (
		!data.form.done &&
		!data.form.evidence &&
		!data.form.appraise &&
		data.form.transfer === 3 &&
		!completed.value &&
		!appraised.value
	)
})
const checked = computed(() => {
	return (
		!data.form.done &&
		!data.form.evidence &&
		!data.form.appraise &&
		data.form.transfer === 4 &&
		!completed.value &&
		!appraised.value
	)
})
const overtimeClose = computed(() => {
	return data.form.done && data.form.closed === 3
})

// 处理结果页面加载打点
setLog({
	button: 0,
	action: 'loading',
	result: 1,
	position: 'wo_result'
})

onMounted(() => {
	data.from = route.query.from
	if (route.query.ticketId) {
		data.ticketId = +route.query.ticketId
		getTicketDetail()
	} else {
		console.log('缺少重要参数')
	}
	// // H5兼容ios12 监听所有input blur
	// const inputsElement = [...document.getElementsByTagName('input')]
	// inputsElement.forEach(element => {
	// 	if (element) {
	// 		element.onblur = () => {
	// 			setTimeout(() => {
	// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
	// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
	// 			}, 300)
	// 		}
	// 	}
	// })
})
// 关闭webview
const closeSDK = () => {
	appContext.config.globalProperties.$utils.jsBridge('close')
}
const getTicketDetail = () => {
	ticketDetail({
		ticket_id: data.ticketId
	}).then((res: dataT["form"]) => {
		data.loading = false
		data.form = res
		data.form.filed = JSON.parse(data.form.filed as string)
		data.form.replenish?.forEach(() => imgState.push(false))
		// 判断是否展开对话信息；有对话信息则展开 + 展开除了对话信息外的最后一项
		if (data.form.commu?.length) {
			activeNames.value.push('chatInfo')
			chatImg.value = true
		}
		// 计算最后一项的:name值，进入页面只展开最后一项
		const flag: number = (data.form.replenish as []).length + (data.form.reopen as []).length
		const flagStr: string = flag.toString()
		activeNames.value.push(flagStr)
		imgState[flag] = true

		// 数据加载完成后，判断data.form.commu?.length是否大于0，大于0则滚动到最后一条聊天记录
		nextTick(() => {
			if (data.form.commu?.length) {
				const scrollToLastChat = () => {
					const contentBodyBox = document.querySelector('.ticket-bg') as HTMLElement
					const chatInfoContainer = document.querySelector('.ticketdetail-chat-info') as HTMLElement

						// 计算内容区域的可视高度
					const contentVisibleHeight = contentBodyBox.clientHeight
					let scrollPosition = 0

					if (chatInfoContainer && data.form.commu?.length) {
						// 获取聊天区域的顶部位置
						const chatInfoTop = chatInfoContainer.offsetTop
						// 获取聊天区域的高度
						const chatInfoHeight = chatInfoContainer.offsetHeight

						console.log('聊天区域 - chatInfoTop:', chatInfoTop)
						console.log('聊天区域 - contentVisibleHeight:', contentVisibleHeight)
						console.log('聊天区域 - chatInfoHeight:', chatInfoHeight)

						// 计算滚动位置：将聊天区域放在可视区域底部
						scrollPosition = chatInfoTop - contentVisibleHeight + chatInfoHeight
					}

					console.log('最终 scrollPosition:', scrollPosition)

					// 滚动到计算出的位置
					contentBodyBox.scrollTo({
						top: Math.max(0, scrollPosition),
						behavior: 'smooth'
					})
				}

				// 立即尝试滚动
				scrollToLastChat()
			}
		})
	}, (res: string) => {
		data.loading = false
		Toast(res)
	}).catch((err: string) => {
		data.loading = false
		Toast(err)
	})
	if (data.from === 'complete' || data.from === 'appraise')  {
		const TIME_COUNT = 3
		if (!data.timer) {
			data.count = TIME_COUNT
			data.timer = setInterval(() => {
				if (data.count > 0 && data.count <= TIME_COUNT) {
					data.count--
				} else {
					clearInterval(data.timer as number)
					data.timer = null
					// 关闭H5(WX小游戏不关闭)
					if (userInfo.value.openid === undefined || userInfo.value.openid === null) {
						if (userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai')) {
							// 兼容AI SDK 关闭webview逻辑
							window.location.href = 'fpcapi://openai?method=redirect&scene=1&target=close'
						} else {
							closeSDK()
						}
					}
				}
			}, 1000)
		}
	}
}
const previewImg = (img: Record<string, string>) => {
	ImagePreview({
		images: [img.url],
		closeable: false,
		showIndex: false,
		teleport: 'body',
	})
}
const getImg = (fileList: Array<Record<string, string>>) => {
	const imgList = fileList.filter(file => file.file_type === 'image')
	return imgList
}
const getVideo = (fileList: Array<Record<string, unknown>>) => {
	const video = fileList.find(file => file.file_type === 'video')
	return video?.url
}
const goToComplete = () => {
	// 跳转补填按钮打点
	setLog({
		button: 1,
		action: 'click',
		result: 1,
		position: 'need_fullfill'
	})
	data.pushAnimate = true
	router.push({
		path: '/complete',
		query: {
			ticketId: data.ticketId
		}
	})
}
const goToAppraise = (val: number) => {
	if (val) {
		// 点击 已解决、算了 按钮打点
		setLog({
			position: val === 1 ? 'button_resolved' : 'button_drop',
			ticket_id: data.ticketId, // 关联的工单id
			click_time: new Date().getTime(), // 什么时间
			action: 'click', // 点击
			cat_id: data.form.cat_id // 问题分类id
		})
		if (val === 1) {
			// 后端接口统计已解决 未解决点击，报表用
			appraiseFeedback({
				ticket_id: data.ticketId,
				resolved: 1 // 是否解决 1：已解决 2：未解决
			}).catch((err: string) => { Toast(err) })
		}
	} else {
		// 点击评价按钮打点(历史逻辑：这里通用版有 定制版没有打点)
		setLog({
			button: 1,
			action: 'click',
			result: 1,
			position: 'wo_result'
		})
		data.pushAnimate = true
	}
	router.push({
		path: '/appraise',
		query: {
			ticketId: data.ticketId
		}
	})
}
const chatImg = ref<boolean>(false)
const reopenImg = ref<boolean>(false)
const showSuccessTips = ref<boolean>(false)
const showSuccess = () => {
	getTicketDetail()
	nextTick(() => {
		showSuccessTips.value = true
		reopenInfo.value = false
	})
}
const activeNames = ref<string[]>([])
let imgState = reactive([false])
const imgRotate = (i: number) => {
	imgState[i] = !imgState[i]
}
// 未解决按钮
let unResolved = ref<boolean>(false)
let toEvaluate = ref<boolean>(false) // 展示抱歉未解决，去评价tip
let hideSelf = ref<boolean>(false)
const goToNext = () => {
	// 点击未解决按钮打点
	setLog({
		position: 'button_unresolved',
		ticket_id: data.ticketId,
		click_time: new Date().getTime(),
		action: 'click',
		cat_id: data.form.cat_id
	})
	appraiseFeedback({
		ticket_id: data.ticketId,
		resolved: 2 // 是否解决 1：已解决 2：未解决
	}).catch((err: string) => { Toast(err) })
	// reopen_disabled: 超过重开次数或者不支持重开：true：展示去评价，否则展示重开
	data.form.reopen_disabled ? toEvaluate.value = true : unResolved.value = true
	hideSelf.value = true
}
// 重开按钮
const reopenInfo = ref<boolean>(false)
const goToReopen = () => {
	// 点击重开按钮打点
	setLog({
		position: 'button_reopen',
		ticket_id: data.ticketId, // 关联的工单id
		click_time: new Date().getTime(), // 什么时间
		action: 'click', // 点击
		cat_id: data.form.cat_id // 问题分类id
	})
	unResolved.value = false
	reopenInfo.value = true
	activeNames.value = ['reopen'] // 所有模块都收起，只展开重开填写模块
	reopenImg.value = true
	imgState.length = 0
}
const getCompleteTime = () => {
	if (data.form.evidence && data.form.replenish) {
		const length = data.form.replenish.length
		const replyTime = data.form.replenish[length - 1].created_at
		const endTime = new Date(replyTime.replace(/-/g, '/')).getTime() + 24 * 60 * 60 * 1000
		return formatDate(endTime)
	} else {
		return ''
	}
}
// 提交工单跳转工单页
const goToQuestion = () => {
	data.pushAnimate = true
	router.push({
		path: '/tickets',
		query: {
			cat_id: data.form.relate_cat as number,
			from_ticket_id: data.form.ticket_id as number
		}
	})
}
const goToHistory = (e: Event) => {
	if((e.target as HTMLElement).localName === 'span') {
		router.push({
			path: '/history'
		})
	}
}
const spanToAppraise = (e: Event) => {
	if((e.target as HTMLElement).localName === 'span') {
		// 点击去评价按钮打点
		setLog({
			position: 'button_goto_appraise',
			ticket_id: data.ticketId, // 关联的工单id
			click_time: new Date().getTime(), // 什么时间
			action: 'click', // 点击
			cat_id: data.form.cat_id // 问题分类id
		})
		router.push({
			path: '/appraise',
			query: {
				ticketId: data.ticketId
			}
		})
	}
}
const com_content = ref<string>('')
// 上传图片
const uploadImg = (path: Array<string>) => {
	data.imgList.push(...path)
}
// 删除图片
const removeImg = (index: number) => {
	data.imgList.splice(index, 1)
}
// 上传视频
const uploadVideo = (path: Array<string>) => {
	data.videoList.push(...path)
}
// 删除视频
const removeVideo = () => {
	data.videoList.splice(0, 1)
}
const isShowTips = ref<boolean>(false)
const handleChatInfo = useDebounceFn(() => {
	if (!com_content.value) {
    Toast($t('text_cant_empty'))
    return
  }
	const picArr = data.imgList.map(item => item.url).join(',')
	const videoArr = data.videoList.map(item => item.url).join(',')
	commit('setLoadingCount', 1)
	playerSendMsg({
		ticket_id: data.ticketId,
		content: com_content.value,
		picture: picArr,
		video: videoArr
	}).then(() => {
		getTicketDetail()
		com_content.value = ''
		data.imgList.length = 0
		data.videoList.length = 0
		location.reload()
		isShowTips.value = true
	}).catch((err: string) => {
		Toast(err)
	}).finally(() => commit('setLoadingCount', 0))
}, 500)

const { form, count, pushAnimate, loading } = toRefs(data)
</script>

<style lang="scss" scoped>
// 修改vant Collapse折叠面板的默认样式
::v-deep .van-collapse-item .van-collapse-item__wrapper {
	overflow: inherit;
}
::v-deep .van-collapse-item__title,
::v-deep .van-collapse-item__content {
	all: inherit;
}
// 图标的展开关闭旋转
.close-rotate {
	transition: all 0.5s;
}
.open-rotate {
	transform: rotate(-180deg);
	transition: all 0.5s;
}
.content {
	word-wrap: break-word;
	word-break: break-all;
	overflow: hidden;
	font-size: 37.352px;
	.form-content {
		padding-bottom: 53.36px;
		overflow: hidden;
	}
	.text-form-box {
		background-color: rgba(103, 89, 58, 0.3);
		border: 2px solid #817042;
		padding: 16px 26.68px;
		width: 97.7%;
		margin-left: 2.1% ;
		box-sizing: border-box;
		color: #E9C86F;
		font-size: 34.684px;
		// text-shadow: 0px 1px 2px rgba(253, 239, 233, 0.4), 1px 0px 2px rgba(253, 239, 233, 0.4);
	}
	.remark-textarea {
		& ::v-deep img {
			width: 100%;
			height: auto;
		}
	}
	.media-form-box {
		width: 97.7%;
		margin-left: 2.1% ;
	}
	.title {
		margin: 0px 21.344px 26.68px 0px;
		box-sizing: border-box;
		width: 100%;
		padding: 8Px 0px 10Px 8Px;
		height: auto;
		overflow: hidden;
		line-height: 58.696px;
    color: #cecfc9;
    font-size: 37.352px;
		border-radius: 0px 2Px 2Px 0px;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
		text-shadow: 0px 1px 2px rgba(76, 79, 88, 0.6), 1px 1px 2px rgba(76, 79, 88, 0.6), 0px 0px 2px rgba(76, 79, 88, 0.6), 1px 0px 2px rgba(76, 79, 88, 0.6);
		border-left: 4Px solid rgba(245, 193, 51, 0.7);

		img {
			display: block;
			height: 46.69px;
			width: 46.69px;
			float: left;
			opacity: 0.8;
			margin: 6.67px 8px 0px 0px;
		}
		.title-time {
			font-size: 26.68px;
			opacity: 0.6;
			margin-left: 16px;
		}
	}
	.evidence-tips {
		width: 80%;
		text-align: left;
		margin: 24.012px auto;
	}
	.close-tips {
		width: 80%;
		text-align: center;
		margin: 53.36px auto;
	}
	.tips {
		width: 80%;
		margin: 53.36px auto;
		text-align: center;
		bottom: 66.7px;
		word-break: normal;
	}
	.reply-wrapper {
		// margin-top: 40px;
		.title {
			margin-bottom: 40px;
		}
	}
	// .reply {
	// 	margin-top: 40px;
	// }
}
.textarea_box {
	::v-deep(.van-cell) {
		background: rgba(5, 5, 6, 0.26);
		border: 1px solid #182033;
		padding: 10px 20px;
	}
	::v-deep(.van-cell::after) {
		border-bottom: none;
	}
	::v-deep(.van-field__control) {
		font-size: 22px;
		line-height: 30px;
		color: #9ABBCD;
	}
	::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #65889B;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #65889B;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #65889B;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #65889B;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #65889B;
		}
	}
	.form-labels {
		display: flex;
		align-items: center;
		margin: 10px 0;
		&.required {
			&::after {
			content: '*';
			color: #ee0a24;
			margin: -4px 0 0 4px;
			}
		}
		.icon {
			width: 24px;
			height: 24px;
			margin-right: 8px;
			background: url('../assets/question/icon_paragraph.png') no-repeat;
			background-size: cover;
		}
	}
	.button-wrapper {
		text-align: center;
		margin-top: 10px;
		display: flex;
		justify-content: center;
		.submit-btn {
			width: 405px;
			height: 80px;
			font-size: 32px;
			border-image: url('../assets/question/button.png') 5 0 fill;
			color: #f8eacb;
			line-height: 80px;
		}
	}
}
// 对话信息
.chat-item {
	position: relative;
	margin: 25px 0;
	.avatar {
		position: absolute;
		top: 0;
		width: 60px;
		height: 60px;
		border-radius: 50%;
	}
	.chat-customer {
		background-image: url('~@/assets/img/soc/icon-gpt.png');
		background-size: cover;
	}
	.chat-player {
		right: 0;
		background-image: url('~@/assets/img/soc/icon-avatar.png');
		background-size: cover;
	}
	.chat-box-customer {
		min-height: 60px;
		width: 89%;
		margin-left: auto;
		::v-deep img {
			max-width: 30%;
		}
	}
	.chat-box-player {
		min-height: 60px;
		width: 87%;
		margin-right: auto;
	}
	& ::v-deep a {
    text-decoration: underline;
    color: #4e6ef2;
  }
}
// 修改上传图片、视频组件样式
::v-deep .img-wrap .img-upload,
::v-deep .video-wrap .video-upload,
::v-deep .pc-box {
	width: 150px;
	height: 150px;
	border: 1px solid #445960;
	background: rgba(5, 5, 6, 0.26) url('~@/assets/img/soc/plus.webp') no-repeat center;
	background-size: 70px;
}
@media all and (orientation : portrait) {
  .textarea_box {
    ::v-deep(.van-cell) {
      width: 96.5%;
      margin: 0 auto;
      background: #f6f6f6 !important;
      border: 0px;
      padding: 10px 20px;
    }
    ::v-deep(.van-cell::after) {
      border-bottom: none;
    }
    ::v-deep(.van-field__control) {
      font-size: 44px;
      line-height: 80px;
      color: #000;
    }
    ::v-deep(.van-field__body) {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #787878;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #787878;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #787878;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #787878;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #787878;
      }
    }
    .form-item {
      margin-left: 0.3rem;

    }
    .form-labels {
      margin-top: 0.2rem;
    }
  }
	.content {
		font-size: 44px;
		.text-form-box {
			padding: 16px 20px;
			width: 96.5%;
			margin-left: 3.3% ;
			font-size: 44px;
		}
		.media-form-box {
			width: 96.5%;
			margin-left: 3.3% ;
		}
		.title {
			height: auto;
			line-height: 80px;
			font-size: 50px;

			img {
				height: 60px;
				width: 60px;
				margin: 8.5px 6Px 0px 0px;
			}
			.title-time {
				font-size: 30px;
				margin-left: 16px;
			}
		}
		.reply-wrapper {
			// margin-top: 40px;
			.title {
				margin-bottom: 30px;
			}
		}
	}
}
.img-wrap {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
	img {
		width: 266.8px;
		height: 266.8px;
		object-fit: cover;
		display: block;
		margin: 0 21.344px 21.344px 0;
	}
}
.video-preview {
	width: 40vw;
	// height: 40vh;
	position: relative;
	.video {
		width: 100%;
		height: 100%;
		display: block;
	}
}

.reopen-info {
	color: #D4AD5B;
	margin-left: 2%;
	> span {
		display: flex;
		align-items: center;
		gap: 10px;
	}
}
</style>
<style lang="scss">
.historyRecordsFont {
	margin-right: 5px;
	span {
		color: #ffffff;
		cursor: pointer;
		text-decoration: underline;
		text-underline-offset: 0.03rem;
	}
}
</style>
