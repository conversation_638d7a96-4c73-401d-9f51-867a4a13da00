<template>
  <div class="q-box">
    <van-loading vertical v-if="loading" color="#f06b29" style="margin-top: 80px;"></van-loading>
    <div class="q-container" v-else>
      <div class="submit-success" v-if='submitMark'>
        <span>{{ $t('text_qn_submit_success') }}</span>
      </div>
      <div v-else>
        <div class="q-title">
          <!-- 标题 -->
          <span class="h-title">{{ qnTemplateData.survey_titles }}</span>
        </div>
        <div class="lang-box">
          <van-field
            class="lang-picker"
            input-align="center"
            v-model="qnLang.name"
            is-link
            readonly
            @click="showPicker = true"
          />
          <van-popup v-model:show="showPicker" position="bottom">
            <van-picker
              show-toolbar
              :columns="langList"
              :columns-field-names="{text: 'name'}"
              @cancel="showPicker = false"
              @confirm="onConfirm"
            >
              <template #confirm>
                {{ $t('btn_confirm') }}
              </template>
              <template #cancel>
                {{ $t('btn_cancel') }}
              </template>
            </van-picker>
          </van-popup>
        </div>

        <div class="q-content">
          <van-form ref="formRef">
            <!-- 推送文案 -->
            <div class="q-custonize-dsc">{{ qnTemplateData.push_contents }}</div>
            <div class="dotted-line"></div>
            <!-- 产品题 -->
            <div class="q-body" v-if='qnTemplateData.is_show_product'>
              <span class="q-pub q-des">{{ qnTemplateData.product_questions }}</span>
              <span class="q-tips">「{{ $t('text_qn_tips') }}」</span>
              <div class="q-rate">
                <van-rate
                  v-model="form.product_rating"
                  :size="25"
                  color="#ffd21e"
                  void-icon="star"
                  void-color="#eee"
                  @change="(val) => handleRateVal(val, 'product_rating')"
                />
              </div>
              <!-- 1~3星不满意时，追问 -->
              <div class="q-probe" v-if="form.product_rating < 4 && form.product_rating > 0">
                <span class="q-pub q-des">{{ qnTemplateData.reasons }}</span>
                <van-cell-group inset>
                  <van-field v-model="form.product_answer" :placeholder="$t('text_please_enter')" rows="1" autosize type="textarea" :rules="[{
                    required: true,
                    message: $t('text_please_enter')
                  }]" />
                </van-cell-group>
              </div>
            </div>
            <!-- 服务题 -->
            <div class="q-body" v-if='qnTemplateData.is_show_product'>
              <span class="q-pub q-des">{{ qnTemplateData.service_questions }}</span>
              <span class="q-tips">「{{ $t('text_qn_tips') }}」</span>
              <div class="q-rate">
                <van-rate
                  v-model="form.service_rating"
                  :size="25"
                  color="#ffd21e"
                  void-icon="star"
                  void-color="#eee"
                  @change="(val) => handleRateVal(val, 'service_rating')"
                />
              </div>
              <!-- 1~3星不满意时，追问 -->
              <div class="q-probe" v-if="form.service_rating < 4 && form.service_rating > 0">
                <span class="q-pub q-des">{{ qnTemplateData.reasons }}</span>
                <van-cell-group inset>
                  <van-field v-model="form.service_answer" :placeholder="$t('text_please_enter')" rows="1" autosize type="textarea" :rules="[{
                    required: true,
                    message: $t('text_please_enter')
                  }]" />
                </van-cell-group>
              </div>
            </div>
            <!-- UID -->
            <div class="q-body" v-if='qnTemplateData.need_uid'>
              <span class="q-pub q-des">{{ $t('text_uid_tip') }}</span>
              <div class="q-probe">
                <van-cell-group inset>
                  <van-field v-model="form.uid" type="digit" :placeholder="$t('text_please_enter')" />
                </van-cell-group>
              </div>
            </div>
          </van-form>
        </div>
        <div class="q-submit">
          <van-button type="primary" block round :disabled="!qnTemplateData.can_submit || submitMark" size="small" @click="submitHandle">{{ $t('text_submit') }}</van-button>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Toast } from 'vant'
import { getEnumConfig, getQuestionnaireTemplate, submitQuestionnaire } from '@/api/questionnaire'
export default defineComponent({
  name: 'QuestionNaire',
})
interface Form {
  token: string
  ts_rank: number //时间戳
  language: string
  uid?: number
  product_rating: number //产品评分
  service_rating: number //服务评分
  product_answer: string //产品追问回复
  service_answer: string //服务追问回复
}
</script>
<script lang="ts" setup>
const { t: $t, locale } = useI18n()
const qnLang = ref({ name: '', code: '' })
const showPicker = ref(false)
const loading = ref(false)
const langList = ref([])
interface QnTemplateData {
  survey_titles: string
  push_contents: string
  is_show_product: boolean
  is_show_service: boolean
  service_questions: string
  product_questions: string
  reasons: string
  can_submit: boolean
  need_uid: boolean
}

const qnTemplateData = ref<QnTemplateData>({
  survey_titles: '',
  push_contents: '',
  is_show_product: false,
  is_show_service: false,
  service_questions: '',
  product_questions: '',
  reasons: '',
  can_submit: false,
  need_uid: false
})

const form = reactive<Form>({
  token: '',
  ts_rank: Date.now(),
  language: '',
  uid: undefined,
  product_rating: 0,
  service_rating: 0,
  product_answer: '',
  service_answer: ''
})
const formRef = ref()
const handleRateVal = (val: number, rateType: 'product_rating' | 'service_rating') => {
  form[rateType] = val
}
// 选择语言
const onConfirm = (val: any) => {
  qnLang.value = val
  showPicker.value = false
  locale.value = qnLang.value.code
  getTplHandle()
}
// 获取问卷模板
const getTplHandle = () => {
  loading.value = true
  getQuestionnaireTemplate({ lang: qnLang.value.code }).then((res: any) => {
    qnTemplateData.value = res
  }, (err: any) => {
    Toast.fail(err)
  }).finally(() => {
    loading.value = false
  })
}

const submitMark = ref(false)

// 提交问卷
const submitHandle = () => {
  if (form.product_rating === 0 || form.service_rating === 0) {
    Toast.fail($t('text_please_rate'))
    return
  }
  formRef.value && formRef.value.validate()
  .then(() => {
    form.ts_rank = Date.now()
    form.uid = form.uid ? Number(form.uid) : 0
    submitQuestionnaire(form).then((res: any) => {
      Toast.success($t('text_success'))
      submitMark.value = true
    }, (err: any) => {
      Toast.fail(err)
    })
  }).catch((err: string) => {
    console.log('submitHandle err', err)
  })
}

onMounted(() => {
  loading.value = true
  getEnumConfig({}).then((res: any) => {
    langList.value = res.langs
    qnLang.value = langList.value.find((item: any) => item.code === navigator.language.toLocaleLowerCase()) || langList.value[0]
    locale.value = qnLang.value.code
    getTplHandle()
  }, (err: any) => {
    Toast.fail(err)
  })
})
</script>

<style lang="scss" scoped>
.submit-success {
  font-size: 50px;
  text-align: center;
  font-weight: bold;
  position:absolute;
  width: 100%;
  top:48%;
  left:50%;
  -webkit-transform: translate(-50%,-50%);
  -moz-transform: translate(-50%,-50%);
  transform: translate(-50%,-50%);
}
.lang-box {
  overflow: hidden;
  &:deep(.van-field__control) {
    font-size: 26px;
  }
}
.lang-picker {
  float: right;
  border: 1Px solid #c0c4cc;
  border-radius: 5Px;
  width: 200px;
  padding: 5px;
  .van-field__control {
    text-align: center;
  }
}
.q-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #ebedf0;
  padding-left: 18%;
  padding-right: 18%;

  .q-container {
    background: #fff;
    min-height: 100%;
    padding: 0 70px;
    position: relative;

    .q-title {
      background-color: rgba(255, 255, 255, 0.9);
      padding: 48px 32px 34px;
      width: 100%;
      color: #1ea0fa;
      text-align: center;

      .h-title {
        font-size: 38px;
        line-height: 45px;
        font-weight: bold;
        margin: 0;
        padding: 0;
      }
    }
  }

  .q-content {
    padding: 0 21px;
    .q-body {
      padding: 20px 0px 40px;
      border-bottom: 1Px dashed #c2c2c2;
    }
    //最后一个不要下划线
    .q-body:last-child {
      border-bottom: none;
    }

    .q-custonize-dsc {
      word-break: break-all;
      font-size: 28px;
      color: #666;
      text-indent: 37px;
      line-height: 36px;
      margin: 27px 0 12px;
    }
    .dotted-line {
      border-top: 1px solid #c2c2c2;
    }
    .q-pub {
      display: block;
      margin-top: 12px;
      &:before {
        margin-right: 10px;
        color: red;
        content: "*";
        display: inline;
      }
    }
    .q-tips {
      display: block;
      color: #666;
      font-size: 26px;
      padding: 16px 0;
    }
    .q-des {
      font-size: 28px;
      font-weight: bold;
      line-height: 38px;
    }
    .q-probe {
      margin-top: 15px;
      &:deep(.van-field__control) {
        height: 45px;
        border-radius: 5px;
        padding: 0 15px;
        border: 1px solid #c0c4cc;
      }
      &:deep(.van-cell) {
        padding: 0px;
        margin-top: 10px;
      }
    }
  }
  .q-submit {
    padding: 60px 0 32px;
    width: 100%;
    text-align: center;
  }
}
@media all and (orientation : portrait) {
  .submit-success {
    font-size: 0.6rem;
  }
  .lang-picker {
    width: 2.6rem;
    &:deep(.van-field__control) {
      font-size: 0.36rem;
    }
  }
	.q-box {
    padding: 0 5%;
    .q-container {
      .q-title {
        padding: 48px 32px 34px;
        .h-title {
          font-size: 63px;
          line-height: 65px;
        }
      }
    }
    .q-content {
      padding: 0 21px;
      .q-body {
        padding: 20px 0px 70px;
      }
      .q-custonize-dsc {
        font-size: 48px;
        text-indent: 57px;
        line-height: 56px;
        margin: 47px 0 30px;
      }

      .dotted-line {
        margin-bottom: 38px;
      }

      .q-pub {
        margin-top: 0.3rem;
        &:before {
          margin-right: 10px;
        }
      }

      .q-tips {
        font-size: 36px;
        padding-bottom: 26px;
      }

      .q-des {
        font-size: 48px;
        font-weight: bold;
        line-height: 60px;
      }
      .q-probe {
        margin-top: 15px;
        &:deep(.van-field__control) {
          height: 145px;
          line-height: 145px;
          border-radius: 10px;
          padding: 0 25px;
          border: 1px solid #c0c4cc;
        }
      }
    }
    .q-submit {
      padding: 60px 0 32px;
    }
  }
}
</style>
