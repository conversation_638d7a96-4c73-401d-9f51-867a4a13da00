<template>
	<PcWrapper>
		<div id="s-container">
			<div class="pc_width">
				<div id="s-wrap">
					<component
						v-for="(v,k) in msgList"
						:key="k" :is="v.msgType"
						:msgItem="v"
						@itemClick="compClickHandle"
						@dislike="selectDislike"
						@dislikeSuccess="dislikeSuccess"
					></component>
				</div>
			</div>
		</div>
		<div class="back_top" @click="backTop" v-show="toTopShow"></div>
		<!-- 输入框 -->
		<div class="input_box" v-if="loadingCount === -1">
			<div class="pc_width">
				<div class="input_item">
					<input type="text" v-model.trim="ask" :placeholder="$t('text_placeholder_issues')">
				</div>
				<div class="smart_btn" @click="askHandle">
					<auto-font-size :text="$t('btn_send')"></auto-font-size>
				</div>
			</div>
		</div>
	</PcWrapper>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch, nextTick, onMounted, onActivated, onDeactivated, getCurrentInstance, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { getAnswer, hotCard, getCards, dislikeOpt } from '@/api/smart'
import { Toast } from 'vant'
import smartComps from './components/smartComp'
interface msgT {
	msgType: string,
	content?: string | Record<string, unknown> | Array<Record<string, unknown>>,
	ai_type?: number,
	isFirst?: boolean,
	needEval?: boolean,
	question_data?: Record<string, unknown> & { question_id: number },
	reasonList?: Array<Record<string, unknown>>,
	title?: string
}
interface dataT {
	ask: string,
	msgList: Array<msgT>,
	isRequesting: boolean,
	unmatchNum: number,
	fuzzyNum: number,
	lastScrollTop: number,
	toTopShow: boolean
}
interface answerT {
	match_type: number,
	ai_type: number,
	invalid?: number,
	more_list?: Array<Record<string, unknown>>,
	answer_details: Array<Record<string, unknown>>
}
type questionDataT = Record<string, unknown> & {
	question_id: number,
	answer_rich_show_cat: Array<number>
}
export default defineComponent({
	name: 'Smart',
	components: {
		...smartComps
	}
})
</script>
<script lang="ts" setup>
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const { state } = useStore()
const router = useRouter()
const { t: $t } = useI18n()
const userInfo = computed(() => state.userInfo)
const loadingCount = computed(() => state.loadingCount)
const data: dataT = reactive({
	ask: '',
	msgList: [],
	isRequesting: false,
	unmatchNum: 0, // 未匹配次数
	fuzzyNum: 0, // 模糊匹配后选择“以上都不是”次数
	lastScrollTop: 0, // 记录滚动位置
	toTopShow: false
})
// 初始化页面时添加滚动监听记录滚动位置
nextTick(() => {
	const container: HTMLElement | null = document.getElementById('s-container')
	if (container) {
		container.onscroll = () => {
			data.lastScrollTop = container.scrollTop
		}
	}
})
// 缓存页面激活时自动滚动到原有位置
onActivated(() => {
	const container: HTMLElement | null = document.getElementById('s-container')
	if (container) {
		container.scrollTop = data.lastScrollTop
	}
	window.addEventListener('scroll', scrollTopShow, true)
})
onDeactivated(() => {
	window.removeEventListener('scroll', scrollTopShow, true)
})
watch(
	() => data.msgList,
	() => {
		nextTick(() => {
			if (data.msgList.length < 2) { return }
			updataScroll()
		})
	},
	{ deep: true }
)
// 页面加载打点
setLog({
	button: 0,
	action: 'loading',
	result: 1,
	position: 'homepage'
})
onMounted(() => {
	// 获取热门大卡ID
	hotCard({}).then((res: Record<string, number>) => {
		if (res.card_id !== -1) {
			getCardInfo(res.card_id, true)
		}
	})
})
// 对话滚动效果
const updataScroll = (): void => {
	const container = document.getElementById('s-container')
	const s_wrapper = document.getElementById('s-wrap')
	if (container === null || s_wrapper === null) return
	const containerH = s_wrapper.scrollHeight
	container.scrollTop = containerH
}
// 卡片、List组件击事件
const compClickHandle = (item: Record<string, unknown>) => {
	const itemText = item.card_response_text || item.question_desc
	// 统计模糊匹配次数，1次/2次
	if (item.answer_type === 100) {
		data.fuzzyNum = data.fuzzyNum > 1 ? 1 : data.fuzzyNum + 1
		// 统计模糊匹配情况下，点击“以上都不是”打点
		setLog({
			position: 'ask',
			action: 'fuzzy_unmatch',
			input: item.real_question_desc,
			fuzzy_num: data.fuzzyNum
		})
	} else { // 排除“以上都不是”的情况下的点击时间的打点
		setLog({
			button: 1,
			action: 'click',
			result: 1,
			position: 'ask',
			input: itemText,
			question_id: new Date().getTime() + '' + userInfo.value.uuid
		})
	}
	sendQuestion(itemText as string, 2, item.answer_type === 100)
}
// 获取卡片信息
const getCardInfo = (card_id: number, isfirst?: boolean) => {
	getCards({
		card_id,
		with_ann_flag: isfirst,
		with_faql_flag: isfirst
	}).then((res: Record<string, unknown>) => {
		// if (!isfirst) {
		// 	data.msgList.pop()
		// }
		data.msgList.push({
			msgType: 'Card',
			content: res,
			isFirst: isfirst
		})
	}).catch((err: string) => {
		console.error('getCardInfo error', err)
	})
}
// 发送问题请求答案
const sendQuestion = (question: string, refer: number, fuzzyMark?: boolean) => {
	// 正在等待问题回馈时不可问新的问题
	if (data.isRequesting) return
	data.isRequesting = true
	// 添加玩家提问内容
	data.msgList.push({
		msgType: 'Question',
		content: question
	})
	// 添加loading动画
	data.msgList.push({
		msgType: 'Loading'
	})
	const params = {
		question_desc: question,
		question_refer: refer,
		fuzzy_num: fuzzyMark ? data.fuzzyNum : 0 // 模糊匹配选择“以上都不是”计数,非“以上都不是点击”该参数必须为0
	}
	// 请求问题答案
	getAnswer(params).then((res: answerT) => {
		data.isRequesting = false
		data.msgList.pop()
		if (res.match_type === 1) { // 精准匹配
			// 精准匹配情况下answer_details后端保证只返回length为1的数组，统一格式，所以固定取[0]
			const answerData = res.answer_details[0]
			// 精准匹配细分答案类型处理 1:富文本; 2:自助查询; 3:固定卡片; 4:工单类型; 100:“以上都不是”返回文案
			if (answerData.answer_type === 1) {
				data.msgList.push({
					msgType: 'RichText',
					needEval: answerData.answer_rich_show_eval === 1 || answerData.answer_rich_show_eval === 3, // 后台配置 1展示 3展示new 则展示评价
					content: answerData,
					ai_type: res.ai_type
				})
				// 逻辑细分【无效提问 + 无效提问的模糊情况 + chatGPT精准附带模糊情况】
				if (res.invalid === 1) {
					// 无效提问
					setLog({
						ai_type: res.ai_type,
						button: 0,
						action: 'loading',
						position: 'reply',
						question_id: new Date().getTime() + '' + userInfo.value.uuid,
						is_replied: 2, // 1为有返回答案，0为无返回答案，2为无效提问
						reply_type: 5, // 0为富文本，1为卡片，2为无返回答案时默认值，3为自主查询，4为模糊匹配，5为无效提问
						result: 1,
						word: question ? question : '', // 提问的内容
					})
					if (res.more_list && res.more_list.length > 0) {
						data.msgList.push({
							msgType: 'List',
							content: res.more_list,
							title: $t('text_more_advice')
						})
					}
				} else {
					// 有效提问，精准匹配时需要添加 模糊匹配建议内容
					if (res.more_list && res.more_list.length > 0) {
						setLog({
							ai_type: res.ai_type,
							button: 0,
							action: 'loading',
							position: 'reply',
							question_id: new Date().getTime() + '' + userInfo.value.uuid,
							is_replied: 1, // 1为有返回答案，0为无返回答案，2为无效提问
							reply_type: 4, // 0为富文本，1为卡片，2为无返回答案时默认值，3为自主查询，4为模糊匹配，5为无效提问
							result: 1,
							word: question ? question : '', // 提问的内容
						})
						data.msgList.push({
							msgType: 'List',
							content: res.more_list,
							title: $t('text_more_advice')
						})
					} else {
						// 返回精准问题打点
						setLog({
							ai_type: res.ai_type,
							button: 0,
							action: 'loading',
							position: 'reply',
							question_id: new Date().getTime() + '' + userInfo.value.uuid,
							is_replied: 1, // 1为有返回答案，0为无返回答案，2为无效提问
							reply_type: 0, // 0为富文本，1为卡片，2为无返回答案时默认值，3为自主查询，4为模糊匹配，5为无效提问
							result: 1,
							word: question ? question : '', // 提问的内容
							real_question_id: answerData.question_id ? answerData.question_id : 0 // 返回的精准匹配问题的id
						})
					}
				}
			} else if (answerData.answer_type === 2) {
				console.log('暂无自助查询功能')
			} else if (answerData.answer_type === 3) {
				setLog({
					ai_type: res.ai_type,
					button: 0,
					action: 'loading',
					position: 'reply',
					question_id: new Date().getTime() + '' + userInfo.value.uuid,
					is_replied: 1, // 1为有返回答案，0为无返回答案
					reply_type: 1, // 0为富文本，1为卡片，2为无返回答案时默认值
					result: 1,
					word: question ? question : '', // 提问的内容
					real_question_id: answerData.question_id ? answerData.question_id : 0 // 返回的精准匹配问题的id
				})
				getCardInfo(answerData.answer_card_id as number)
			} else if (answerData.answer_type === 4) {
				data.msgList.push({
					msgType: 'RichTextTickets',
					content: answerData
				})
				if (res.more_list && res.more_list.length > 0) {
					setLog({
						ai_type: res.ai_type,
						button: 0,
						action: 'loading',
						position: 'reply',
						question_id: new Date().getTime() + '' + userInfo.value.uuid,
						is_replied: 1, // 1为有返回答案，0为无返回答案，2为无效提问
						reply_type: 4, // 0为富文本，1为卡片，2为无返回答案时默认值，3为自主查询，4为模糊匹配，5为无效提问
						result: 1,
						word: question ? question : '', // 提问的内容
					})
					data.msgList.push({
						msgType: 'List',
						content: res.more_list,
						title: $t('text_more_advice')
					})
				} else {
					// 精准匹配富文本工单打点
					setLog({
						ai_type: res.ai_type,
						button: 0,
						action: 'loading',
						position: 'reply',
						question_id: new Date().getTime() + '' + userInfo.value.uuid,
						is_replied: 1, // 1为有返回答案，0为无返回答案，2为无效提问
						reply_type: 0, // 0为富文本，1为卡片，2为无返回答案时默认值，3为自主查询，4为模糊匹配，5为无效提问
						result: 1,
						word: question ? question : '', // 提问的内容
						real_question_id: answerData.question_id ? answerData.question_id : 0 // 返回的精准匹配问题的id
					})
				}
			} else if (answerData.answer_type === 100) {
				data.msgList.push({
					msgType: 'RichText',
					needEval: false,
					content: answerData,
					ai_type: res.ai_type,
				})
			}
		} else if (res.match_type === 2) { // 模糊匹配
			setLog({
				ai_type: res.ai_type,
				button: 0,
				action: 'loading',
				position: 'reply',
				question_id: new Date().getTime() + '' + userInfo.value.uuid,
				is_replied: 1, // 1为有返回答案，0为无返回答案
				reply_type: 4, // 0为富文本，1为卡片，2为无返回答案时默认值
				result: 1,
				word: question ? question : '', // 提问的内容
			})
			data.msgList.push({
				msgType: 'List',
				content: res.answer_details
			})
		} else if (res.match_type === 3) { // 未匹配
			// 计数要排除历史工单跳回情况和无智能客服直接触发工单的情况,计数两次后清零
			data.unmatchNum = data.unmatchNum === 2 ? 1 : data.unmatchNum + 1
			// 未匹配打点
			setLog({
				ai_type: res.ai_type,
				button: 0,
				action: 'loading',
				position: 'reply',
				question_id: new Date().getTime() + '' + userInfo.value.uuid,
				is_replied: 0, // 1为有返回答案，0为无返回答案
				reply_type: 2, // 0为富文本，1为卡片，2为无返回答案时默认值
				result: 1,
				unmatch_num: data.unmatchNum, // 未匹配次数
				word: question ? question : '', // 提问的内容
			})
			// 触发两次未匹配才给工单入口
			if (data.unmatchNum < 2) {
				data.msgList.push({
					msgType: 'RichText',
					needEval: false,
					content: {
						answer_rich_text: $t('unmatch_first_tip')
					},
					ai_type: res.ai_type,
				})
				return
			}
			data.msgList.push({
				msgType: 'UnMatch'
			})
		}
	}, (res: string) => {
		setLog({
			button: 0,
			action: 'loading',
			position: 'reply',
			question_id: new Date().getTime() + '' + userInfo.value.uuid,
			result: 0,
			word: question ? question : '', // 提问的内容
		})
		data.isRequesting = false
		data.msgList.pop()
		Toast(res)
	}).catch((err: string) => {
		setLog({
			button: 0,
			action: 'loading',
			position: 'reply',
			question_id: new Date().getTime() + '' + userInfo.value.uuid,
			result: 0,
			word: question ? question : '', // 提问的内容
		})
		data.isRequesting = false
		data.msgList.pop()
		Toast(err)
	})
}
// 用户点击发送按钮
const askHandle = () => {
	if (!data.ask || data.isRequesting) return
	sendQuestion(data.ask, 1)
	data.ask = ''
	setLog({
		button: 0,
		action: 'click',
		result: 1,
		position: 'ask',
		input: data.ask,
		question_id: new Date().getTime() + '' + userInfo.value.uuid
	})
}
// 选择点踩原因
const selectDislike = (item: Record<string, unknown> & { question_id: number }) => {
	data.msgList.push({
		msgType: 'Loading'
	})
	dislikeOpt({}).then((res: Array<Record<string, unknown>>) => {
		data.msgList.pop()
		data.msgList.push({
			msgType: 'Dislike',
			question_data: item,
			reasonList: res
		})
	}).catch((err: string) => {
		data.msgList.pop()
		Toast(err)
	})
}
// 提交点踩原因成功
const dislikeSuccess = (question_data?: questionDataT) => {
	// data.msgList.pop()
	if (question_data) {
		router.push({
			path: '/pc/tickets',
			query: {
				dislike_tickets: question_data.answer_rich_show_cat
			}
		})
	} else {
		data.msgList.push({
			msgType: 'RichText',
			needEval: false,
			content: {
				answer_rich_text: $t('text_dislike_tip')
			}
		})
	}
}
const backTop = ()=> {
	const wrap: HTMLElement | null = document.getElementById('s-container')
	wrap?.scrollTo({
		top: 0,
		behavior: 'smooth'
	})
}
// 回到顶部按钮显隐
const scrollTopShow = ()=> {
	const wrap: HTMLElement | null = document.getElementById('s-container')
	data.toTopShow = (wrap as HTMLElement).scrollTop > 200
}

const { ask, msgList, toTopShow } = toRefs(data)
</script>

<style lang="scss" scoped>
.pc_width {
	max-width: 1200Px;
	margin: 0 auto;
	height: 100%;
	.input_item {
		input {
			text-indent: 10Px;
		}
	}
}
.input_item {
	height: 100%;
	float: left;
	overflow: hidden;
	border-radius: 10px;
	input {
		width: 1301.984px;
		height: 100%;
		line-height: 72.036px;
		border-radius: 10px;
		border: 1px solid #626059;
		background-color: #4B4F58;
		color: #CECFC9;
	}
	::-webkit-input-placeholder {
		font-size: 34.684px;
		line-height: 72.036px;
		color: rgba(206, 207, 201, 0.5);
	}
}
.smart_btn {
	width: 242.788px;
	height: 100%;
	line-height: 72.036px;
	text-align: center;
	padding: 0px 6.67px;
	float: right;
	font-size: 34.684px;
	background-image: url("~@/assets/img/smart_btn.png");
	background-position: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	color: #3C3F46;
}
@media all and (orientation : portrait) {
	.input_item {
		input {
			width: 8.93rem;
			height: 100%;
			line-height: 1.07rem;
			font-size: 0.46rem;
		}
		::-webkit-input-placeholder {
			font-size: 0.46rem;
			line-height: 1.07rem;
			color: rgba(206, 207, 201, 0.5);
		}
	}
	.smart_btn {
		width: 3.25rem;
		line-height: 1.07rem;
		font-size: 0.46rem;
	}
}
.back_top {
  z-index: 1;
  width: 69.368px;
  height: 69.368px;
  background-image: url("~@/assets/img/back_top.png");
  background-size: cover;
  position: absolute;
  right: 26.68px;
  bottom: 200.1px;
}
</style>
