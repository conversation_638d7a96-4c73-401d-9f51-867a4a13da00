<template>
	<PcWrapper>
		<loading-wrap :loading="loading">
			<div class="ticket-bg">
				<div class="ticket-wrapper">
					<div class="history-content">
						<table class="tableList" cellspacing="0" v-if="historyList.length > 0">
							<tr class="row head">
								<th class="item">{{ $t("text_created_at") }}</th>
								<th class="item">{{ $t("text_ticket") }}</th>
								<th class="item">{{ $t("text_category") }}</th>
								<th class="item">{{ $t("text_progress") }}</th>
								<th class="item">{{ $t("text_detail") }}</th>
							</tr>
							<tr class="row" v-for="(v, k) in historyList" :key="k">
								<td class="item">{{ v.created_at }}</td>
								<td class="item">{{ v.ticket_id }}</td>
								<td class="item">{{ v.category }}</td>
								<td class="item">
									<div class="position_box">
										{{ progressEnum[v.progress] }}
									</div>
								</td>
								<td class="item">
									<div class="more" @click="showDetails(v.ticket_id)">
										{{ $t("btn_view") }}
										<div class="red_point" v-if="v.read === 0"></div>
									</div>
								</td>
							</tr>
						</table>
						<div v-else class="emptybox">
							<div>{{ $t("text_none_record") }}</div>
							<div class="a" @click="goauto">{{ $t("btn_submit_question") }}</div>
						</div>
					</div>
				</div>
			</div>
		</loading-wrap>
	</PcWrapper>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, computed, getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from "vue-router"
import { Toast } from 'vant'
import LoadingWrap from '@/components/loadingWrap'
import { getHistoryList } from '@/api/tickets'
interface dataT {
	historyList: Array<{
		ticket_id: number,
		created_at: string,
		category: string,
		progress: number
	}>,
	loading: boolean
}
export default defineComponent({
	name: 'History',
	components: { LoadingWrap }
})
</script>
<script setup lang="ts">
const router = useRouter()
const { t: $t } = useI18n()
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const data: dataT = reactive({
	historyList: [],
	loading: true
})
const progressEnum = computed((): {
	[key: number]: string
} => {
	return {
		1: $t('text_done'),
		2: $t('text_need_complete'),
		3: $t('text_in_progress'),
		4: $t('text_timeout'),
		5: $t('text_tips_processing'),
		6: $t('text_done')
	}
})
// 获取历史列表
getHistoryList({}).then((res: dataT['historyList']) =>{
	// 历史工单加载成功打点
	setLog({
		button: 0,
		action: 'loading',
		result: 1,
		position: 'history_record'
	})
	data.historyList = res
	data.loading = false
}, (res: string) =>{
	// 历史工单加载失败打点
	setLog({
		button: 0,
		action: 'loading',
		result: 0,
		position: 'history_record'
	})
	data.loading = false
	Toast(res)
}).catch((err: string)=> {
	// 历史工单加载失败打点
	setLog({
		button: 0,
		action: 'loading',
		result: 0,
		position: 'history_record'
	})
	data.loading = false
	Toast(err)
})
const showDetails = (id: number) => {
	// 历史工单点击详情打点
	setLog({
		button: id,
		action: 'click',
		result: 1,
		position: 'history_record',
		view_answers_time: new Date().getTime() // 玩家查看答案的时间
	})
	router.push({ path: '/pc/detail', query: { ticketId: id } })
}
// 暂时逻辑，因为现在没有智能客服，先跳转工单
const goauto = () =>{
	router.push({ path: '/pc/tickets' })
}

const { historyList, loading } = toRefs(data)
</script>

<style lang="scss" scoped>
.history-content {
	font-size: 32.016px;
	width: 100%;
	height: 100%;
	overflow: auto;

	.tableList {
		width: 100%;
		table-layout: automatic;
		.row {
			padding: 13.34px 0;
			.item {
				vertical-align: middle;
				text-align: center;
				padding: 16.008px;
				border-bottom: 1px dashed #57544e;
				.more {
					width: 186.76px;
					box-shadow: 1Px 2Px 4Px 1Px rgba(0, 0, 0, 0.4);
					height: 66.7px;
					line-height: 66.7px;
					background: linear-gradient(to bottom, #C39C36, #AA8821, #F5CE61);
					color: #fdefe9;
					text-transform: uppercase;
					border-radius: 3Px;
					position: relative;
					// text-shadow: 0px 1px 2px #817042, 1px 1px 2px #817042, 0px 0px 2px #817042, 1px 0px 2px #817042;
					// opacity: 0.7;
					margin: 0 auto;
					box-sizing: border-box;
					.red_point {
						width: 26.68px;
						height: 26.68px;
						border-radius: 50%;
						overflow: hidden;
						background: url("~@/assets/img/red_p.png") no-repeat;
						background-size: contain;
						position: absolute;
						top: 2.668px;
						right: 2.668px;
					}
				}
			}
			&.head {
				background: rgba(0,0,0,0.2);
				.item {
					font-weight: bold;
					border-bottom: 0px;
				}
			}
		}
	}
}
.emptybox {
  position: absolute;
  height: 106.72px;
  top: 50%;
  margin-top: -80.04px;
  width: 100%;
  left: 50%;
  margin-left: -50%;
  text-align: center;
  font-size: 37.352px;
  .a {
    text-decoration: underline;
    color: #f7bf28;
    margin-top: 26.68px;
		cursor: pointer;
  }
}
@media all and (orientation : portrait) {
	.emptybox {
		font-size: 48px;
		margin-top: -120px;
	}
	.history-content {
		.tableList {
			.row {
				.item {
					.more {
						height: 72px;
						line-height: 72px;
					}
				}
			}
		}
	}
	
	
}
</style>