<template>
  <div class="t-items">
    <dialog-wrapper>
      <div class="richtext_box">
        <div class="rich_content" v-html="itemData.fields.content"></div>
      </div>
    </dialog-wrapper>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, onMounted } from 'vue'
import { ImagePreview } from 'vant'
interface autoFlowDataT {
	process_session?: string,
	process_id?: number,
	node_id?: number,
	cat_id?: number,
	cat_name?: string,
	module_group?: number,
	fields?: Record<string, unknown>
}
export default defineComponent({
  name: 'AutoRichText'
})
</script>
<script lang="ts" setup>
defineProps<{
  itemData: autoFlowDataT
}>()
onMounted(() => {
  // 富文本图片点击可预览
  const richTextImage = document.querySelectorAll('.rich_content img')
  richTextImage.forEach(item => {
    (item as HTMLElement).onclick = (e: Event) => {
      const parentDom = document.getElementById('s-container')
      ImagePreview({
        images: [(e.target as HTMLImageElement).src],
        teleport: 'body',
        closeable: false,
        showIndex: false
      })
    }
  })
})
</script>

<style lang="scss" scoped>
.t-items {
  & ::v-deep .dialog_wrapper {
    max-width: 100%;
    width: 100% !important;
    margin-top: 0px;
    margin-bottom: 0.2rem;
  }
}
.richtext_box {
  // padding: 20px;
  box-sizing: border-box;
  width: 1128.564px;
  & ::v-deep img {
    max-width: 919.126px;
    margin: 26.68px 0px;
    display: block;
  }
  & ::v-deep a {
    text-decoration: underline;
    color: #F5C133;
  }
  
}
@media all and (orientation : portrait) {
  .richtext_box{
    width: 10.1rem;
    & ::v-deep img {
      max-width: 8.57rem;
    }
  }
}
</style>