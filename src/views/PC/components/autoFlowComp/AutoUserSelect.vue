<template>
  <div class="t-items">
		<div class="title">
			<img src="~@/assets/img/group.png">
			<!-- 请选择问题分类 / 请选择xxx的细分问题分类 -->
			<span>{{ itemData.fields.desc }}</span>
		</div>
		<div class="list">
			<div :class="['list-item', hasSelect && activeKey === k ? 'hasSelected' : '']" v-for="(v, k) in itemData.fields.list" :key="k">
				<div style="float:left" @click="listClickHandle(v, k)">
					<img  v-if="hasSelect && activeKey === k" src="~@/assets/img/point_selected.png"><img v-else src="~@/assets/img/point.png"><span>{{ v.content }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs } from 'vue'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
interface dataT {
  hasSelect: boolean,
  activeKey: number
}
export default defineComponent({
	name: 'AutoUserSelect'
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const data: dataT = reactive({
  hasSelect: false,
  activeKey: -1
})
const emit = defineEmits<{
  (event: 'item', itemInfo: Record<string, unknown>): void
}>()
const listClickHandle = (item: Record<string, unknown>, activeKey: number) => {
  if (data.hasSelect) {
    if (activeKey !== data.activeKey) {
      Toast($t('auto_only_text'))
    }
    return
  }
  data.hasSelect = true
  data.activeKey = activeKey
  emit('item', {
    ...props.itemData,
    fields: item
  })
}
const { hasSelect, activeKey } = toRefs(data)
</script>

<style lang="scss" scoped>
.title {
  // overflow: hidden;
  margin: 0px 21.334px 26.68px 0px;
  box-sizing: border-box;
  width: 100%;
  padding: 8Px 2Px 10Px 8Px;
  height: auto;
  line-height: 37.352px;
  color: #cecfc9;
  font-size: 34.684px;
  border-radius: 0px 2Px 2Px 0px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);

  img {
    display: block;
    height: 37.352px;
    width: 37.352px;
    float: left;
    opacity: 0.8;
    margin: 0px 6Px 0px 0px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 61.364px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.list-item {
  color: #E9C86F;
  height: auto;
  overflow: hidden;
  font-size: 32px;
  line-height: 53.36px;
  padding-left: 26.68px;
  vertical-align: middle;
  margin: 10Px auto;
  cursor: pointer !important;
  img {
    display: block;
    height: 53.36px;
    width: 53.36px;
    float: left;
    margin: 0px 6Px 0px;
  }
}
.hasSelected {
  color: #afafad;
}
@media all and (orientation : portrait) {
  .title {
    height: auto;
    line-height: 50px;
    font-size: 0.46rem;
    margin: 0px 16px 14px 0px;
    img {
      height: 48px;
      width: 48px;
      margin: 0px 6Px 0px 0px;
    }
  }
  .title::after {
    width: 100px;
  }
  .title::before {
    height: 70px;
  }
  .list-item {
    line-height: 60px;
    font-size: 44px;
    img {
      height: 60px;
      width: 60px;
      margin: 0px 6Px 0px;
    }
  }
}
</style>