<template>
	<div class="t-items">
		<div v-if="formData.category" class="content">
			<div class="title">
				<img src="~@/assets/img/ft.png">
				<span>{{ formData.category }}</span>
			</div>
			<van-form ref="formRef" :show-error="false" class="form-wrapper">
				<div v-for="(item, index) in formItemList" :key="index" class="form-item-wrap">
					<!-- “提示”或者“说明”的label特殊处理 -->
					<div v-if="item.field_type === ITEM_TYPES.CAPTION" class="form-caption" :class="{ required: item.is_required }">
						<div class="icon"></div>
						<span>{{ item.field_name }}: {{ item.hint_field }}</span>
					</div>
					<div v-else class="form-label" :class="{ required: item.is_required }">
						<div class="icon"></div>
						<span>{{ item.field_name }}</span>
					</div>
					<!-- input -->
					<van-field
						class="form-item"
						v-if="item.field_type === ITEM_TYPES.TEXT"
						v-model="submitForm.fields[item.field_name]"
						:placeholder="item.hint_field"
						:rules="[
							{ required: item.is_required, message: item.required_rule }
						]"
					/>
					<!-- textarea -->
					<van-field
						class="form-item"
            v-if="item.field_type === ITEM_TYPES.TEXTAREA"
            v-model="submitForm.fields[item.field_name]"
            type="textarea"
            rows="4"
            autosize
            maxlength="2000"
            show-word-limit
            :placeholder="item.hint_field"
            :rules="[
              { required: item.is_required, message: item.required_rule }
            ]"
          />
					<!-- number input -->
					<van-field
						class="form-item"
            v-if="item.field_type === ITEM_TYPES.NUMBER"
            v-model="submitForm.fields[item.field_name]"
            type="number"
            :placeholder="item.hint_field"
            :rules="[
              { required: item.is_required, message: item.required_rule }
            ]"
          />
					<!-- datepicker -->
					<template v-if="item.field_type === ITEM_TYPES.DATEPICKER">
            <van-field
							class="form-item"
              readonly
              clickable
              name="datetimePicker"
              v-model="submitForm.fields[item.field_name]"
              :right-icon="selectImg"
              :rules="[
                { required: item.is_required, message: item.required_rule }
              ]"
              @click="item.show_picker = true"
            />
            <van-popup v-model:show="item.show_picker" overlay-class="f-overlay" position="bottom">
              <van-datetime-picker
                :type="item.field_extend.only_date === true ? 'date' : 'datetime' "
                v-model="currentDate"
                @confirm="onTimeConfirm($event, item, item.field_name)"
                @cancel="item.show_picker = false"
              >
                <template #confirm>
                  {{ $t('btn_confirm') }}
                </template>
                <template #cancel>
                  {{ $t('btn_cancel') }}
                </template>
              </van-datetime-picker>
            </van-popup>
          </template>
					<!-- select -->
					<template v-if="item.field_type === ITEM_TYPES.SELECT">
            <van-field
							class="form-item"
              readonly
              clickable
              name="picker"
              v-model="submitForm.fields[item.field_name]"
              :right-icon="selectImg"
              :rules="[
                { required: item.is_required, message: item.required_rule }
              ]"
              @click="item.show_picker = true"
            />
            <van-popup v-model:show="item.show_picker" position="bottom">
              <van-picker
                show-toolbar
                :columns="item.option_field"
                :columns-field-names="{text: 'name'}"
                @confirm="onPickerConfirm($event, item, item.field_name)"
                @cancel="item.show_picker = false"
              >
                <template #confirm>
                  {{ $t('btn_confirm') }}
                </template>
                <template #cancel>
                  {{ $t('btn_cancel') }}
                </template>
              </van-picker>
            </van-popup>
          </template>
					<!-- img upload -->
					<div class="form-item">
						<template v-if="item.field_type === ITEM_TYPES.PIC">
							<img-upload
								:isH5Upload="true"
								@success="uploadSuccess(item.field_name, $event)"
								@remove="removeUploaded(item.field_name, $event)"
							></img-upload>
						</template>
					</div>
					<!-- video upload -->
					<div class="form-item">
						<template v-if="item.field_type === ITEM_TYPES.VIDEO">
							<video-upload
								:isH5Upload="true"
								@success="uploadSuccess(item.field_name, $event)"
								@remove="removeUploaded(item.field_name)"
							></video-upload>
						</template>
					</div>
				</div>
				<!-- 提交按钮，表单未加载时不展示按钮 -->
				<div v-if="formItemList && formItemList.length !== 0" class="button-wrapper">
					<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
						<div class="fp-button" @mousedown.stop.prevent="onSubmit" @mouseup.stop.prevent="pushAnimate = false">{{ $t("text_submit") }}</div>
					</div>
        </div>
			</van-form>
		</div>
		<van-loading v-else size="24px" color="#c59b29" type="spinner" vertical>{{ $t('text_in_progress') }}</van-loading>
	</div>
</template>

<script lang="ts">
	import { defineComponent, ref, reactive, toRefs, defineProps, onMounted, nextTick, defineEmits, getCurrentInstance, computed } from 'vue'
	import { useStore } from 'vuex'
	import { Toast } from 'vant'
	import { useI18n } from 'vue-i18n'
	import { getTplInfo, createTicket } from '@/api/tickets'
	interface AnyObj {
		// eslint-disable-next-line
		[key: string]: any
	}
	interface FormData {
		fields?: string,
		category?: string,
		tpl?: string,
		tpl_id?: number
	}
	interface FormItem {
		[key: string]: unknown,
		field_name: string,
		hint_field: string,
		is_required: boolean,
		required_rule: string,
		show_picker?: boolean,
		option_field: Array<Record<string, unknown>>,
		field_extend: Record<string, unknown>,
	}
	interface SubmitForm {
		[key: string]: unknown,
		fields: AnyObj
	}
	const ITEM_TYPES = {
		TEXT: 0,
		TEXTAREA: 1,
		NUMBER: 2,
		DATEPICKER: 3,
		SELECT: 4,
		PIC: 5,
		VIDEO: 6,
		CAPTION: 7
	}
	export default defineComponent({
		name: 'TicketForm'
	})
</script>
<script setup lang="ts">
	const { state } = useStore()
	const userInfo = computed(() => state.userInfo)
	const { t: $t } = useI18n()
	const currentDate = ref(new Date())
	const formRef = ref()
	const selectImg = require('@/assets/img/select.png')
	const isShowChat = computed(() => state.global.isShowChat)
	const data: {
		formData: FormData,
		formItemList: Array<FormItem>,
		submitForm: SubmitForm,
		locked: boolean,
		pushAnimate: boolean
	} = reactive({
		formData: {},
		formItemList: [],
		submitForm: {
			ticket_type: isShowChat.value,
			origin: 1,
			//  nickname: '',
			cat_id: null,
			fields: {},
			trouble_uid: null
		},
		locked: false,
		pushAnimate: false
	})
	const _u = getCurrentInstance()?.appContext.config.globalProperties.$utils
	const setLog = _u.basicLog
	const props = defineProps<{
		itemData: {
			id: number,
			label?: string,
			level: number,
			tpl_id: number,
			index: number,
			process_id?: number,
			process_session?: string
		}
		fromTicketId: number
	}>()
	const emit = defineEmits<{
		(event: 'update-scroll'): void
		(event: 'submit-success', ticket_id: number): void
	}>()

	onMounted(() => {
		const { tpl_id, id, process_id, process_session } = props.itemData
		// 提交表单需要cat_id
		data.submitForm.cat_id = id
		// 自动化流程需要process_id和process_session
		if (process_id) data.submitForm.process_id = process_id
		if (process_session) data.submitForm.process_session = process_session
		// 提交表单需要昵称
		// getNickName()
		getTplInfo({
			tpl_id,
			cat_id: id,
			process_id: process_id ? process_id : 0
		}).then((res: FormData) => {
			// 表单加载成功打点
			setLog({
				button: 0,
				action: 'loading',
				result: 1,
				position: 'form_fill'
			})

			data.formData = res
			data.formItemList = res.fields && JSON.parse(res.fields)
			getFieldMap()
			nextTick(() => {
				emit('update-scroll')
			})
		}, () => {
			// 表单加载失败打点
			setLog({
				button: 0,
				action: 'loading',
				result: 0,
				position: 'form_fill'
			})
		})
	})
	// sdk映射默认值
	const getFieldMap = () => {
		data.formItemList.forEach((item) => {
			if (
				(
					item.field_type === ITEM_TYPES.TEXT ||
					item.field_type === ITEM_TYPES.TEXTAREA ||
					item.field_type === ITEM_TYPES.NUMBER
				) && item.field_map
			) {
				if (item.field_map === 'role_name') {
					data.submitForm.fields[item.field_name] = userInfo.value[item.field_map]
						? decodeURIComponent(userInfo.value[item.field_map]) : userInfo.value.name
							? decodeURIComponent(userInfo.value.name) : ''
				} else {
					data.submitForm.fields[item.field_name] = userInfo.value[item.field_map as string] ? decodeURIComponent(userInfo.value[item.field_map as string]) : ''
        if (item.field_map === 'uid' || item.field_map === 'UID') {
          data.submitForm['uid_key'] = item.field_name
        }
				}
			}
		})
	}
	// const getNickName = () => {
	// 	getUserInfo({}).then((res: Record<string, unknown>) => {
	// 		res && (data.submitForm.nickname = res.name)
	// 	})
	// }
	const onTimeConfirm = (time: Date, item: FormItem, name: string): void => {
		if (item.field_extend.only_date) {
			data.submitForm.fields[name] = _u.formatDate(time.getTime(), 'YYYY-MM-DD')
		}	else {
			data.submitForm.fields[name] = _u.formatDate(time.getTime())
		}
		item.show_picker = false
	}
	const onPickerConfirm = (option: Record<string, unknown>, item: FormItem, name: string): void => {
		data.submitForm.fields[name] = option.name
		item.show_picker = false
	}
	// img、video上传成功
	const uploadSuccess = (name: string, path: Array<string>): void => {
		!data.submitForm.fields[name] && (data.submitForm.fields[name] = [])
    data.submitForm.fields[name].push(...path)
	}
	// 删除img、video上传
	const removeUploaded = (name: string, index?: number): void => {
		index = index || 0
		data.submitForm.fields[name].splice(index, 1)
	}
	// 提交表单
	const onSubmit = () => {
		// 按钮按下
		data.pushAnimate = true
		// 同一时间只能提交一个表单
		if (data.locked) {
			// 表单提交中\n请稍候
			Toast.fail($t('text_submiting'))
			return
		}
		// 表单验证
		formRef.value && formRef.value.validate()
		.then(() => {
			const result = data.formItemList.find(formItem => {
				return (
					(formItem.field_type === ITEM_TYPES.PIC ||
						formItem.field_type === ITEM_TYPES.VIDEO) &&
					formItem.is_required &&
					(!data.submitForm.fields[formItem.field_name] ||
						!data.submitForm.fields[formItem.field_name].length)
				)
			})
			if (result) {
				Toast(result.required_rule)
				return
			}
			data.locked = true
			const submitParams = JSON.parse(JSON.stringify(data.submitForm))
			// 如果有uid_key，将uid_key对应的值赋给trouble_uid，这是代提单的uid
			if (submitParams.uid_key && submitParams.fields[submitParams.uid_key]) {
        submitParams.trouble_uid = Number(submitParams.fields[submitParams.uid_key])
      }
			submitParams.fields = JSON.stringify(submitParams.fields)
			if (props.fromTicketId !== 0) {
				submitParams.from_ticket_id = props.fromTicketId
			}
			createTicket(submitParams).then((res: Record<string, number>) => {
				// 工单表单提交成功打点
				setLog({
					button: 1,
					action: 'click',
					result: 1,
					position: 'form_fill'
				})
				emit('submit-success', res.ticket_id)
				data.locked = false
			}, (res: string) =>{
				// 工单表单提交失败打点
				setLog({
					button: 1,
					action: 'click',
					result: 0,
					position: 'form_fill'
				})
				data.locked = false
				Toast(res)
			}).catch((err: string)=> {
				// 工单表单提交失败打点
				setLog({
					button: 1,
					action: 'click',
					result: 0,
					position: 'form_fill'
				})
				data.locked = false
				Toast(err)
			})
		}).catch((err: string) => {
			console.log('TicketForm onSubmit err', err)
		})
	}
	const { formData, formItemList, submitForm, pushAnimate } = toRefs(data)
</script>

<style lang="scss" scoped>
.content {
	word-wrap: break-word;
	word-break: break-all;
	overflow: hidden;
	font-size: 18.676px;
	.title {
		margin: 0px 21.344px 26.68px 0px;
		box-sizing: border-box;
		width: 100%;
		overflow: hidden;
		padding: 8Px 0px 10Px 8Px;
		height: auto;
		line-height: 37.352px;
    color: #cecfc9;
    font-size: 37.352px;
		border-radius: 0px 2Px 2Px 0px;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
		text-shadow: 0px 1px 2px rgba(76, 79, 88, 0.6), 1px 1px 2px rgba(76, 79, 88, 0.6), 0px 0px 2px rgba(76, 79, 88, 0.6), 1px 0px 2px rgba(76, 79, 88, 0.6);
		border-left: 4Px solid rgba(245, 193, 51, 0.7);

		img {
			display: block;
			height: 37.352px;
			width: 37.352px;
			float: left;
			opacity: 0.8;
			margin: 0px 6Px 0px 0px;
		}
	}

	.form-wrapper {
		box-sizing: border-box;
		padding: 0px 26.68px 0px 13.34px;
		.form-item-wrap {
			padding-bottom: 26.68px;
		}
		.form-item {
			box-sizing: border-box;
			width: 97.7%;
			margin-left: 2.1% ;
		}
	}

	& ::v-deep(.van-form) {
		font-size: 37.352px;
	}

	& ::v-deep(.van-cell) {
		background-color: rgba(103, 89, 58, 0.3);
		border: 2px solid #bea55f;
		padding: 16px 26.68px;
		text-shadow: none !important;
		color: #E9C86F;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 34.684px;
		color: #E9C86F;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #b6ae9b;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #b6ae9b;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #b6ae9b;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #b6ae9b;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #b6ae9b;
		}
	}

	& ::v-deep(.van-field__right-icon) {
		width: 53.36px;
		padding: 0;
		line-height: 0;

		img {
			width: 100%;
			height: auto;
		}
	}
}

@media all and (orientation : portrait) {
	.content {
		font-size: 44px;
		.form-wrapper {
			.form-item {
				width: 96.5%;
				margin-left: 3.3% ;
			}
		}
		& ::v-deep(.van-form) {
			font-size: 44px;
		}
		& ::v-deep(.van-field__control) {
			font-size: 42px;
		}
		& ::v-deep(.van-field__right-icon) {
			width: 60px;
		}
		& ::v-deep(.van-cell) {
			border: 2Px solid #bea55f;
			padding: 16px 30px;

			&::after {
				border-bottom: 0;
			}
		}
		.title {
			height: auto;
			line-height: 90px;
			font-size: 0.6rem;

			img {
				height: 70px;
				width: 70px;
				margin: 8.5px 6Px 0px 0px;
			}
		}
	}
}
</style>
