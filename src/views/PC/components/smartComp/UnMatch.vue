<template>
  <dialog-wrapper>
    <div class="unmatch" v-html="$t('text_unmatch')" @click="goTickets"></div>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'UnMatch'
})
</script>
<script lang="ts" setup>
const router = useRouter()
const goTickets = () => {
  router.push('/pc/tickets')
}
</script>

<style lang="scss" scoped>
.unmatch {
  color: #CECFC9;
  font-size: 34.684px;
  & ::v-deep span {
    text-decoration: underline;
    color: #F5C133;
    cursor: pointer;
  }
}
@media all and (orientation : portrait) {
  .unmatch {
    font-size: 0.46rem;
    word-break: normal;
  }
}
</style>