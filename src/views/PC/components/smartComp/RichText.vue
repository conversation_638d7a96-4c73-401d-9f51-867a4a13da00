<template>
  <dialog-wrapper>
    <div class="richtext_box">
      <div class="rich_content" v-html="props.msgItem.content.answer_rich_text"></div>
      <div class="rich_eval_box" v-if="props.msgItem.needEval">
        <p class="rich_eval_text">
          <auto-font-size :text="$t('text_resolved')"></auto-font-size>
        </p>
        <div class="rich_eval_btn">
          <span class="btn like" :class="isActive ? 'active' : ''"
            :style="{ 'pointer-events' : isUnActive ? 'none' : 'auto' }" @click="solve(1)"></span>
          <span class="btn dislike" :class="isUnActive ? 'active' : ''"
            :style="{ 'pointer-events': isActive ? 'none' : 'auto' }" @click="solve(-1)"></span>
        </div>
      </div>
    </div>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent, defineProps, reactive, toRefs, getCurrentInstance, computed, onMounted, defineEmits } from 'vue'
import { useStore } from 'vuex'
import { sendSolve } from '@/api/smart'
import { ImagePreview, Toast } from 'vant'
interface dataT {
  isRequesting: boolean,
  isActive: boolean,
  isUnActive: boolean
}
export default defineComponent({
  name: 'RichText'
})
</script>
<script lang="ts" setup>
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const { state } = useStore()
const userInfo = computed(() => state.userInfo)
const data: dataT = reactive({
  isRequesting: false,
  isActive: false,
  isUnActive: false
})
const emit = defineEmits<{
  (event: 'dislike', info: Record<string, unknown> & { question_id: number }): void
}>()
const props = defineProps<{
  msgItem: Record<string, unknown> & {
    content: Record<string, unknown> & {
      question_id: number
    },
    ai_type: number
  }
}>()
onMounted(() => {
  // 富文本图片点击可预览
  const richTextImage = document.querySelectorAll('.rich_content img')
  richTextImage.forEach(item => {
    (item as HTMLElement).onclick = (e: Event) => {
      const parentDom = document.getElementById('s-container')
      ImagePreview({
        images: [(e.target as HTMLImageElement).src],
        teleport: 'body',
        closeable: false,
        showIndex: false
      })
    }
  })
})
const solve = ( flag: number ) => {
  if (data.isRequesting) return
  data.isRequesting = true
  const params = {
    question_id: props.msgItem.content.question_id,
    score: flag
  }
  sendSolve(params).then((res: Record<string, unknown>) => {
    setLog({
      ai_type: props.msgItem.ai_type,
      button: flag === 1 ? 1 : 2,
      action: 'click',
      position: 'reply',
      question_id: new Date().getTime() + '' + userInfo.value.uuid,
      result: 1,
      word: props.msgItem.content.question_desc ? props.msgItem.content.question_desc : '', // 提问的内容
			real_question_id: props.msgItem.content.question_id ? props.msgItem.content.question_id : 0 // 返回的精准匹配问题的id
    })
    flag === 1 ? data.isActive = true : data.isUnActive = true
    if (flag === -1) {
      emit('dislike', props.msgItem.content)
    }
  }).catch((err: string) => {
    setLog({
      ai_type: props.msgItem.ai_type,
      button: flag === 1 ? 1 : 2,
      action: 'click',
      position: 'reply',
      question_id: new Date().getTime() + '' + userInfo.value.uuid,
      result: 0,
      word: props.msgItem.content.question_desc ? props.msgItem.content.question_desc : '', // 提问的内容
			real_question_id: props.msgItem.content.question_id ? props.msgItem.content.question_id : 0 // 返回的精准匹配问题的id
    })
    Toast(err)
  }).finally(() => {
    data.isRequesting = false
  })
}
const { isActive, isUnActive } = toRefs(data)
</script>

<style lang="scss" scoped>
.richtext_box {
  // padding: 20px;
  box-sizing: border-box;
  width: 1128.564px;
  & ::v-deep img {
    max-width: 919.126px;
    margin: 26.68px 0px;
    display: block;
  }
  & ::v-deep a {
    text-decoration: underline;
    color: #F5C133;
  }
  .rich_eval_box {
    height: 80px;
    margin-bottom: -20px;
    margin-top: 40px;
    border-top: 1px solid rgba(98, 101, 109, 0.36);
  }
  .rich_eval_text {
    line-height: 80px;
    width: 941.804px;
    display: inline-block;
    font-size: 26.68px;
    & ::v-deep .auto-font-size {
      text-align: left !important;
    }
  }
  .rich_eval_btn {
    display: inline-block;
    height: 80px;
    line-height: 80px;
  }
  .btn {
    height: 33.35px;
    width: 33.35px;
    margin-left: 53.36px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    display: inline-block;
    cursor: pointer;
    &.like {
      background-image: url('~@/assets/img/icon_kudos.png');
    }
    &.like.active {
      background-image: url('~@/assets/img/icon_kudos1.png');
    }
    &.dislike {
      background-image: url('~@/assets/img/icon_kudos.png');
      margin-right: 13.34px;
      transform: rotateX(180deg);
    }
    &.dislike.active {
      background-image: url('~@/assets/img/icon_kudos1.png');
      transform: rotateX(180deg);
    }
  }
}
@media all and (orientation : portrait) {
  .richtext_box{
    width: 10.1rem;
    & ::v-deep img {
      max-width: 8.57rem;
    }
    .rich_eval_box{
      height: 1.25rem;
    }
    .rich_eval_text{
      line-height: 1.25rem;
      width: 7.82rem;
      font-size: 0.36rem;
    }
    .rich_eval_btn {
      height: 1.25rem;
      line-height: 1.25rem;
    }
    .btn {
      height: 0.45rem;
      width: 0.45rem;
      margin-left: 0.6rem;
    }
  }
}
</style>