<template>
  <dialog-wrapper>
    <div class="loading_box">
      <span class="loading_sign"><em></em></span>
    </div>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'Loading',
})
</script>
<script lang="ts" setup>
</script>

<style lang="scss" scoped>
.loading_box {
  width: 133.4px;
  height: 40px;
  position: relative;
}
.loading_sign {
  width: 16px;
  height: 16px;
  margin: -8px 0px 0px -8px;
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  &::before, &::after, em {
    content: '';
    width: 16px;
    height: 16px;
    background: #C3BDB1;
    border-radius: 50%;
    position: absolute;
    top: 0px;
  }
  &::before {
    left: -34.684px;
    animation: loading 1.4s linear infinite;
  }
  em {
    animation: loading 1.4s linear 0.2s infinite;
  }
  &::after {
    right: -34.684px;
    animation: loading 1.4s linear 0.4s infinite;
  }
}
@keyframes loading {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-10px);
  }
  40% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(0);
  }
}
</style>