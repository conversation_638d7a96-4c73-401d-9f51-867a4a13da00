<template>
	<dialog-wrapper class="moreBox" v-if="props.msgItem.content.ann_detail || props.msgItem.content.faql_list">
		<div class="item-card-wrap hot-ann">
			<!-- 公告部分 -->
			<div class="ann-box" v-if="props.msgItem.content.ann_detail">
				<div class="ann-title">
					<div class="title-icon"></div>
					<div class="title-text">{{ $t('ann_info_text') }}</div>
				</div>
				<div class="ann-guide">{{ props.msgItem.content.ann_detail.title }}</div>
				<div class="ann-content">
					<!-- 纯文本公告 -->
					<div class="text-ann" v-if="props.msgItem.content.ann_detail.ann_type === 1">{{
						props.msgItem.content.ann_detail.ann_text }}</div>
					<!-- 链接文章公告 -->
					<div class="question-ann" v-if="props.msgItem.content.ann_detail.ann_type === 2">
						<span @click.stop.prevent="questionsClickHandle(props.msgItem.content.ann_detail.ann_text)">{{
							props.msgItem.content.ann_detail.ann_text }}</span>
					</div>
				</div>
			</div>
			<div class="ann-line" v-if="props.msgItem.content.ann_detail"></div>
			<!-- 卡片部分 -->
			<div class="hasann-cardbox">
				<div class="card-history" @click="goHistory" v-if="props.msgItem.isFirst">
					<div class="more-icon"></div>
					<!-- 工单未读红点 -->
          <div class="red_point" v-if="unRead"></div>
					<div class="his-btn">{{ $t('text_history') }}</div>
				</div>
				<div class="card-scroll">
					<div class="card-scroll-item">
						<div :class="['card_item', animateMark === index ? 'rubberBand' : '']"
							v-for="(item, index) in props.msgItem.content.children" :key="index"
							@click.stop.prevent="cardClick(item, index)">
							<div style="position:relative;height:100%;width:100%">
								<div class="card_img" :style="{ 'background-image': `url(${item.card_image})` }"></div>
								<div class="card_text">
									<auto-font-size :text="item.card_title"></auto-font-size>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 热门问题部分 -->
			<div class="hot-ques" v-if="props.msgItem.content.faql_list">
				<div class="ques-box">
					<div class="ques-title">{{ $t('hot_ques_text') }}</div>
					<div v-for="(v, k) in props.msgItem.content.faql_list" :key="k" class="hot-questions-item">
						<span @click="questionsClickHandle(v.question_content)">{{ v.question_content }}</span>
					</div>
				</div>
			</div>
		</div>
	</dialog-wrapper>
  <dialog-wrapper :leftTopIcon="props.msgItem.isFirst" v-else>
    <div class="card_box">
			<div class="history-btn-box" v-if="props.msgItem.isFirst">
        <div class="history-btn" @click="goHistory">
          {{ $t('text_history') }}
          <!-- 工单未读红点 -->
          <div class="red_point" v-if="unRead"></div>
        </div>
      </div>
      <div class="card_title" :style="{ 'margin-left': (!props.msgItem.isFirst ? '0' : '')}">
        <span class="iconfont icon-w_hot" v-if="props.msgItem.isFirst"></span>
        <span>{{ props.msgItem.content.card_guide_title }}</span>
      </div>
      <!-- 卡片提示语 -->
      <div v-if="props.msgItem.content.card_prompt_title" class="card_prompt">{{ props.msgItem.content.card_prompt_title }}</div>
      <!-- show_type === 1 则以卡片形式展示数据 -->
      <div class="cards_wraper" v-if="props.msgItem.content.show_type === 1">
        <div :class="['card_item', animateMark === index ? 'rubberBand' : '']"
          v-for="(item, index) in props.msgItem.content.children" :key="index"
          @click.stop.prevent="cardClick(item, index)">
          <div style="position:relative;height:100%;width:100%">
            <div class="card_img" :style="{ 'background-image': `url(${item.card_image})` }"></div>
            <div class="card_text">
              <auto-font-size :text="item.card_title"></auto-font-size>
            </div>
          </div>
        </div>
      </div>
      <!-- 否则则以列表形式展示数据 -->
      <div class="card_list_wraper" v-else>
        <p class="list_item" v-for="(item, index) in props.msgItem.content.children" :key="index"
          @click="cardClick(item)">
          {{ index + 1 }}.
          {{ item.card_title }}
        </p>
      </div>
    </div>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent, defineProps, reactive, toRefs, defineEmits, onActivated } from 'vue'
import { checkUnread } from '@/api/smart'
import { useRouter } from 'vue-router'
interface dataT {
  animateMark: number,
	unRead: boolean
}
export default defineComponent({
  name: 'Card'
})
</script>
<script lang="ts" setup>
const props = defineProps<{
	msgItem: Record<string, unknown> & {
		content: Record<string, unknown> & {
			children: Array<Record<string, unknown>>,
			ann_detail: Record<string, unknown> & {
				ann_text: string
			},
			faql_list: Array<Record<string, unknown> & {
				question_content: string
			}>
		}
	}
}>()
const data: dataT = reactive({
  animateMark: -1,
  unRead: false
})
const emit = defineEmits<{
  (event: 'item-click', itemInfo: Record<string, unknown>): void
}>()
const router = useRouter()
const showRedPoint = () => {
  if (props.msgItem.isFirst) {
    checkUnread({}).then(() => {
      data.unRead = true
    }, () => {
      data.unRead = false
    })
  }
}
showRedPoint()
onActivated(() => {
	showRedPoint()
})
// 卡片点击
const cardClick = (item: Record<string, unknown>, index?: number) => {
  if (data.animateMark !== -1) return false
  if (index !== undefined) {
    data.animateMark = index
  }
  emit('item-click', item)
  setTimeout(() => {
    data.animateMark = -1
  }, 600)
}
const goHistory = () => {
	data.unRead = false
	router.push('/pc/history')
}
const questionsClickHandle = (text: string) => {
	emit('item-click', {
		question_desc: text
	})
}
const { animateMark, unRead } = toRefs(data)
</script>

<style lang="scss" scoped>
.red_point {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  overflow: hidden;
  background: url("~@/assets/img/red_p.png") no-repeat;
  background-size: 100% 100%;
  float: right;
  margin-top: 2.668px;
  margin-left: 2.668px;
}
// 新增公告、热门问题
.hot-ann {
	overflow: hidden;
	width: 8.88rem;
	// 公告部分
	.ann-box {
		padding: 13.34px 34.684px 10.672px;
		.ann-title {
			overflow: hidden;
			.title-icon {
        width: 29.348px;
        height: 24px;
        margin-top: 5.336px;
        background-size: 100%;
        background-position: center;
        background-repeat: no-repeat;
        float: left;
        background-image: url('~@/assets/img/ann.png');
      }
      .title-text {
        font-size: 29.348px;
        height: 34.684px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #F5C133;
        line-height: 34.684px;
        float: left;
        margin-left: 10.672px;
      }
		}
		.ann-guide {
			font-size: 22.678px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
			color: #E2DFCF;
      line-height: 26.68px;
      margin-top: 8px;
      margin-bottom: 8px;
		}
		.ann-content {
			.text-ann {
				font-size: 22.678px;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #E2DFCF;
				line-height: 32.016px;
			}
			.question-ann {
        font-size: 22.678px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFDA76;
        line-height: 32px;
        span {
          display: inline-block;
          border-bottom: 1Px solid #FFDA76;
          padding-bottom: 4px;
          box-sizing: content-box;
					cursor: pointer;
        }
      }
		}
	}
	.ann-line {
		height: 1Px;
		width: 97%;
		margin: 0 auto;
		background-color: rgba(216, 216, 216, 0.2);
	}
	// 卡片展示
	.hasann-cardbox {
		.card-history {
      height: 42.668px;
      line-height: 42.668px;
      box-sizing: border-box;
      margin: 8px 24px 8px;
      padding: 0px 17.342px;
      background: linear-gradient(270deg, rgba(249, 247, 247, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
      .his-btn {
        font-size: 21.334px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #E2DFCF;
        float: right;
      }
      .more-icon {
        height: 18.676px;
        width: 18.676px;
        background-image: url('~@/assets/img/more.png');
        background-repeat: no-repeat;
        background-size: 100%;
        background-position: center;
        float: right;
        margin-top: 10.672px;
        margin-left: 1.334px;
      }
    }
		.card-scroll {
      margin: 0px 24px;
      width: calc(100% - 58.696px);
      padding: 0px;
      .card-scroll-item {
				margin-bottom: 0px;
				.card_item {
					display: inline-block;
					float: none;
					height: 160px;
					width: 213.44px;
					padding: 10.672px;
					margin: 10.672px;
					background: rgba(109, 109, 109, 0.15);
					.card_img {
						width: 112.056px;
						height: 112.056px;
						margin: 0 auto;
					}
					.card_text {
						width: 188.094px;
						margin-left: -93.38px;
						height: 40px;
						line-height: 40.687px;
						top: 90.712px;
						padding: 0px 18.676px 0px;
						font-size: 34px;
					}
				}
      }
    }
	}
	// 热门问题
	.hot-ques {
		margin: 13.34px 25.346px 10.672px;
		.ques-title {
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #F5C133;
      line-height: 38.686px;
      margin-bottom: 8px;
    }
    .ques-box {
      background: rgba(0, 0, 0, 0.3);
      border: 1Px solid #4E4540;
      padding: 8px 14.674px 0px;
      box-sizing: border-box;
      margin-top: 10.672px;
      .hot-questions-item {
        font-size: 22.678px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFDA76;
        line-height: 26.68px;
        margin-bottom: 16px;
        span {
          display: inline-block;
          text-decoration: underline;
          text-underline-offset: 0.05rem;
          padding-bottom: 4px;
          box-sizing: content-box;
          cursor: pointer;
        }
      }
    }
	}
}
// 新增公告、热门问题end
.card_box {
  margin-bottom: -20px;
  overflow: hidden;
  width: 1128.564px;
}
.card_title {
  font-size: 34.684px;
  margin-left: 13.34px;
  margin-top: 13.34px;
  color: #CECFC9;
  font-weight: 300;
  overflow: hidden;
  margin-bottom: 53.36px;
  .icon-w_hot {
    margin-right: 17.342px;
    font-size: 44.022px;
    float: left;
  }
}
.card_prompt {
  font-size: 34.684px;
  color: #CECFC9;
  font-weight: 300;
  margin-top: -26.68px;
  margin-bottom: 53.36px;
}
.card_item {
  width: 234.784px;
  height: 160px;
  float: left;
  margin: 0px 22.678px 76.038px;
  cursor: pointer;
  
  .card_img {
    width: 140px;
    height: 140px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
  }
  .card_text {
    width: 234.784px;
    left: 50%;
    margin-left: -117.392px;
    height: 45.356px;
    line-height: 45.356px;
    position: absolute;
    top: 113.39px;
    background: url('~@/assets/img/card_t.png') no-repeat;
    background-size: 100% 100%;
    background-position: center center;
    box-sizing: border-box;
    padding: 0px 22.678px 0px;
    font-size: 29.348px;
  }
}
.list_item {
  font-size: 32px;
  color: #F5C133;
  font-weight: 300;
  text-decoration: underline;
  line-height: 40px;
  margin-bottom: 40px;
  cursor: pointer;
}
@-webkit-keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  15% {
    -webkit-transform: scale3d(1.05, 0.75, 1);
    transform: scale3d(1.05, 0.75, 1);
  }

  30% {
    -webkit-transform: scale3d(0.75, 1.05, 1);
    transform: scale3d(0.75, 1.05, 1);
  }

  45% {
    -webkit-transform: scale3d(1.05, 0.85, 1);
    transform: scale3d(1.05, 0.85, 1);
  }

  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes rubberBand {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  15% {
    -webkit-transform: scale3d(1.05, 0.75, 1);
    transform: scale3d(1.05, 0.75, 1);
  }

  30% {
    -webkit-transform: scale3d(0.75, 1.05, 1);
    transform: scale3d(0.75, 1.05, 1);
  }

  45% {
    -webkit-transform: scale3d(1.05, 0.85, 1);
    transform: scale3d(1.05, 0.85, 1);
  }

  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }

  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
  -webkit-animation-duration: 0.6s;
  animation-duration: 0.6s;
}
@media all and (orientation : portrait) {
	.red_point {
		width: 24px;
		height: 24px;
		border-radius: 50%;
		overflow: hidden;
		background: url("~@/assets/img/red_p.png") no-repeat;
		background-size: 100% 100%;
		float: right;
		margin-top: 8px;
		margin-left: 2px;
	}
  .card_box {
    margin-bottom: -0.27rem;
    overflow: hidden;
    width: 10.1rem;
  }
  .card_title {
    font-size: 0.46rem;
    .icon-w_hot {
      font-size: 0.46rem;
    }
  }
  .card_prompt {
    font-size: 0.46rem;
  }
  .card_item {
    width: 2.5rem;
    height: 1.8rem;
    margin: 0px 0.43rem 0.5rem;
    .card_img {
      width: 1.78rem;
      height: 1.78rem;
    }
    .card_text {
      width: 2.5rem;
      margin-left: -1.25rem;
      height: 0.5rem;
      line-height: 0.5rem;
      font-size: 0.34rem;
      top: 1.43rem;
    }
  }
  .list_item {
    font-size: 0.43rem;
    line-height: 0.53rem;
    margin-bottom: 0.53rem;
  }
}
.history-btn {
	text-align: right;
}
</style>