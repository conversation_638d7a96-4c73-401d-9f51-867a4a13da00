<template>
  <dialog-wrapper>
    <div class="list_box">
      <div class="list_title">{{ props.msgItem.title ? props.msgItem.title : $t('text_choose') }}</div>
      <div class="list">
        <p :class="['list_item', item.isAbleClick === false ? 'disableItem' : '']" v-for="(item, index) in props.msgItem.content" :key="index" @click="clickHandle(item)">
          {{ index + 1 }}.  
          {{ item.question_desc }}
        </p>
      </div>
    </div>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, getCurrentInstance, ComponentInternalInstance } from 'vue'
export default defineComponent({
  name: 'List'
})
</script>
<script lang="ts" setup>
const { proxy: _this } = getCurrentInstance() as ComponentInternalInstance
const props = defineProps<{
  msgItem: Record<string, unknown> & {
    content: Array<Record<string, unknown>>,
    title?: string
  }
}>()
const emit = defineEmits<{
  (event: 'item-click', itemInfo: Record<string, unknown>): void
}>()
const clickHandle = (item: Record<string, unknown>) => {
  // 模糊匹配每个选项只允许点击一次
  if (item.isAbleClick === false) {
    return
  }
  item.isAbleClick = false
  _this?.$forceUpdate()
  emit('item-click', item)
}
</script>

<style lang="scss" scoped>
.list_box {
  margin-bottom: -26.68px;
  width: 1128.564px;
}
.list_title {
  color: #CECFC9;
  font-size: 34.684px;
  font-weight: 300;
  margin-bottom: 33.35px;
}
.list_item {
  font-size: 32.016px;
  color: #F5C133;
  font-weight: 300;
  text-decoration: underline;
  line-height: 40px;
  margin-bottom: 40px;
  cursor: pointer;
}
.disableItem {
  color: #8E897B;
}
@media all and (orientation : portrait) {
  .list_box {
    width: 10.1rem;
    margin-bottom: -0.4rem;
  }
  .list_title {
    font-size: 0.46rem;
  }
  .list_item {
    font-size: 0.43rem;
    line-height: 0.53rem;
    margin-bottom: 0.53rem;
  }
}
</style>