<template>
	<slot v-if="!loading">
	</slot>
	<van-loading v-else size="24px" color="#c59b29" type="spinner" vertical style="margin-top: 35px;">{{ $t('text_in_progress') }}</van-loading>
</template>
<script lang="ts">
	import { defineComponent, withDefaults, defineProps, onUpdated, defineEmits } from 'vue'
	interface Props {
		loading: boolean
	}
	export default defineComponent({
		name: 'FpLoading'
	})
</script>
<script lang="ts" setup>
	withDefaults(defineProps<Props>(), {
		loading: true
	})
	const emit = defineEmits<{
		(event: 'update-scroll'): void
	}>()
	onUpdated(() => {
		emit('update-scroll')
	})
</script>

<style lang="scss" scoped>

</style>