.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}

// 定制样式
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: transparent;
  background-image: url('~@/assets/img/koa/bg.webp');
  // 主页/聊天页背景图
  .main-role {
    background-image: url('~@/assets/img/koa/bg-role.webp');
    width: 360px !important;
    margin-right: 10px;
  }
  .my-swipe {
    .title {
      span {
        color: #F8EACB !important;
      }
    }
  }
  .page-arr {
    background-image: url('~@/assets/img/koa/icon-next.webp') !important;
    width: 25px !important;
    height: 30px !important;
  }
  .card-title {
    color: #8F9FB0 !important;
  }
  .home-articles .title span {
    color: #ECE3BA !important;
  }
  .home-bg {
    background-image: url('~@/assets/img/koa/bg-home.webp') !important;
    .home-cards {
      background: rgba(18,18,18,0.26) !important;
      box-shadow: 0px 0px 3Px 0Px rgba(188, 170, 141, 0.3);
      box-sizing: border-box !important;
      .card-scroll {
        padding: 0px 5px !important;
      }
      .card {
        background: rgba(13,18,23,0.3) !important;
      }
      .history-card {
        background: linear-gradient(180deg, rgba(47, 58, 68, 0.3) 0%, rgba(91, 108, 122, 0.3) 100%);
        .img-box {
          background: url('~@/assets/img/koa/history-btn.webp') no-repeat center center;
          background-size: 95% 95%;
        }
      }
    }
  }
  #app {
    .article-bg,
    .ticket-wrapper {
      @include backgroundSec('koa/bg-home.webp');
    }
  }
  .cs-body .title {
    color: #D4AD5B;
  }
  .user-input {
    .search-form {
      height: 47px !important;
      background: #A8AAAB !important;
      border: 1px solid #3E4C5E;
      .q-list {
        background: #A8AAAB !important;
        .q-item {
          color: #323A42 !important;
        }
      }
      input {
        height: 48px !important;
        color: #323A42 !important;
      }
    }
    .btn {
      max-width: 200px;
      color: #ffffff !important;
      font-weight: 500 !important;
      background: url('~@/assets/img/koa/btn-send.webp') center center no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  // 底部
  .bottom-wrap {
    background: #181E26;
    box-shadow: 0px -2px 1px 0px rgba(0,0,0,0.5);
    .hot-words {
      .item {
        .content {
          color: #8F9FB0 !important;
        }
      }
    }
    .hot-words .item:first-child {
      margin-left: -10px;
    }
    .bg-1, .bg-2, .bg-3 {
      background: url('~@/assets/img/koa/hot-word-bg.webp') !important;
    }
  }
  // 聊天页
  .gpt-a .rich-wrap {
    .rich-wrap-bg {
      // background: rgba(70,84,100,0.6) !important;
      border: none !important;
      &::after {
        // border-right-color: rgba(0,0,0,0.6) !important;
      }
    }
    .answer {
      color: #8F9FB0 !important;
    }
    .question {
      color: #DECDA6 !important;
    }
    .btn_correct {
      // background: #BBB7AE !important;
    }
  }
  .dislike-wrapper .reason-list .reason-submit {
    background: url('~@/assets/img/koa/btn-send.webp') center center no-repeat !important;
    background-size: 100% 100% !important;
    color: #ffffff !important;
  }
  .user-q .msg {
    background: rgb(70, 84, 100) !important;
    border: none !important;
    border-radius: 0 !important;
    color: #C3D2DE !important;
    &::after {
      border-left-color: rgb(70, 84, 100) !important;
    }
  }
  // 二级子卡页
  .second-level .sec-card {
    background: rgb(30, 39, 47) !important;
    dd {
      color: #8F9FB0 !important;
    }
  }
  // 首页文章列表
  .home-articles .article-list .item {
    color: #8F9FB0 !important;
    // background: rgba(8,8,8,0.36) !important;
  }
  .home-articles .article-list .item.readed {
    color: #7990a8 !important;
  }
}
// 其它页公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  // 其它页公共背景图
  .main-role {
    height: 610px;
    width: 420px !important;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-left: -210px;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}
// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('koa/btn-send.webp');
	font-family: Adobe Heiti Std;
	font-weight: normal;
	font-size: 28px;
	color: #ffffff;
}

a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}
:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button {
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active {
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn {
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active {
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable {
    filter: grayscale(1);
  }
}

// 定制css样式
#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #ECE3BA !important;
  
  .chat-item {
    .chat-customer {
      background-image: url('~@/assets/img/koa/icon-gpt.webp') !important;
    }
    .chat-player {
      background-image: url('~@/assets/img/koa/icon-avatar.webp') !important;
    }
  }
  .user-q {
    .avatar {
      right: 14px !important;
      border-radius: 0% !important;
      &.default {
        background-image: url('~@/assets/img/koa/icon-avatar.webp') !important;
      }
    }
  }
  .gpt-a {
    .avatar {
      left: 14px !important;
      border-radius: 0% !important;
      background-image: url('~@/assets/img/koa/icon-gpt.webp') !important;
    }
  }
  // 历史服务记录页
  .ticket-wrapper {
    background: rgba(0,0,0,0.4);
    backdrop-filter: blur(4px);
    // 查看按钮
    .more {
      background: url('~@/assets/img/koa/btn-send.webp') no-repeat center center;
      background-size: 100% 100%;
      color: #ffffff;
    }
    .history-content .tableList {
      .row {
        background: rgba(0,0,0,0.24);
        .item {
          color: #8F9FB0;
        }
      }
      .head {
        background: rgb(71,82,95);
        box-shadow: 0px 2px 0px 0px rgba(0,0,0,0.5);
        .item {
          color: #C3D2DE;
        }
      }
    }
  }
  // 工单详情页
  .content {
    color: #C3D2DE !important;
  }
  .form-content {
    color: #C3D2DE !important;
  }
  // 文章详情页
  .article-title, .main-title {
    color: #DECDA6 !important;
  }
  .context {
    color: #8F9FB0;
  }
  // 评价页
  .appraise {
    color: #DECDA6 !important;
  }
  // 表单填写页
  .form-wrapper {
    color: #ECE3BA;
    .form-label {
      color: #C3D2DE !important;
    }
    .van-field__control {
      color: #C3D2DE;
    }
    .van-field__body {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #8F9FB0;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #8F9FB0;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #8F9FB0;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #8F9FB0;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #8F9FB0;
      }
    }
    .van-cell {
      border: none;
    }
  }
  // 重开工单
  .van-collapse-item__content .ticket-wrapper {
    background: none;
    backdrop-filter: none;
  }
}

// loading
.loading-icon {
  display: none;
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}

// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('koa/btn-send.webp');
	width: 45%;
	color: #ffffff;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  border: 2px solid #BBB7AE;
	width: 45%;
	color: #cecfc9;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 1058px !important;
  }
}

// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
}