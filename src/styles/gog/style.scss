.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}

// 定制样式
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: transparent;
  background-image: url('~@/assets/img/gog/bg.jpg');
  // 主页/聊天页背景图
  .main-role {
    background-image: url('~@/assets/img/gog/bg-role.png');
    width: 360px !important;
    margin-right: -10px;
  }
  .container.show {
    left: -98px;
  }
  .main {
    .container {
      left: -98px;
    }
  }
  .nav-item {
    background-image: url('~@/assets/img/gog/bg-nav.png') !important;
    margin-bottom: 20px !important;
    font-size: 20px !important;
    color: #BBB7AE !important;
    font-weight: 500 !important;
  }
  .nav-item.active-nav {
    color: #3D2C17 !important;
    background-image: url('~@/assets/img/gog/bg-nav-active.png') !important;
  }
  .my-swipe {
    .title {
      span {
        color: rgba(231, 200, 131, 1) !important;
      }
    }
  }
  .page-arr {
    background-image: url('~@/assets/img/gog/icon-next.png') !important;
    width: 40px !important;
    height: 40px !important;
  }
  .card-title {
    color: #ECE3BA !important;
  }
  .home-articles .title span {
    color: #ECE3BA !important;
  }
  .home-articles {
    .title {
      span {
        &::before,
        &::after {
          top: 12px !important;
          background-image: url('~@/assets/img/gog/bg-title-icon.png') !important;
        }
      }
    }
  }
  .home-bg {
    background-image: url('~@/assets/img/gog/bg-home.png') !important;
    .home-cards {
      background: rgba(48, 43, 36, 0.3) !important;
      box-shadow: 0px 0px 3Px 0Px rgba(188, 170, 141, 0.3);
      box-sizing: border-box !important;
      .card-scroll {
        padding: 0px 5px !important;
      }
      .card {
        background: none !important;
      }
      .history-card {
        height: 120px !important;
        background: url('~@/assets/img/gog/bg-his-card.png') no-repeat center center !important;
        margin-top: 0px !important;
        transform: scale(1) !important;
        .img-box {
          background: url('~@/assets/img/gog/history-btn.png') no-repeat center center;
          background-size: 95% 95%;
        }
      }
    }
  }
  .home-articles .article-list .item .new {
    background-image: url('~@/assets/img/gog/new.png') !important;
  }
  .home-articles .article-list .item .hot {
    background-image: url('~@/assets/img/gog/hot.png') !important;
  }
  #app {
    .bottom-wrap {
      background: #1A1511 !important;
    }
    .article-bg,
    .ticket-wrapper {
      // background-image: url('~@/assets/img/gog/bg-home.png') !important;
      @include backgroundSec('gog/bg-home.png');
    }
  }
  .cs-body .title {
    color: #D4AD5B;
  }




  .user-input {
    .search-form {
      height: 47px !important;
      background-image: url('~@/assets/img/gog/bg-search.png') !important;
      border: none !important;
      .q-list {
        background: #434343 !important;
        .q-item {
          color: #BBB7AE !important;
        }
      }
      input {
        height: 48px !important;
        color: #92806A !important;
      }
    }
    .btn {
      max-width: 200px;
      color: #ffffff !important;
      font-weight: 500 !important;
      background: url('~@/assets/img/gog/btn-send.png') center center no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  #app {
    .hot-words {
      .hot-item {
        .content {
          color: #3D2C17 !important;
        }
        .bg-1, .bg-2, .bg-3 {
          background: #C9AB71;
        }
      }
      .item {
        .content {
          color: #BBB7AE !important;
        }
        .bg-1, .bg-2, .bg-3 {
          background: #282522;
        }
      }
    }
  }
  
  .gpt-a .rich-wrap {
    .rich-wrap-bg {
      background: rgba(0,0,0,0.6) !important;
      border: none !important;
      &::after {
        border-right-color: rgba(0,0,0,0.6) !important;
      }
    }
    .answer {
      color: #BBB7AE !important;
    }
    .question {
      color: #FFE69B !important;
    }
    .btn_correct {
      background: #BBB7AE !important;
    }
  }
  // 赞踩
  .wrap {
    .like {
      background-image: url('~@/assets/img/gog/icon_kudos.png') !important;
      background-size: 46% 80% !important;
      background-position: center center !important;
      margin-right: -15px;
      &.active {
        background-image: url('~@/assets/img/gog/icon_kudos1.png') !important;
      }
    }
    .dislike {
      background-image: url('~@/assets/img/gog/icon_kudos.png') !important;
      transform: rotateX(180deg);
      background-size: 46% 80% !important;
      background-position: center center !important;
      &.active {
        background-image: url('~@/assets/img/gog/icon_kudos1.png') !important;
      }
    }
  }
  .dislike-wrapper .reason-list .reason-submit {
    background: url('~@/assets/img/gog/btn-send.png') center center no-repeat !important;
    background-size: 100% 100% !important;
    color: #ffffff !important;
  }
  .user-q .msg {
    background: rgba(0, 0, 0, 0.6) !important;
    border: none !important;
    border-radius: 0 !important;
    color: #BBB7AE !important;
    &::after {
      border-left-color: rgba(0, 0, 0, 0.6) !important;
    }
  }
  .second-level .sec-card {
    background: rgba(122, 108, 84, 0.14) !important;
    dd {
      color: #ECE3BA !important;
    }
  }
  .home-articles .article-list .item {
    color: #BBB7AE !important;
    background: rgba(92, 77, 47, 0.12) !important;
  }
  .home-articles .article-list .item.readed {
    color: #BBB7AE !important;
  }
}
.article-title, .main-title {
  color: #FFE69B !important;
}
.form-label {
  color: #ECE3BA !important;
}
.form-content {
  color: #BBB7AE !important;
}
// 其它页公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  // 其它页公共背景图
  .main-role {
    height: 610px;
    width: 420px !important;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-left: -210px;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}
// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('gog/btn-send.png');
	font-family: Adobe Heiti Std;
	font-weight: normal;
	font-size: 28px;
	color: #ffffff;
}
.divider, .bottom-line {
  background-image: url('~@/assets/img/gog/bg-line.png') !important;
}

a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}
:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button {
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active {
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn {
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active {
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable {
    filter: grayscale(1);
  }
}
#app .btn_self_servies {
  @include backgroundSec('gog/btn-self-servies.png');
  border: none;
  & .auto-font-size span {
    color: #ffffff !important;
    &::before {
      background-image: url('~@/assets/img/gog/icon_robot.png') !important;
      transform: translateY(-50%);
    }
  }
}

// 定制css样式
#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #ECE3BA !important;
  
  .chat-item {
    .chat-customer {
      background-image: url('~@/assets/img/gog/icon-gpt.jpg') !important;
    }
    .chat-player {
      background-image: url('~@/assets/img/gog/icon-avatar.jpg') !important;
    }
  }
  .user-q {
    .avatar {
      right: 14px !important;
      border-radius: 0% !important;
      &.default {
        background-image: url('~@/assets/img/gog/icon-avatar.jpg') !important;
      }
    }
  }
  .gpt-a {
    .avatar {
      left: 14px !important;
      border-radius: 0% !important;
      background-image: url('~@/assets/img/gog/icon-gpt.jpg') !important;
    }
  }
  // 底部
  .bottom-wrap {
    background: #272523;
    box-shadow: 0px -2px 1px 0px rgba(0,0,0,0.5);
    .hot-words .item:first-child {
      margin-left: -10px;
    }
  }
  // 历史服务记录页
  .ticket-wrapper {
    background: rgba(0,0,0,0.4);
    backdrop-filter: blur(4px);
    // 查看按钮
    .more {
      background: url('~@/assets/img/gog/btn-send.png') no-repeat center center;
      background-size: 100% 100%;
      color: #ffffff;
    }
    .history-content .tableList {
      .row {
        background: rgba(92, 77, 47, 0.12) !important;
        .item {
          color: #BBB7AE;
        }
      }
      .head {
        background: #48392a;
        box-shadow: 0px 2px 0px 0px rgba(0,0,0,0.5);
        .item {
          color: #ECE3BA;
        }
      }
    }
  }

  .content-box {
    .content {
      .head {
        background: rgba(72, 57, 42, 1) !important;
        .item {
          color: #ECE3BA !important;
          font-weight: 500;
        }
      }
      .row {
        &:nth-child(odd) {
          background: rgba(92, 77, 47, 0.32);
        }
        .item {
          color: #BBB7AE;
        }
      }
    }
  }

  // 工单详情页
  .content {
    color: #BBB7AE !important;
    
  }
  .historyRecordsFont span {
    color: #54760b !important;
  }
  // 文章详情页
  .context {
    color: #BBB7AE;
  }
  // 评价页
  .appraise {
    color: #ECE3BA !important;
  }
  .tips-gohome {
    color: #BBB7AE;
    span {
      color: #54760b;
    }
  }
  // 表单填写页
  .form-wrapper {
    color: #ECE3BA;
    .van-field__control {
      color: #BBB7AE;
    }
    .van-field__body {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #a09b91;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #a09b91;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #a09b91;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #a09b91;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #a09b91;
      }
    }
    .van-cell {
      border: none;
    }
    .img-wrap .img-upload,
    .video-wrap .video-upload,
    .pc-box {
      border: 1px solid #817042;
	    background: url('~@/assets/img/plus.png') no-repeat center;
    }
  }
  // 重开工单
  .van-collapse-item__content .ticket-wrapper {
    background: none;
    backdrop-filter: none;
  }
}

// loading
.loading-icon {
  display: none;
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}

// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('gog/btn-send.png');
	width: 45%;
	color: #ffffff;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  border: 2px solid #BBB7AE;
	width: 45%;
	color: #cecfc9;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 1058px !important;
  }
}

// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
  .history-bar {
    background: rgba(92, 77, 47, 0.2) !important;
  }
  .container.show {
    left: 0px !important;
  }
  .main {
    .container {
      left: 0px !important;
    }
  }
}