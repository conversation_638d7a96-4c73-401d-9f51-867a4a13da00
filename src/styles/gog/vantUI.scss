:root:root {
  --view-safe-left: 122px;
  --van-white: #fff;
  --van-blue: #EADDBF;
  --van-button-primary-color: var(--van-white);
  --van-button-primary-background: var(--van-primary-color);
  --van-slider-button-width: 27px;
  --van-slider-button-height: 54px;
  --van-slider-bar-height: 24px;
  // --van-slider-active-background: url('~@/assets/img/soc/bg-slider.jpeg') ;
  // --van-slider-inactive-background: url('~@/assets/img/soc/bg-slider-before.png') center center no-repeat;
  --van-radius-max: 0;
  // --van-slider-button-background: url('~@/assets/img/soc/icon-slider.png') center center no-repeat;
  --van-toast-background: url('~@/assets/img/soc/bg-toast.png') center center no-repeat;
  --van-toast-max-width: auto;
  --van-toast-default-min-height: 48px;
  --van-toast-position-top-distance: 80px;
  --van-toast-radius: 0;
  --van-toast-font-size: 24px;
  --van-toast-text-padding: 0;
  --van-overlay-z-index: 10;
}
.van-slider__bar {
  background-size: 750px 14px !important;
  background-repeat: no-repeat !important;
  background-position: 5px 5px !important;
}
.van-slider__button {
  background-size: 100% 100% !important;
}
.van-toast--text {
  height: 67px !important;
  width: 100% !important;
  top: 108px !important;
  background-size: auto 100% !important;
}