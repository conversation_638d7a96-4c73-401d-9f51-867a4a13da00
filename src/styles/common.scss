#t-wrap {
  .history-btn-box {
    display: flex;
    justify-content: flex-end;
    .bar-title {
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      .more-icon {
        display: inline-block;
        width: 14PX;
        height: 14PX;
        border-style: solid;
        border-width: 3PX 3PX 0 0;
        transform: rotate(45deg);
        margin: 5px;
      }
    }
    .red-p {
      position: absolute;
      top: 15px;
      right: 35px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      @include backgroundSec('red_p.png');
    }
  }
}

@media (orientation: portrait) {
  #app #t-wrap {
    .history-btn-box {
      .bar-title {
        .more-icon {
          transform: rotate(45deg) scale(1.2);
        }
      }
    }
  }
}
