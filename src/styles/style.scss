@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
	#app {
		/* ios < 11.2 */
		padding-left: constant(safe-area-inset-left);
		padding-top: constant(safe-area-inset-top);
		padding-right: constant(safe-area-inset-right);
		padding-bottom: constant(safe-area-inset-bottom);
		/* ios >= 11.2 */
		padding-left: env(safe-area-inset-left);
		padding-top: env(safe-area-inset-top);
		padding-right: env(safe-area-inset-right);
		padding-bottom: env(safe-area-inset-bottom);
	}
}
#app {
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: transparent;
  // background-image: url('~@/assets/img/csbg.jpg');
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
	font-family: 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
	// 解决字体放大兼容问题
	-webkit-text-size-adjust: none !important;
	-moz-text-size-adjust: none !important;
	-ms-text-size-adjust: none !important;
	// 富文本居中
	.ql-align-center {
		text-align: center;
	}
	// 富文本居右
	.ql-align-right {
		text-align: right;
	}
}
.clearfix:after {
  content: " ";
  display: block;
  clear: both;
  height: 0;
}
// #app:after{
//     content: "";
//     width:100%;
//     height:100%;
//     position: absolute;
//     left:0;
//     top:0;
//     background: inherit;
//     filter: blur(4Px);
// 		z-index: 1;
// }
// @media all and (orientation : landscape) {
//   #app {
//     background-image: url('~@/assets/img/csbg.jpg');
//   }
// }
@media all and (orientation : portrait){
  #app {
    background: #f8f8f8 !important;
    .ticket-wrapper {
      .content {
        overflow: auto;
      }
    }
    .tips-gohome {
      span {
        color: #f06b29;
      }
    }
    .ann-title {
      .title-icon {
        background-image: url('~@/assets/img/ann-por.png');
      }
      .title-text {
        color: #000;
        font-weight: 600;
      }
    }
    .ann-guide {
      color: #000;
      font-weight: 500;
      padding-left: 5Px;
    }
    .ann-content {
      .text-ann {
        color: #787878;
        padding-left: 5Px;
      }
    }
    .ann-line {
      background-color: rgba(252, 104, 5, 0.2);
    }
    .rich_eval_box {
      border-top: 1Px solid rgba(252, 104, 5, 0.2);
    }
    .btn.like.active, .btn.dislike.active {
      background-image: url('~@/assets/img/icon_kudos1_por.png');
    }
    .dislike-wrapper {
      .title {
        border-bottom: 1Px solid rgba(252, 104, 5, 0.2);
      }
    }
    .reason-list {
      .reason-item {
        color: #000;
        font-weight: normal;
        text-shadow: none;
        padding-left: 2Px;
      }
      .reason-submit {
        background: #fbf0e9;
        color: #f06b29;
        font-size: 0.4rem;
        padding: 0.1rem 0.2rem;
        border-radius: 8Px;
      }
    }


    .card-history {
      .his-btn {
        color: #787878;
        font-weight: 600;
      }
    }
    .card-scroll-item .card_item {
      background: none;
      width: 2.3rem;
    }
    .card_item {
      .card_text {
        color: #000;
        top: 1.7rem;
      }
    }
    .list_item {
      color: #f06b29;
    }
    .list_title {
      color: #000;
      font-weight: 500;
    }
    .ques-box {
      background: #fff;
      border: none;
      .ques-title {
        color: #000;
        font-weight: 600;
      }
      .hot-questions-item {
        color: #d46f3d;
        padding-left: 5Px;
        span {
          // text-decoration: none;
          font-weight: 400;
        }
      }
    }
    .mini-button-yes {
      background: #f06b29;
    }
    .mini-button-no {
      background: #fbf0e9;
      border: 1Px solid #f06b29;
      color: #787878;
    }
    .button-wrapper {
      overflow: visible;
    }
    .appraise {
      margin-top: 10Px;
    }


    .history-btn {
      font-weight: bold;
      color: #787878;
    }
    .tickets_enter {
      color: #f06b29 !important;
    }
    .smart_btn {
      background: #f06b29;
      color: #fff;
      font-weight: bold;
      letter-spacing: 2Px;
      text-indent: 2Px;
      border-radius: 10Px;
    }
    .back_top {
      background-image: url("~@/assets/img/back_top_por.png");
      background-size: cover;
    }

    .van-loading__text {
      color: #f06b29 !important;
    }
    .van-loading__spinner i {
      color: #f06b29 !important;
    }

    .ticket-wrapper {
      color: #000;
      font-family: 'PingFang SC';
      background-color: #fff;
      .history-btn-box {
        .history-btn {
          margin-top: 10px;
          margin-right: 30px;
        }
      }
    }
    .leftTopIcon {
      .history-btn-box {
        margin-top: -15px;
      }
    }
    .row.head {
      background: #fbf0e9;
      .item {
        border-bottom: 0Px;
      }
    }
    .more {
      background: #f06b29;
      box-shadow: none;
    }
    .row .item {
      border-bottom: 1Px dashed #57544e;
    }
    .content .title {
      color: #000;
      text-shadow: none;
      font-weight: 600;
      border-left: 4Px solid #f06b29;
      background: rgba(255, 255, 255, 0);
      img {
        // display: none;
      }
    }
    .text-form-box {
      background-color: #f6f6f6;
      border-radius: 30px;
      border: 0px;
      padding: 25px 25px;
      color: #787878;
    }
    .historyRecordsFont span {
      color: #f06b29;
    }
    .form-label {
      .icon {
        display: none;
      }
    }
    .form, .form-wrapper {
      overflow: visible;
      width: 95%;
      margin: 0 auto;
      .form-label {
        color: #000;
        font-weight: 500;
      }
      .van-cell {
        background-color: #f6f6f6;
        border-radius: 30px;
        border: 1Px solid rgba(252, 104, 5, 0.2);
        // padding: 25px 25px;
        color: #787878;
        .van-field__control {
          color: #787878;
        }
      }
      .van-field__right-icon img {
        display: none;
      }
    }
    .t-items {
      .title {
        color: #000;
        font-weight: 600;
        background: rgba(255, 255, 255, 0);
        border-left: 4Px solid #f06b29;
        img {
          display: none;
        }
      }
      .title::before {
        display: none;
      }
      .title::after {
        display: none;
      }
      .list-item {
        color: #000;
        font-weight: 500;
        line-height: 70px;
        img {
          display: none;
        }
        span::before {
          content: '';
          margin-left: 10Px;
          margin-right: 5Px;
          display: inline-block;
          height: 0px;
          width: 0px;
          border-top: 20px solid transparent;
          border-bottom: 20px solid transparent;
          border-left: 40px solid #f06b29;
          color: #f06b29;
        }
      }
    }
    .img-upload, .video-upload, .pc-box {
      border: 1Px solid rgba(252, 104, 5, 0.2);
      background-color: #fff;
      background: url("~@/assets/img/plus-por.png") no-repeat center;
    }
    .delete {
      background: url("~@/assets/img/delete-por.png") no-repeat;
      background-size: cover;
        background-position: center;
        background-color: rgba(0, 0, 0, 0.4);
    }
    .fp-button-box {
      box-shadow: none;
      line-height: 157px;
      height: 160px;
      .fp-button {
        background: #f06b29;
        border: 0px;

      }
    }
    .emptybox .a {
      color: #f06b29;
    }
  }
}
// 遮罩
.f-overlay {
	background: rgba(0, 0, 0, 0.6);
}
.cs-body {
	height: 100%;
	width: 100%;
	overflow: auto;
	position: absolute;
	left:0;
	top:0;
	z-index: 2;
	padding-top: var(--head-bar-height);

	.position-rel {
		position: relative;
		height: 100%;
		width: 100%;
	}
}
.ticket-bg {
	position: relative;
	margin: 0 auto;
	height: 100%;
	background: linear-gradient(to bottom, rgba(144, 144, 145, 0.2), rgba(255,255,255, 0));
	overflow: auto;
	padding: 40px;

	.ticket-wrapper {
		color: #c2a862;
		font-family: 'PingFang SC';
	}
	.t-items {
		box-sizing: border-box;
		padding-bottom: 26.68px;
	}
	.form-label {
		line-height: 53.36px;
		margin: 26.68px 0 16px;
		word-break: normal;
		&.required {
			&::after {
				content: '*';
				color: #ee0a24;
				text-shadow: none;
				margin: -5.336px 0 0 5.336px;
			}
		}
		&.required-left {
			&::before {
				content: '*';
				color: #ee0a24;
				text-shadow: none;
				margin: -10.672px 10.672px 0 0 ;
			}
		}
		.icon {
			float: left;
			width: 53.36px;
			height: 53.36px;
			margin-right: 2Px;
			opacity: 0.6;
			background: url('../assets/img/label.png') no-repeat;
			background-size: cover;
		}
	}
	.form-caption {
		font-family: Arial;
		font-size: 32px;
		margin-left: 32px;
		color: #9b895a ;
		line-height: 42.668px;
		.icon {
			float: left;
			width: 37.352px;
			height: 37.352px;
			opacity: 0.5;
			margin-right: 3Px;
			background: url('../assets/img/tips.png') no-repeat;
			background-size: cover;
		}
	}
}
// 智能客服（横版）
#s-container {
	margin: 0 auto;
	height: calc(100% - 109.388px);
	overflow: auto;
	padding: 0px 106.72px;
	#s-wrap {
		color: #c2a862;
		font-family: 'PingFang SC';
	}
}
.input_box {
	height: 109.338px;
	background-color: #3C3F46;
	padding: 18.676px 106.72px;
	box-sizing: border-box;
}

// 智能客服（横板）end
// landscape横屏
@media all and (orientation : landscape) {
  .ticket-bg {
    width: 97%;
		padding: 26.68px;
  }
}
// portrait竖屏
@media all and (orientation : portrait) {
  .ticket-bg {
    width: 95%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
		.form-label {
			margin: 40px 0px 15px;
			line-height: 60px;
			.icon {
				width: 60px;
				height: 60px;
			}
		}
		.form-caption {
			font-size: 32px;
			margin-left: 32px;
			line-height: 40px;
			.icon {
				width: 32px;
				height: 32px;
			}
		}
		.fp-button-box {
			height: 100px;
			line-height: 104px;
			font-size: 56px;
			margin-top: 80px;
		}
		.mini-button-box {
			height: 80px;
			width: 58%;
			line-height: 80px;
			font-size: 0.3rem;
		}
	  .mini-button-left-text {
			line-height: 80px;
			font-size: 0.42rem;
		}
  }
	#s-container {
		padding: 0px 0.5rem;
		height: calc(100% - 1.61rem);
		padding: 0px 0.43rem;
	}
	.input_box {
		height: 1.61rem;
		padding: 0.268rem 0.43rem;
    background: #fff;
	}
}

// button特效
.fp-button-box {
	position: relative;
	height: 93.38px;
	width: 80%;
	margin: 40px auto 26.68px;
	overflow: hidden;
	line-height: 96px;
	border-radius: 3Px;
	box-shadow: 1Px 2Px 4Px 1Px rgba(0, 0, 0, 0.4);
	font-size: 42.688px;
}
.fp-button-push {
	box-shadow: inset 1Px 1Px 5Px 4Px rgba(0, 0, 0, 0.9);
	.fp-button {
		opacity: 0.8;
	}
}
.fp-button {
	cursor: pointer;
	border-radius: 2Px;
	letter-spacing: 3Px;
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%,-50%);
	background: -webkit-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -moz-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -o-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -ms-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: linear-gradient(to bottom, #AA8821, #F5CE61);
	width: 100%;
	height: 100%;
	text-align: center;
	text-transform: uppercase;
	z-index: 1;
	color: #fff;
	font-family: Arial, sans-serif;
}

// 文字按钮排一行
.mini-button-box {
	height: 66.7px;
	line-height: 66.7px;
	width: 36%;
	font-size: 0.2rem;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 0.23rem;
	line-height: 66.7px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	box-shadow: 1Px 2Px 4Px 1Px rgba(0, 0, 0, 0.4);
	background: -webkit-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -moz-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -o-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: -ms-linear-gradient(to bottom, #AA8821, #F5CE61);
	background: linear-gradient(to bottom, #AA8821, #F5CE61);
	width: 45%;
	color: #fff;
	font-family: Arial, sans-serif;
}
.mini-button-no {
	cursor: pointer;
	background-color: rgba(194, 187, 167, 0.09);
  border-radius: 5px;
  border: 2px solid rgba(245, 193, 51, 0.5);
	width: 45%;
	color: #cecfc9;
	font-family: Arial, sans-serif;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}
// 阿语适配
#app .lang_ar {
	transform: scaleX(-1);
	.history-btn {
		transform: scaleX(-1);
	}
	span {
		transform: scaleX(-1);
		display: inline-block;
	}
	textarea, .text-form-box, input {
		transform: scaleX(-1);
		text-align: right;
	}
	.van-ellipsis {
		transform: scaleX(-1);
	}
	button {
		transform: scaleX(-1);
	}
	.van-field__word-limit {
		transform: scaleX(-1);
		text-align: left;
		.van-field__word-num {
			transform: none;
		}
	}
	.title-time {
		transform: none;
	}
	.fp-button-box, .emptybox {
		transform: scaleX(-1);
	}
	.label-container {
		.label {
			transform: scaleX(-1);
		}
	}
	.img-container {
		img {
			transform: scaleX(-1);
		}
	}
	.van-image-preview {
		transform: scaleX(-1);
	}
	.video-pre {
		transform: scaleX(-1);
	}
	.evidence-tips {
		transform: scaleX(-1);
		direction: rtl;
		text-align: right;
		.time {
			direction: ltr;
			transform: scaleX(1);
		}
	}
	.close-tips {
		transform: scaleX(-1);
		direction: rtl;
	}
	.tableList {
		.row {
			.item {
				transform: scaleX(-1);
			}
		}
	}
}
// 滚动条定制样式
.card-scroll {
	overflow-x: scroll;
	height: auto;
	width: 100%;
	overflow-y: hidden;
	/*解决ios上滑动不流畅*/
	-webkit-overflow-scrolling: touch;

	&::-webkit-scrollbar {
		display: block;
		width: 8px;
		height: 8px;
		-webkit-border-radius: 8px;
		-moz-border-radius: 8px;
		border-radius: 8px;
	}

	&::-webkit-scrollbar-button {
		width: 5px;
		display: block;
		visibility: hidden;
	}

	&::-webkit-scrollbar-thumb {
		background-color: rgba(91, 99, 105, 0.6);
		-webkit-border-radius: 6px;
		-moz-border-radius: 6px;
		border-radius: 6px;
	}

	.card-scroll-item {
		white-space: nowrap;
	}
}

// 按钮动画
.btnPush {
	box-shadow: inset 1Px 1Px 5Px 2Px rgba(0, 0, 0, 0.6) !important;
	opacity: 0.8 !important;
	transform: scale(0.98);
}

.item-card-wrap .issues-item-wrap .btnPush {
	box-shadow: none !important;
	opacity: 0.9 !important;
	transform: scale(0.94);
}


.history-bar .bar-title {
	color: #B4CCE4;
}
