*{
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none; 
  }
}
.pc_box {
  * {
    scrollbar-width: auto;

    &::-webkit-scrollbar {
      display: block;
    }
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: auto;
  position: relative;
  background: transparent;
  font-family: "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}
a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}

:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.btn{
  // transition: all .3s;
  &:active{
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
}
