@font-face {
  font-family: 'tsTitle';
  src: url('./LINESeedSans_A_He.otf') format('truetype');
  font-display: swap;
}
@font-face {
  font-family: 'tsContent';
  src: url('./Roboto-Bold.ttf') format('truetype');
  font-display: swap;
}
.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "tsContent", "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif !important;
}

// 定制样式
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: transparent;
  background-image: url('~@/assets/img/ts/bg.webp');
  // 主页/聊天页背景图
  .main-role {
    display: none;
  }
  .article-bg {
    background-image: url('~@/assets/img/ts/home-bg.webp');
    .content {
      .van-form {
        font-family: "tsContent", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
      }
    }
  }
  // 文章页
  .answers-detail {
    .article-bg {
      @include backgroundSec('ts/bg.webp');
      color: #f2ebdd;
    }
  }
  .home-bg {
    background-image: url('~@/assets/img/ts/home-bg.webp');
    .home-cards {
      .card {
        background: #2F6E9E !important;
      }
      .history-card {
        background: #2F6E9E !important;
        .img-box {
          // background: url('~@/assets/img/ss/history-btn.webp') no-repeat center center;
          background-size: 95% 95%;
        }
      }
    }
  }
  .user-input {
    .search-form {
      padding: 0 10px !important;
      height: 47px !important;
      background: #0F3859 !important;
      border-radius: 8px;
      .q-list {
        background: #0F3859 !important;
        border-radius: 10px;
        .q-item {
          font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          color: #f2ebdd !important;
        }
      }
      input {
        height: 46px !important;
        line-height: 1em !important;
        color: #f2ebdd !important;
      }
    }
    .btn {
      color: #f2ebdd !important;
      max-width: 200px;
      background: url('~@/assets/img/ts/btn-send.webp') center center no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .hot-words {
    .item {
      .content {
        color: #9C7C52 !important;
      }
    }
  }
  .gpt-a {
    padding-left: 100px !important;
    .rich-wrap {
      .question {
        color: #f8ac3d !important;
        font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
      }
      .rich-wrap-bg {
        background: rgb(50, 92, 142) !important;
        border-radius: 10px;
        &::after {
          border-right-color: rgb(50, 92, 142) !important;
        }
      }
  
      .answer {
        font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
        color: #f2ebdd !important;
      }
  
      .btn_correct {
        font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
        background: #f2ebdd !important;
      }

      .rich-body {
        .problem-box .problem {
          color: #f2ebdd !important;
          border: 1px solid #f8ac3d !important;
        }
        .reason-list {
          .reason-item {
            color: #f2ebdd !important;
          }
          .reason-submit {
            background: rgb(70,170,230) !important;
            color: #f2ebdd !important;
          }
        }
        .reason-end {
          color: #f2ebdd !important;
        }
      }
    }
  }
  // 聊天页赞踩
  .wrap {
    .like {
      background-image: url('~@/assets/img/ts/icon_kudos.webp') !important;
      background-size: 70% 110% !important;
      margin-right: -15px;
      &.active {
        background-image: url('~@/assets/img/ts/icon_kudos.webp') !important;
      }
    }
    .dislike {
      background-image: url('~@/assets/img/ts/icon_kudos.webp') !important;
      transform: rotateX(180deg);
      background-size: 70% 110% !important;
      margin-top: -10px;
      &.active {
        background-image: url('~@/assets/img/ts/icon_kudos.webp') !important;
      }
    }
  }
  .user-q {
    padding-right: 100px !important;
    .msg {
      font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
      font-weight: normal !important;
      color: #2e5c8b !important;
      background: #C2ECFC !important;
      &::after {
        border-left-color: #C2ECFC !important;
      }
    }
  }
  .second-level .sec-card {
    background: #2F6E9E !important;
    border-radius: 15px;
  }
}
// 其它页公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  // 其它页公共背景图
  .main-role {
    display: none;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}
// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('ts/btn-send.webp');
	font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
	font-weight: normal;
	font-size: 28px;
	color: #f2ebdd;
}

a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}
:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button {
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active {
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn {
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active {
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable {
    filter: grayscale(1);
  }
}

// 定制css样式
#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #f8ac3d;
  line-height: 30px;

  .text-form-box {
    border: 1px solid #6a5ba4;
  }

  .video-preview, .img-wrap {
    .delete {
      background: url("~@/assets/img/ts/delete.webp") no-repeat !important;
      background-size: cover !important;
      background-position: center !important;
    }
  }

  .tips-gohome {
    color: #5f91a8;
    span {
      color: #f2ebdd;
    }
  }
  .article-title, .article-body {
    font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
  }
  .article-title {
    color: #f2ebdd;
    background: #1D2C4D;
    border-radius: 43px;
    min-height: 38px;
  }
  .article-link {
    color: #4d3b6f;
    margin-left: 5px;
    margin-right: 5px;
  }
  .web-link {
    color: #ffeb4e;
    margin-left: 5px;
    margin-right: 5px;
  }
  .game-goto {
    border: none;
    background: #eeebff !important;
    color: #8473cf;
    border-radius: 5Px;
  }
  .context {
    font-family: "tsContent", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
    .btn {
      margin-left: 8px;
      margin-right: 8px;
    }
  }
  .form-wrapper .van-cell {
    border: none !important;
  }
  .tickets_enter {
    color: #f2ebdd !important;
  }
  .main-title {
    color: #f2ebdd !important;
    font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
  }
  .title.reply {
    img {
      display: none;
    }
  }

  .historyRecordsFont span {
    color: #f2ebdd !important;
  }

  .answers-detail {
    .content {
      // 展开折叠栏背景
      .title {
        color: #f2ebdd;
        background-image: url('~@/assets/img/ts/select.webp');
        background-size: 100% 100%;
        font-family: "tsContent", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
        // 展开折叠栏 图标
        .nav-select-img {
          background-image: url('~@/assets/img/ts/select-img.webp');
        }
        .open-rotate {
          transform: rotate(-90deg);
          transition: all 0.5s;
        }
        .close-rotate {
          transition: all 0.5s;
        }
      }
    }
  }

  .nav-box {
    .nav-item {
      background-image: url('~@/assets/img/ts/bg-nav.webp');
      color: #e9f7fa;
      font-family: "tsContent", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    }
    .nav-item.active-nav {
      background-image: url('~@/assets/img/ts/bg-nav-active.webp');
      color: #5f91a8;
    }
  }

  .home-page {
    .container {
      .history-bar {
        margin: 12px 0px;
      }
      .home-cards {
        margin-top: 0px;
      }
      .home-swiper {
        .page-arr {
          @include backgroundSec('ts/icon-next.webp');
          width: 40px;
          &.icon-prev {
            transform: translateY(-50%) scaleX(-1);
          }
        }
        .title {
          background: none;
          span {
            text-shadow: 0px 3px 2px #000;
            font-weight: normal;
            color: #f2ebdd;
            font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          }
        }
      }
      .home-articles {
        .title {
          span {
            color: #f2ebdd;
            font-weight: normal;
            font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          }
        }
        .article-list {
          .item {
            background: #ABC6D8;
            border-radius: 10px;
            color: #2e5c8b !important;
            padding-left: 18px;
            padding-right: 50px;
            font-weight: normal !important;
            font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
            padding-top: 12px !important;
            padding-bottom: 12px !important;
            line-height: 28px !important;
            &.readed {
              color: #2e5c8b !important;
            }
    
            .new {
              width: 44px;
              height: 20px;
              margin-top: -12px;
              @include backgroundSec('ts/icon-new.webp');
            }
    
            .hot {
              width: 48px;
              height: 20px;
              margin-top: -12px;
              @include backgroundSec('ts/icon-hot.webp');
            }
          }
        }
      }
    }
  }
  .bottom-wrap {
    .container {
      .hot-words {
        height: 38px;
        margin-bottom: 6px;
        .item {
          background: #3B7EB0;
          border-radius: 43px;
          height: 38px;
          .content {
            padding-top: 6px;
          }
          span {
            font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
            font-weight: normal;
            color: #f2ebdd;
          }
          .bg {
            display: none;
          }
        }
      }
      .btn_self_servies {
        display: none !important;
      }
    }
  }
  
  .chat-item {
    .chat-customer {
      border-radius: 0px !important;
      background-image: url('~@/assets/img/ts/icon-gpt.webp') !important;
    }
    .chat-player {
      border-radius: 0px !important;
      background-image: url('~@/assets/img/ts/icon-avatar.webp') !important;
    }
  }
  .user-q {
    .avatar {
      top: -10px !important;
      border-radius: 0px !important;
      &.default {
        background-image: url('~@/assets/img/ts/icon-avatar.webp') !important;
      }
    }
  }
  .gpt-a {
    .avatar {
      top: 16px !important;
      border-radius: 0px !important;
      background-image: url('~@/assets/img/ts/icon-gpt.webp') !important;
    }
  }
  // 底部
  .bottom-wrap {
    background: #2B5A98;
    box-shadow: 0px -2px 1px 0px rgba(0,0,0,0.5);
    .hot-words .item:first-child {
      margin-left: -10px;
    }
    .bg-1, .bg-2, .bg-3 {
      background: #424040;
    }
  }
  // 历史服务记录页
  .ticket-wrapper {
    background: none;
    // 查看按钮
    .more {
      @include backgroundSec('ts/btn-send.webp');
      color: #f2ebdd;
      width: 110px;
      font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
      font-weight: normal;
    }
    .history-content .tableList {
      .row {
        background: rgba(8,8,8,0.2);
        .item {
          color: #f2ebdd;
          font-family: "tsContent", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          font-weight: normal;
        }
        .item:first-child {
          border-top-left-radius: 20px;
          border-bottom-left-radius: 20px;
        }
        .item:last-child {
          border-top-right-radius: 20px;
          border-bottom-right-radius: 20px;
        }
      }
      .head {
        background: #405786;
        .item {
          color: #f2ebdd;
          font-family: "tsTitle", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          font-weight: normal;
        }
      }
    }
  }
  // 工单详情页
  .content {
    color: #f2ebdd !important;
  }
  // 文章详情页
  .context {
    color: #f2ebdd !important;
  }
  // 评价页
  .appraise {
    color: #f2ebdd !important;
  }
  // 表单填写页
  .form-wrapper {
    color: #f2ebdd;
    .form-label {
      color: #f2ebdd;
    }
    .van-field__control {
      color: #fff;
    }
    .van-field__word-limit {
      color: #b4b4b4;
    }
    .van-field__body {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #a09b91;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #a09b91;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #a09b91;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #a09b91;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #a09b91;
      }
    }
    .van-cell {
      border: none;
    }
    .img-wrap .img-upload,
    .video-wrap .video-upload,
    .pc-box {
      border: 2px solid #f2ebdd;
	    background: url('~@/assets/img/plus-por.png') no-repeat center;
    }
  }
  // 重开工单
  .van-collapse-item__content .ticket-wrapper {
    background: none;
    backdrop-filter: none;
  }
}

// loading
.loading-icon {
  display: none;
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}

// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('ts/btn-send.webp');
	width: 45%;
	color: #f2ebdd;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  @include backgroundSec('eden/btn-send.png');
	width: 45%;
	color: #f2ebdd;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 1058px !important;
  }
}


// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
    // font-size: 80Px !important;
  }
  .bot-page {
    padding-bottom: 126px !important;
    .main {
      bottom: 126px !important;
    }
  }
  .nav-box {
    top: -42.5px !important;
  }
  .home-page {
    padding-bottom: 126px !important;
    .main {
      bottom: 126px !important;
    }
    .container {
      padding: 18px 20px !important;
      .home-cards {
        background: none;
        .card-scroll {
          display: block;
          padding: 10px;
          .card {
            background: #2F6E9E !important;
            border-radius: 15px;
            display: block;
            float: left;
            width: 25%;
            // height: auto;
            margin-top: 10px;
            img {
              width: 80px;
              height: 80px;
            }
          }
          .card:nth-child(-n+4) {
            margin-top: 0;
          }
          .card-title {
            color: #c5e7dc;
          }
        }
      }
    }
  }
  .home-articles {
    background: #D1DDE4;
    border-radius: 15px;
    .item {
      background: #ABC6D8 !important;
      color: #2e5c8b !important;
      border-radius: 10px;
      width: 96% !important;
      margin: 8px auto 0 !important;
      line-height: 28px !important;
    }
    .title {
      color: #f2ebdd;
      background: #76A9CC;
      border-radius: 10px;
      min-height: 50px;
      margin: 15px auto 0 !important;
      width: 96%;
    }
  }
  .bottom-wrap {
    height: 126px !important;
    .container {
      padding: 12px 20px !important;
    }
  }
  .user-input .btn {
    margin: 0px 0px 0px 22px !important;

  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
  .history-bar {
    font-size: 24px !important;
    font-weight: normal !important;
    color: #f2ebdd;
    background: #2F6E9E !important;
    border-radius: 15px;
    .more-icon {
      background-image: url('~@/assets/img/ts/icon-more.webp');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 26px !important;
      width: 26px !important;
      border-style: none !important;
      transform: rotate(0) !important;
      vertical-align: text-bottom;
    }
  }

  #app {
    .main {
      margin-top: calc(var(--head-bar-height) + 75px) !important;
    }
    .van-popup.van-popup--bottom {
      width: 100% !important;
    }
  }
}