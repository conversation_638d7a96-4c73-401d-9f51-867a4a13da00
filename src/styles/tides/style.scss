.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
*{
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: transparent;
  background-image: url('~@/assets/img/soc/bg.jpg');
  .main-role {
    background-image: url('~@/assets/img/soc/bg-role.png');
    transform: rotateY(180deg);
  }
  .article-bg {
    background-image: url('~@/assets/img/soc/bg-home.png');
  }
  .home-bg {
    background-image: url('~@/assets/img/soc/bg-home.png');
  }
}
a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}

:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button{
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active{
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn{
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active{
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable{
    filter: grayscale(1);
  }
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #E2C885;
}
// loading
.loading-icon {
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}
// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 部分公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  .main-role {
    height: 641px;
    width: 476px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-left: -160px;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}

// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('bg-btn-send.png');
	font-family: Adobe Heiti Std;
	font-weight: normal;
	font-size: 28px;
	color: #1B1F22;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('bg-btn-send.png');
	width: 45%;
	color: #1B1F22;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  border: 2px solid #445960;
	width: 45%;
	color: #cecfc9;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}
// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }

  .answers-detail .content-body,
  .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }

  .answers-detail .content-body,
  .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }

  .answers-detail .content-body,
  .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }

  .answers-detail .content-body,
  .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }

  .answers-detail .content-body,
  .ticket-wrapper {
    height: 1058px !important;
  }
}


// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
    // font-size: 80Px !important;
  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
}