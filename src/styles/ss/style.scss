.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  // height: 100vh;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "PingFang SC", Arial,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}

// 定制样式
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: transparent;
  background-image: url('~@/assets/img/ss/bg.webp');
  // 主页/聊天页背景图
  .main-role {
    background-image: url('~@/assets/img/ss/bg-role.webp');
    width: 250px !important;
    margin-right: 40px;
  }
  .article-bg {
    // background-image: url('~@/assets/img/soc/bg-home.png');
    background: rgba(0,0,0,0.4);
    box-shadow: inset 0px 0px 3px 1px rgba(0,0,0,0.5);
    backdrop-filter: blur(6px);
  }
  .home-bg {
    // background-image: url('~@/assets/img/soc/bg-home.png');
    background: rgba(0,0,0,0.4);
    box-shadow: inset 0px 0px 3px 1px rgba(0,0,0,0.5);
    backdrop-filter: blur(6px);
    .home-cards {
      background: rgb(45,44,43);
      .card {
        background: #595959 !important;
      }
      .history-card {
        background: #595959 !important;
        .img-box {
          background: url('~@/assets/img/ss/history-btn.webp') no-repeat center center;
          background-size: 95% 95%;
        }
      }
    }
  }
  .user-input {
    .search-form {
      height: 47px !important;
      background: #434343 !important;
      border: 1px solid #565252;
      .q-list {
        background: #434343 !important;
        .q-item {
          color: #A9A292 !important;
        }
      }
      input {
        height: 48px !important;
        color: #A9A292 !important;
      }
    }
    .btn {
      max-width: 200px;
      background: url('~@/assets/img/ss/btn-send.webp') center center no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .hot-words {
    .item {
      .content {
        color: #9C7C52 !important;
      }
    }
  }
  .gpt-a .rich-wrap {
    .rich-wrap-bg {
      background: rgba(40,39,38,0.64) !important;
      &::after {
        border-right-color: rgba(40,39,38,0.64) !important;
      }
    }
    .answer {
      color: #A9A292 !important;
    }
    .btn_correct {
      background: #A9A292 !important;
    }
  }
  // 赞踩
  .wrap {
    .like {
      background-image: url('~@/assets/img/icon_kudos.png') !important;
      background-size: 45% 70% !important;
      margin-right: -15px;
      &.active {
        background-image: url('~@/assets/img/icon_kudos1.png') !important;
      }
    }
    .dislike {
      background-image: url('~@/assets/img/icon_kudos.png') !important;
      transform: rotateX(180deg);
      background-size: 45% 70% !important;
      &.active {
        background-image: url('~@/assets/img/icon_kudos1_por.png') !important;
      }
    }
  }
  .user-q .msg {
    background: #BAAE9C !important;
    &::after {
      border-left-color: #BAAE9C !important;
    }
  }
  .second-level .sec-card {
    background: rgba(40, 39, 38, 0.64) !important;
  }
  .home-articles .article-list .item {
    color: #A9A292 !important;
  }
}
// 其它页公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  // 其它页公共背景图
  .main-role {
    height: 641px;
    width: 310px !important;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-left: -160px;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}
// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('ss/btn-send.webp');
	font-family: Adobe Heiti Std;
	font-weight: normal;
	font-size: 28px;
	color: #1B1F22;
}

a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}
:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button {
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active {
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn {
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active {
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable {
    filter: grayscale(1);
  }
}

// 定制css样式
#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #E2C885;
  
  .chat-item {
    .chat-customer {
      background-image: url('~@/assets/img/ss/icon-gpt.webp') !important;
    }
    .chat-player {
      background-image: url('~@/assets/img/ss/icon-avatar.webp') !important;
    }
  }
  .user-q {
    .avatar {
      &.default {
        background-image: url('~@/assets/img/ss/icon-avatar.webp') !important;
      }
    }
  }
  .gpt-a {
    .avatar {
      background-image: url('~@/assets/img/ss/icon-gpt.webp') !important;
    }
  }
  // 底部
  .bottom-wrap {
    background: #272523;
    box-shadow: 0px -2px 1px 0px rgba(0,0,0,0.5);
    .hot-words .item:first-child {
      margin-left: -10px;
    }
    .bg-1, .bg-2, .bg-3 {
      background: #424040;
    }
  }
  // 历史服务记录页
  .ticket-wrapper {
    background: rgba(0,0,0,0.4);
    backdrop-filter: blur(4px);
    // 查看按钮
    .more {
      background: rgb(137, 98, 75);
      color: #EADDBF;
    }
    .history-content .tableList {
      .row {
        background: rgba(39,37,35,0.4);
        .item {
          color: #A9A292;
        }
      }
      .head {
        background: #292826;
        box-shadow: 0px 2px 0px 0px rgba(0,0,0,0.5);
        .item {
          color: #EADDBF;
        }
      }
    }
  }
  // 工单详情页
  .content {
    color: #EADDBF !important;
  }
  // 文章详情页
  .context {
    color: #EADDBF !important;
  }
  // 评价页
  .appraise {
    color: #EADDBF !important;
  }
  // 表单填写页
  .form-wrapper {
    color: #EADDBF;
    .van-field__control {
      color: #A9A292;
    }
    .van-field__body {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #a09b91;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #a09b91;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #a09b91;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #a09b91;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #a09b91;
      }
    }
    .van-cell {
      border: none;
    }
    .img-wrap .img-upload,
    .video-wrap .video-upload,
    .pc-box {
      border: 1px solid #817042;
	    background: url('~@/assets/img/plus.png') no-repeat center;
    }
  }
  // 重开工单
  .van-collapse-item__content .ticket-wrapper {
    background: none;
    backdrop-filter: none;
  }
}

// loading
.loading-icon {
  display: none;
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}

// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('ss/btn-send.webp');
	width: 45%;
	color: #EADDBF;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  border: 2px solid #A9A292;
	width: 45%;
	color: #cecfc9;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 1058px !important;
  }
}


// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
    // font-size: 80Px !important;
  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - env(safe-area-inset-top) - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
  .history-bar {
    background: rgb(45,44,43) !important;
  }
}