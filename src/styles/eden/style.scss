@font-face {
  font-family: 'eden';
  src: url('./FuturaNowHeadlineBlack.ttf') format('truetype');
  font-display: swap;
}
.img-preload {
  opacity: 0;
  width: 0;
  height: 0;
  @include backgroundSec('soc/bg-toast.png');
}
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  box-sizing:border-box;
  -webkit-box-sizing:border-box;
  -webkit-overflow-scrolling: touch;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
input {
  -webkit-user-select: auto !important;
}
html, body {
  width: 100vw;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  background: transparent;
  font-family: "eden", "PingFang SC", <PERSON>l,"SimHei", "Helvetica Neue",Helvetica,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif !important;
}

// 定制样式
html {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  background-color: #937de4;
  // 主页/聊天页背景图
  .main-role {
    display: none;
  }
  .article-bg {
    background: #c8c6fc;
    .divider, .bottom-line {
      background: #6f65a1 !important;
    }
    .content {
      .van-form {
        font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
      }
    }
  }
  .answers-detail {
    .article-bg {
      background: #8572cd;
    }
  }
  .home-bg {
    background: #c8c6fc;
    .home-cards {
      background: rgb(45,44,43);
      .card {
        background: #595959 !important;
      }
      .history-card {
        background: #595959 !important;
        .img-box {
          background: url('~@/assets/img/ss/history-btn.webp') no-repeat center center;
          background-size: 95% 95%;
        }
      }
    }
  }
  .user-input {
    .search-form {
      padding: 0 10px !important;
      height: 47px !important;
      background: #464749 !important;
      .q-list {
        background: #464749 !important;
        .q-item {
          color: #d8d8d8 !important;
        }
      }
      input {
        height: 46px !important;
        line-height: 1em !important;
        color: #d8d8d8 !important;
      }
    }
    .btn {
      color: #a11515 !important;
      max-width: 200px;
      background: url('~@/assets/img/eden/btn-send.png') center center no-repeat !important;
      background-size: 100% 100% !important;
    }
  }
  .hot-words {
    .item {
      .content {
        color: #9C7C52 !important;
      }
    }
  }
  .gpt-a {
    padding-left: 90px !important;
    .rich-wrap {
      .question {
        color: #e9842d !important;
        font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
      }
      .rich-wrap-bg {
        background: #2e2e2e !important;
        &::after {
          display: none;
        }
      }
  
      .answer {
        font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
        color: #d8d8d8 !important;
      }
  
      .btn_correct {
        font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
        background: #d8d8d8 !important;
      }
    }
  }
  // 赞踩
  .wrap {
    .like {
      background-image: url('~@/assets/img/icon_kudos.png') !important;
      background-size: 45% 70% !important;
      margin-right: -15px;
      &.active {
        background-image: url('~@/assets/img/icon_kudos1.png') !important;
      }
    }
    .dislike {
      background-image: url('~@/assets/img/icon_kudos.png') !important;
      transform: rotateX(180deg);
      background-size: 45% 70% !important;
      &.active {
        background-image: url('~@/assets/img/icon_kudos1.png') !important;
      }
    }
  }
  .article-body {
    .like-wrap {
      .like {
        background-image: url('~@/assets/img/icon_kudos_dark.png') !important;
        &.active {
          background-image: url('~@/assets/img/icon_kudos1.png') !important;
        }
      }
      .dislike {
        background-image: url('~@/assets/img/icon_kudos_dark.png') !important;
        &.active {
          background-image: url('~@/assets/img/icon_kudos1.png') !important;
        }
      }
    }
  }
  .user-q .msg {
    font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
    color: #393a3e !important;
    background: #f6f6f6 !important;
    border-top-right-radius: 0px !important;
    &::after {
      top: 0px !important;
      width: 10px !important;
      height: 10px !important;
      box-sizing: border-box !important;
      border-left: 10px solid #f6f6f6 !important;
      border-top: 10px solid #f6f6f6 !important;
      border-right: 10px solid transparent !important;
    }
  }
  .second-level .sec-card {
    background: rgba(40, 39, 38, 0.64) !important;
  }

}
// 其它页公共样式
.answers-detail {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  // 其它页公共背景图
  .main-role {
    display: none;
  }
  .content-body {
    height: 652px;
    width: 907px;
    margin-bottom: 21px;
    margin-left: -40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // justify-content: center;
    padding: 25px 50px;
    .content-detail {
      overflow: auto;
    }
  }
}
// 提交按钮
.submit-btn {
  box-sizing: border-box;
  padding: 0 6px;
	margin: 20px auto;
	width: 260px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	@include backgroundSec('eden/t-btn-send.png');
	font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
	font-weight: normal;
	font-size: 28px;
	color: #c17112;
}

a {
  text-decoration:none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}
a:hover { text-decoration: none; }
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
textarea{
  user-select:text !important;
  -webkit-user-select:text !important;
}
:root {
  --view-safe-top: 0px;
  --view-safe-bottom: 0px;
}

.button {
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active {
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}

.btn {
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active {
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable {
    filter: grayscale(1);
  }
}

// 定制css样式
#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  color: #ae780b;
  line-height: 30px;

  .text-form-box {
    border: 1px solid #6a5ba4;
  }

  .video-preview, .img-wrap {
    .delete {
      background: url("~@/assets/img/eden/delete.png") no-repeat !important;
      background-size: cover !important;
      background-position: center !important;
    }
  }

  .tips-gohome {
    color: #d8d8d8;
    span {
      color: #e9842d;
    }
  }
  .article-title, .article-body {
    font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
  }
  .article-title {
    color: #ffffff;
  }
  .article-link {
    color: #4d3b6f;
    margin-left: 5px;
    margin-right: 5px;
  }
  .web-link {
    color: #ffeb4e;
    margin-left: 5px;
    margin-right: 5px;
  }
  .game-goto {
    border: none;
    background: #eeebff !important;
    color: #8473cf;
    border-radius: 5Px;
  }
  .context {
    font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
    .btn {
      margin-left: 8px;
      margin-right: 8px;
    }
  }
  .form-wrapper .van-cell {
    border: none !important;
  }
  .tickets_enter {
    color: #e9842d !important;
  }
  .main-title {
    color: #e9842d !important;
    font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    font-weight: normal !important;
  }
  .title.reply {
    img {
      display: none;
    }
  }

  .historyRecordsFont span {
    color: #e9842d !important;
  }

  .answers-detail {
    .content {
      .title {
        color: #e9842d;
        font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
        font-weight: normal !important;
      }
    }
  }

  .nav-box {
    .nav-item {
      background: #b0b0b0;
      color: #393939;
      box-sizing: content-box;
      border: 1Px solid #232323;
      font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
    }

    .active-nav {
      background: #e68b33;
      color: #fff;
    }
  }

  .home-page {
    .container {
      .history-bar {
        margin: 12px 0px;
      }
      .home-cards {
        margin-top: 0px;
      }
      .home-swiper {
        .page-arr {
          @include backgroundSec('eden/icon-next.png');
          width: 26px;
        }
        .title {
          background: none;
          span {
            text-shadow: 0px 3px 2px #000;
            font-weight: normal;
            color: #fff;
            font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          }
          
        }
      }
      .home-articles {
        .title {
          span {
            color: #e9842d;
            font-weight: normal;
            font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
            &::before,
            &::after {
              height: 4Px;
              background: none;
              border-right: 1px solid #7e756c;
              border-bottom: 1px solid #7e756c;
              top: 8px;
            }
          }
        }
        .article-list {
          .item {
            background: rgba(8, 8, 8, 0.1);
            padding-left: 18px;
            padding-right: 50px;
            color: #4e4e4d !important;
            font-weight: normal !important;
            font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
            padding-top: 12px !important;
            padding-bottom: 12px !important;
            line-height: 28px !important;
    
            &.readed {
              color: #d8d8d8 !important;
            }
    
            .new {
              width: 46px;
              height: 20px;
              top: 50%;
              right: 5px;
              margin-top: -10px;
              @include backgroundSec('eden/icon-new.png');
            }
    
            .hot {
              width: 46px;
              height: 24px;
              top: 50%;
              right: 5px;
              margin-top: -12px;
              @include backgroundSec('eden/icon-hot.png');
            }
          }
        }
      }
    }
  }
  .bottom-wrap {
    .container {
      .hot-words {
        height: 38px;
        margin-bottom: 6px;
        .item {
          background: #4a4a4a;
          border-radius: 43px;
          height: 38px;
          .content {
            padding-top: 6px;
          }
          span {
            font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
            font-weight: normal;
            color: #cdd1d4;
          }
          .bg {
            display: none;
          }
        }
      }
      .btn_self_servies {
        display: none !important;
      }
    }
  }
  
  .chat-item {
    .chat-customer {
      border: 1Px solid #3c3b4b !important;
      border-radius: 0px !important;
      background-image: url('~@/assets/img/eden/icon-gpt.png') !important;
    }
    .chat-player {
      border: 1Px solid #3c3b4b !important;
      border-radius: 0px !important;
      background-image: url('~@/assets/img/eden/icon-avatar.png') !important;
    }
  }
  .user-q {
    .avatar {
      top: -10px !important;
      border: 1Px solid #3c3b4b !important;
      border-radius: 0px !important;
      &.default {
        background-image: url('~@/assets/img/eden/icon-avatar.png') !important;
      }
    }
  }
  .gpt-a {
    .avatar {
      top: 16px !important;
      border: 1Px solid #3c3b4b !important;
      border-radius: 0px !important;
      background-image: url('~@/assets/img/eden/icon-gpt.png') !important;
    }
  }
  // 底部
  .bottom-wrap {
    background: #272523;
    box-shadow: 0px -2px 1px 0px rgba(0,0,0,0.5);
    .hot-words .item:first-child {
      margin-left: -10px;
    }
    .bg-1, .bg-2, .bg-3 {
      background: #424040;
    }
  }
  // 历史服务记录页
  .ticket-wrapper {
    background: rgba(0,0,0,0.4);
    backdrop-filter: blur(4px);
    // 查看按钮
    .more {
      @include backgroundSec('eden/t-btn-send.png');
      color: #c17112;
      width: 110px;
      font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
      font-weight: normal;
    }
    .history-content .tableList {
      .row {
        background: rgba(8,8,8,0.2);
        .item {
          color: #d8d8d8;
          font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          font-weight: normal;
        }
      }
      .head {
        background: #2e2e2e;
        .item {
          color: #cdd1d4;
          font-family: "eden", "PingFang SC", Arial, "SimHei", "Helvetica Neue", Helvetica, "Microsoft Yahei", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif !important;
          font-weight: normal;
        }
      }
    }
  }
  // 工单详情页
  .content {
    color: #d8d8d8 !important;
  }
  // 文章详情页
  .context {
    color: #d8d8d8 !important;
  }
  // 评价页
  .appraise {
    color: #d8d8d8 !important;
  }
  // 表单填写页
  .form-wrapper {
    color: #d8d8d8;
    .form-label {
      color: #cdd1d4;
    }
    .van-field__control {
      color: #fff;
    }
    .van-field__word-limit {
      color: #b4b4b4;
    }
    .redIcon:before, .van-field__error-message, .van-field__right-icon {
      color: #e9842d;
    }
    .van-field__body {
      ::-webkit-input-placeholder {
        /* WebKit, Blink, Edge */
        color: #a09b91;
      }
      :-moz-placeholder {
        /* Mozilla Firefox 4 to 18 */
        color: #a09b91;
      }
      ::-moz-placeholder {
        /* Mozilla Firefox 19+ */
        color: #a09b91;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #a09b91;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #a09b91;
      }
    }
    .van-cell {
      border: none;
    }
    .img-wrap .img-upload,
    .video-wrap .video-upload,
    .pc-box {
      border: 2px solid #bb8657;
	    background: url('~@/assets/img/plus-por.png') no-repeat center;
    }
  }
  // 重开工单
  .van-collapse-item__content .ticket-wrapper {
    background: none;
    backdrop-filter: none;
  }
}

// loading
.loading-icon {
  display: none;
  width: 108px;
  height: 108px;
  animation: loading 3s infinite linear;
  @include backgroundSec('soc/icon-loading.png');
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.cs-body, .position-rel {
  height: 100%;
}

// 必填项小红星
.redIcon:before {
  content: '* ';
  color: red;
}

// 文字按钮排一行
.mini-button-box {
	height: 50px;
	line-height: 50px;
	width: 50%;
	font-size: 19px;
	white-space: nowrap;
	display: flex;
	justify-content: space-around;
}
.mini-button-left-text {
	font-size: 22px;
	line-height: 50px;
}
.mini-button-yes {
	cursor: pointer;
	border-radius: 5px;
	@include backgroundSec('eden/t-btn-send.png');
	width: 45%;
	color: #c17112;
  padding: 0 3px;
}
.mini-button-no {
	cursor: pointer;
  border-radius: 5px;
  @include backgroundSec('eden/btn-send.png');
	width: 45%;
	color: #a11515;
  padding: 0 3px;
}
.resolve-box {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

// 兼容适配折叠屏和平板
@media (orientation: landscape) and (max-aspect-ratio: 14/9) {
  .main .container {
    height: 620px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 738px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 13/9) {
  .main .container {
    height: 700px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 818px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 12/9) {
  .main .container {
    height: 780px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 898px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 11/9) {
  .main .container {
    height: 860px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 978px !important;
  }
}

@media (orientation: landscape) and (max-aspect-ratio: 10/9) {
  .main .container {
    height: 940px !important;
  }
  .answers-detail .content-body, .ticket-wrapper {
    height: 1058px !important;
  }
}


// 竖版通用部分样式
@media all and (orientation: portrait) {
  html {
    font-size: min(max(50Px, 18vw), 80Px) !important;
    // font-size: 80Px !important;
  }
  .bot-page {
    padding-bottom: 126px !important;
    .main {
      bottom: 126px !important;
    }
  }
  .home-page {
    padding-bottom: 126px !important;
    .main {
      bottom: 126px !important;
    }
    .container{
      padding: 18px 20px !important;
      .home-cards {
        border-radius: 18px;
        background: #cecae7;
        box-shadow: inset 0 3Px 6Px 2Px #c3b6ed;
        .card-scroll {
          display: block;
          padding: 10px;
          .card {
            background: none !important;
            display: block;
            float: left;
            width: 25%;
            height: auto;
            margin-top: 10px;
            img {
              width: 80px;
              height: 80px;
            }
          }
          .card:nth-child(-n+4) {
            margin-top: 0;
          }
          .card-title {
            margin-top: -24px;
            color: #ae780b;
          }
        }
      }
    }
  }
  .home-articles {
    padding-bottom: 0px !important;
    .item {
      padding-top: 6px;
      padding-bottom: 6px !important;
      line-height: 28px !important;
    }
    .title {
      margin: 6px auto !important;
      width: 100%;
    }
  }
  .bottom-wrap {
    height: 126px !important;
    .container {
      padding: 12px 20px !important;
    }
  }
  .user-input .btn {
    margin: 0px 0px 0px 22px !important;

  }
  .answers-detail {
    .main-role {
      display: none;
    }
    .content-body {
      height: calc(100% - var(--head-bar-height) - 15px) !important;
      margin: 0;
    }
  }
  .history-bar {
    font-size: 24px !important;
    font-weight: normal !important;
    color: #e9842d;
    background: linear-gradient(230deg, rgb(46, 46, 46, 1) 0%, rgba(0, 0, 0, 0) 90%) !important;
    .more-icon {
      height: 20px;
      width: 20px;
      border-width: 2Px 2Px 0 0 !important;
    }
  }

  #app {
    .main {
      margin-top: calc(var(--head-bar-height) + 75px) !important;
    }
    .van-popup.van-popup--bottom {
      width: 100% !important;
    }
  }
}