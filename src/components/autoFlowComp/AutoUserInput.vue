<template>
  <div class="t-items">
    <div class="title">
			<img src="~@/assets/img/group.png">
			<!-- 请选择问题分类 / 请选择xxx的细分问题分类 -->
			<span>{{ itemData.fields.desc }}</span>
		</div>
    <van-form class="form-wrapper">
      <van-field
        class="form-item"
        v-model="submitData"
        :disabled="hasClick"
      />
    </van-form>
    <div class="button-wrapper">
      <div :class="['fp-button-box', submitData && !hasClick ? '' : 'disabled_btn', pushAnimate && submitData ? 'fp-button-push': '']">
        <div class="fp-button" @mousedown.stop.prevent="onSubmit" @touchstart.stop.prevent="onSubmit" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("text_submit") }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs, defineExpose } from 'vue'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
interface dataT {
  submitData: string,
  pushAnimate: boolean,
  hasClick: boolean
}
export default defineComponent({
	name: 'AutoUserInput'
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const data: dataT = reactive({
  submitData: '',
  pushAnimate: false,
  hasClick: false
})
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const emit = defineEmits<{
  (event: 'item', itemInfo: autoFlowDataT): void
}>()
const onSubmit = () => {
  if (!data.submitData) {
    return
  }
  if (data.hasClick) {
    Toast($t("auto_only_text"))
    return
  }
  // 按钮按下
  data.hasClick = true
	data.pushAnimate = true
  const itemData = Object.assign({}, props.itemData)
  itemData.fields.value = data.submitData
  emit('item', itemData)
}
const reset = () => {
  data.submitData = ''
  data.hasClick = false
}
defineExpose({
  reset
})
const { submitData, pushAnimate, hasClick } = toRefs(data)
</script>

<style lang="scss" scoped>
.title {
  // overflow: hidden;
  margin: 0px 21.334px 26.68px 0px;
  box-sizing: border-box;
  width: 100%;
  padding: 8Px 2Px 10Px 8Px;
  height: auto;
  line-height: 37.352px;
  color: #cecfc9;
  font-size: 34.684px;
  border-radius: 0px 2Px 2Px 0px;
  position: relative;
  background: rgba(0, 0, 0, 0.2);

  img {
    display: block;
    height: 37.352px;
    width: 37.352px;
    float: left;
    opacity: 0.8;
    margin: 0px 6Px 0px 0px;
  }
}
.title::after {
  content: '';
  position: absolute;
  left: -2Px;
  opacity: 0.6;
  top: 2Px;
  height: 2Px;
  z-index: 1;
  width: 96px;
  background: linear-gradient(to right, #f5c133, rgba(245, 212, 121, 0));
}
.title::before {
  content: '';
  position: absolute;
  left: 2Px;
  opacity: 0.6;
  top: -2Px;
  height: 61.364px;
  z-index: 1;
  width: 2Px;
  background: linear-gradient(to bottom, #f5c133, rgba(245, 212, 121, 0));
}
.form-wrapper {
  & ::v-deep(.van-form) {
		font-size: 37.352px;
	}

	& ::v-deep(.van-cell) {
		background-color: rgba(103, 89, 58, 0.3);
		border: 2px solid #bea55f;
		padding: 16px 26.68px;
		text-shadow: none !important;
		color: #E9C86F;
    border-radius: 3Px;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 34.684px;
		color: #E9C86F;
    text-align: center;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #b6ae9b;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #b6ae9b;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #b6ae9b;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #b6ae9b;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #b6ae9b;
		}
	}

	& ::v-deep(.van-field__right-icon) {
		width: 53.36px;
		padding: 0;
		line-height: 0;

		img {
			width: 100%;
			height: auto;
		}
	}
}
@media all and (orientation : portrait) {
  .form-wrapper {
    .form-item {
      width: 100%;
      margin-top: 35px;
    }
    & ::v-deep(.van-form) {
      font-size: 44px;
    }
    & ::v-deep(.van-field__control) {
      font-size: 42px;
    }
    & ::v-deep(.van-field__right-icon) {
      width: 60px;
    }
    & ::v-deep(.van-cell) {
      border: 2Px solid #bea55f;
      border-radius: 5Px;
      padding: 16px 30px;

      &::after {
        border-bottom: 0;
      }
    }
  }
  .title {
    height: auto;
    line-height: 50px;
    font-size: 0.46rem;
    margin: 0px 16px 14px 0px;
    img {
      height: 48px;
      width: 48px;
      margin: 0px 6Px 0px 0px;
    }
  }
  .title::after {
    width: 100px;
  }
  .title::before {
    height: 70px;
  }
}
.disabled_btn {
  opacity: 0.4;
}
</style>