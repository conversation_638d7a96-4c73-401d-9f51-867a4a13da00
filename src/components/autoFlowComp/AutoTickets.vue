<template>
  <div class="t-items">
    <dialog-wrapper>
      <div class="richtext_box">
        <div class="richtext_tickets rich_content" v-html="itemData.fields.desc"></div> <span @click="clickHandle" :class="['tickets_enter', hasClick ? 'disabled' : '']">{{ $t('text_submit_cstickets') }}</span>
      </div>
    </dialog-wrapper>
  </div>
</template>

<script lang="ts">
import { defineComponent, defineProps, defineEmits, reactive, toRefs } from 'vue'
interface autoFlowDataT {
	process_session: string,
	process_id: number,
	node_id: number,
	cat_id: number,
	cat_name: string,
	module_group: number,
	fields: Record<string, unknown>
}
export default defineComponent({
	name: 'AutoTickets'
})
</script>
<script setup lang="ts">
const props = defineProps<{
  itemData: autoFlowDataT
}>()
const data: {
  hasClick: boolean
} = reactive({
  hasClick: false
})
const emit = defineEmits<{
  (event: 'auto-to-ticket', itemInfo: Record<string, unknown>): void
}>()
const clickHandle = (item: Record<string, unknown>) => {
  if (data.hasClick) return
  data.hasClick = true
  emit('auto-to-ticket', { ...props.itemData })
}
const { hasClick } = toRefs(data)
</script>

<style lang="scss" scoped>
.t-items {
  & ::v-deep .dialog_wrapper {
    max-width: 100%;
    width: 100% !important;
    margin-top: 0px;
    margin-bottom: 0.2rem;
  }
}
.richtext_box {
  box-sizing: border-box;
  width: 1128.564px;
  & ::v-deep img {
    max-width: 919.126px;
    margin: 26.68px 0px;
    display: block;
  }
  & ::v-deep .tickets_enter {
    text-decoration: underline;
    color: #F5C133;
  }
  .disabled {
    color: #CECFC9;
  }
}
@media all and (orientation : portrait) {
  .richtext_box{
    width: 10.1rem;
    & ::v-deep img {
      max-width: 8.57rem;
    }
  }
}
</style>