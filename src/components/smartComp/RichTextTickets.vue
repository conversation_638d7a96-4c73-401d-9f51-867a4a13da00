<template>
  <dialog-wrapper>
    <div class="richtext_tickets rich_content" v-html="props.msgItem.content.answer_rich_text"></div> <span @click="goTickets" class="tickets_enter">{{ $t('text_submit_cstickets') }}</span>
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent, defineProps, onMounted } from 'vue'
import { ImagePreview } from 'vant'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'RichTextTickets'
})
</script>
<script lang="ts" setup>
const props = defineProps<{
  msgItem: Record<string, unknown> & {
    content: Record<string, unknown>
  }
}>()
const router = useRouter()
onMounted(() => {
  // 富文本图片点击可预览
  const richTextImage = document.querySelectorAll('.rich_content img')
  richTextImage.forEach(item => {
    (item as HTMLElement).onclick = (e: Event) => {
      const parentDom = document.getElementById('s-container')
      ImagePreview({
        images: [(e.target as HTMLImageElement).src],
        teleport: 'body',
        closeable: false,
        showIndex: false
      })
    }
  })
})
const goTickets = () => {
  router.push({
    path: '/tickets',
    query: {
      cat_id: props.msgItem.content.answer_ticket_id as number
    }
  })
}
</script>

<style lang="scss" scoped>
.richtext_tickets {
  & ::v-deep img {
    max-width: 919.126px;
    margin: 26.68px 0px;
    display: block;
  }

  & ::v-deep a {
    text-decoration: underline;
    color: #F5C133 !important;
  }
}
.tickets_enter {
  text-decoration: underline;
  color: #F5C133 !important;
}
@media all and (orientation : portrait) {
  .richtext_tickets{
    width: 10.1rem;
    & ::v-deep img {
      max-width: 8.57rem;
      margin: 0.36rem 0px;
      display: block;
    }
  }
}
</style>