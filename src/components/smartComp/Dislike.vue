<template>
  <dialog-wrapper class="dislike-wrapper">
    <div class="title">{{ $t('text_not_solve_reason') }}</div>
    <div class="reason-list">
      <div
        class="reason-item"
        v-for="(item, index) in props.msgItem.reasonList"
        :key="index"
        @click="changeReason(item.key)"
      >
        <div class="reason-item__icon" :class="{ 'select': item.key === selectReason }"></div>
        <span>{{ item.desc }}</span>
      </div>
      <div class="reason-submit" @click="submit">
        <auto-font-size :text="$t('text_submit')"></auto-font-size>
      </div>
    </div>
    <!-- <div class="reason-remark" v-if="selectReason" >
      <van-field
        class="remark"
        v-model="remark"
        type="textarea"
        rows="3"
        autosize
        maxlength="500"
        show-word-limit
        :placeholder="$t('text_reason_placeholder')"
      />
    </div> -->
  </dialog-wrapper>
</template>

<script lang="ts">
import { defineComponent, defineProps, reactive, toRefs, defineEmits, getCurrentInstance } from 'vue'
import { dislikeSave } from '@/api/smart'
import { Toast } from 'vant'
import { useI18n } from 'vue-i18n'

interface dataT {
  isRequesting: boolean,
  selectReason: string,
  remark: string
}
export default defineComponent({
  name: 'Dislike'
})
</script>
<script lang="ts" setup>
const { t: $t } = useI18n()
const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
const props = defineProps<{
  msgItem: Record<string, unknown> & {
    question_data: Record<string, unknown> & { question_id: number },
		reasonList: Array<Record<string, unknown> & {
			key: string
		}>
  }
}>()
const data: dataT = reactive({
  isRequesting: false,
  selectReason: '',
  remark: ''
})
const emit = defineEmits<{
  (event: 'dislike-success', info?: Record<string, unknown> & { question_id: number }): void
}>()

const changeReason = (val: string) => {
  data.selectReason = val
  data.remark = ''
}
// 提交原因
const submit = () => {
  if (data.isRequesting) return
  if (!data.selectReason) {
    Toast($t('text_reason_select'))
    return
  }
  data.isRequesting = true
  const params = {
    question_id: props.msgItem.question_data.question_id,
    reason_key: data.selectReason,
    reason_remark: data.remark
  }
  dislikeSave(params).then((res: Record<string, unknown>) => {
    // Toast($t('text_reason_get'))
    setLog({
      type: 'submit_dislike_reason_trigger',
      question_id: props.msgItem.question_data.question_id,
      is_succeed: 'true',
      reason_key: data.selectReason,
      reason_remark: data.remark
    })
    // 如果点踩理由选择第三个则触发检查有无关联工单列表
    emit('dislike-success', data.selectReason === 'DislikeUnResolved' ? props.msgItem.question_data : undefined)
  }).catch((err: string) => {
    Toast(err)
    setLog({
      type: 'submit_dislike_reason_trigger',
      question_id: props.msgItem.question_data.question_id,
      is_succeed: 'false',
      reason_key: selectReason,
      reason_remark: remark
    })
  }).finally(() => {
    data.isRequesting = false
  })
}
const { selectReason, remark } = toRefs(data)

</script>

<style lang="scss" scoped>
.dislike-wrapper {
  .title {
    width: 1128.564px;
    padding-bottom: 30.682px;
    line-height: 45.356px;
    font-size: 37.352px;
    border-bottom: 1px solid rgba(98, 101, 109, 0.36);
    word-break: break-word;
  }
  .reason-list {
    position: relative;
    padding-bottom: 77.372px;
    .reason-item {
      padding: 20px 0;
      font-size: 32.016px;
      line-height: 40px;
      color: #E9BE75;
      text-shadow: 0px 1px 1px #101013;
      word-break: break-word;
      display: flex;
      align-items: center;
      span {
        margin-left: 13.34px;
        cursor: pointer;
      }
      &__icon {
        width: 40px;
        height: 40px;
        background: url('~@/assets/img/icon_not_select.png') no-repeat;
        background-size: 100% 100%;
        flex-shrink: 0;
      }
      .select {
        width: 40px;
        height: 40px;
        background: url('~@/assets/img/icon_select.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .reason-submit {
      position: absolute;
      padding: 0px 6.67px;
      right: 0px;
      bottom: 0px;
      width: 137.402px;
      color: #FFFCF9;
      background: url('~@/assets/img/smart_btn.png') no-repeat;
      background-size: 100% 100%;
      line-height: 48.024px;
      text-align: center;
    }
  }
  .reason-remark {
    padding: 20px 0;
    & ::v-deep .van-cell {
      background-color: rgba(0, 0, 0, 0.1);
      border: 2px solid #5C5B5A;
      padding: 10.672px 26.68px;
      font-size: 32.016px;
      &::after {
        border-bottom: 0;
      }
    }
    & ::v-deep .van-field__control {
      color: #E9BE75;
    }
    & ::v-deep .van-field__word-limit {
      color: #E9BE75;
    }
  }
}
@media all and (orientation : portrait) {
  .dislike-wrapper {
    .title {
      width: 10.1rem;
      line-height: 0.6rem;
      font-size: 0.46rem;
    }
    .reason-list {
      position: relative;
      .reason-item {
        line-height: 0.43rem;
        font-size: 0.43rem;
        padding: 0.3rem 0;
        &__icon {
          width: 0.43rem;
          height: 0.43rem;
          background: url('~@/assets/img/icon_not_select.png') no-repeat;
          background-size: 100% 100%;
        }
        .select {
          width: 0.43rem;
          height: 0.43rem;
          background: url('~@/assets/img/icon_select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .reason-submit {
        position: absolute;
        right: 0rem;
        bottom: 0rem;
        width: 1.52rem;
        padding: 0rem 0.1rem;
        line-height: 0.6rem;
      }
    }
    .reason-remark {
      & ::v-deep .van-cell {
        font-size: 0.43rem;
      }
    }
  }
}
</style>