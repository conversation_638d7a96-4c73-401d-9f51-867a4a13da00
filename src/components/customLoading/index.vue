<template>
  <van-overlay :show="loadingCount > 0">
    <div class="wrapper" v-if="loadingCount > 0">
      <slot name="loadicon"></slot>
      <div class="loading-text">
        Loading...
      </div>
    </div>
  </van-overlay>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
export default defineComponent({
  name: 'CustomLoading'
})
</script>
<script setup lang="ts">
const { state } = useStore()

const loadingCount = computed(() => state.loadingCount)
</script>
<style lang="scss" scoped>
.loading-text {
  font-size: 28px;
  font-family: Flareserif821 BT;
  font-weight: normal;
  color: #D1C3B5;
  line-height: 1;
  margin-top: 10px;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  --van-loading-spinner-size: 108px;
  flex-direction: column;
}
</style>