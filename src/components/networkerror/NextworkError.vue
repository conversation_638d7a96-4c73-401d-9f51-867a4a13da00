<template>
  <van-overlay :show="show">
    <div class="wrapper">
      <div class="icon"></div>
      <p class="text">Network Error</p>
      <div class="btn" @click="onBtnClick">Confirm</div>
    </div>
  </van-overlay>
</template>

<script lang="ts">
import { defineProps, defineComponent, ref } from 'vue'
export default defineComponent({
  name: 'NextworkError'
})
</script>
<script setup lang="ts">

const props = defineProps<{
  conf: {
    callback: () => void,
    show: boolean,
  }
}>()
let show = ref(props.conf.show)
const onBtnClick = () => {
  props.conf.callback()
  show.value = false
}
</script>



<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.icon {
  width: 96px;
  height: 74px;
  margin-bottom: 15px;
  @include backgroundSec('icon-networkerror.png');
}
.text{
  height: 28px;
  margin-bottom: 30px;
  line-height: 28px;
  font-size: 22px;
  color: #9A948E;
}
.btn {
  width: 244px;
  height: 74px;
  line-height: 60px;
  font-size: 26px;
  font-weight: bold;
  color: #FCFDFF;
  text-align: center;
  @include textStroke(#202320);
  @include backgroundSec('bg-btn-2.png');
}
</style>