<template>
	<div class="ticket-bg">
		<div class="ticket-wrapper">
			<div class="content">
				<van-form :show-error="false" class="form-wrapper">
					<div class="form-label">
						<div class="icon"></div>
						<span>{{ $t("text_reopen_label") }}</span>
            <span style="color: rgb(247, 16, 16);font-size: 20px;">*</span>
          </div>
					<van-field
						class="form-item"
						v-model="form.content"
						type="textarea"
						ref="textarea"
						rows="3"
						autosize
						maxlength="2000"
						show-word-limit
					/>
					<div class="form-label">
						<div class="icon"></div>
						<span>{{ $t("text_img_add") }}</span>
					</div>
					<img-upload
						:isH5Upload="isWXGame || isFromAI"
						class="form-item"
						@success="uploadImg"
						@remove="removeImg"
					></img-upload>
					<div class="form-label">
						<div class="icon"></div>
						<span>{{ $t("text_video_add") }}</span>
					</div>
					<video-upload
						:isH5Upload="isWXGame || isFromAI"
						class="form-item"
						@success="uploadVideo"
						@remove="removeVideo"
					></video-upload>
					<div class="button-wrapper">
						<div :class="['fp-button-box', pushAnimate ? 'fp-button-push': '']">
							<div class="fp-button" @mousedown.stop.prevent="handleComplete" @touchstart.stop.prevent="handleComplete" @mouseup.stop.prevent="pushAnimate = false" @touchend.stop.prevent="pushAnimate = false">{{ $t("text_submit") }}</div>
						</div>
					</div>
				</van-form>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, ref, getCurrentInstance, computed, defineEmits, defineProps } from 'vue'
import { useRoute } from 'vue-router'
import { Toast } from 'vant'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { reopenTicket } from '@/api/tickets'
	interface dataT {
		imgList: Array<unknown>,
		videoList: Array<unknown>,
		pushAnimate: boolean,
		form: {
			ticket_id?: number,
			content: string,
			files: Array<unknown>
		},
		locked: boolean
	}
	export default defineComponent({
		name: 'ReopenTicket'
	})
</script>
<script lang="ts" setup>
	const { state } = useStore()
	const userInfo = computed(() => state.userInfo)
	const isWXGame = computed(() => userInfo.value && 'openid' in userInfo.value )
	const isFromAI = computed(() => userInfo.value && userInfo.value.sdk_version && userInfo.value.sdk_version.includes('ai') )
	const route = useRoute()
	const { t: $t } = useI18n()
	const setLog = getCurrentInstance()?.appContext.config.globalProperties.$utils.basicLog
	const textarea = ref()
	const data: dataT = reactive({
		imgList: [],
		videoList: [],
		form: {
			content: '',
			files: []
		},
		pushAnimate: false,
		locked: false
	})
	const props = defineProps<{
		catIdFromParent: number
	}>()
	onMounted(() => {
		if (route.query.ticketId) {
			data.form.ticket_id = +route.query.ticketId
		} else {
			console.log('缺少重要参数')
		}
		// // H5兼容ios12 监听所有input blur
		// const inputsElement = [...document.getElementsByTagName('input')]
		// inputsElement.forEach(element => {
		// 	if (element) {
		// 		element.onblur = () => {
		// 			setTimeout(() => {
		// 				const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0
		// 				window.scrollTo(0, Math.max(scrollHeight - 1, 0))
		// 			}, 300)
		// 		}
		// 	}
		// })
	})
	// 上传图片
	const uploadImg = (path: Array<string>) => {
		data.imgList.push(...path)
	}
	// 删除图片
	const removeImg = (index: number) => {
		data.imgList.splice(index, 1)
	}
	// 上传视频
	const uploadVideo = (path: Array<string>) => {
		data.videoList.push(...path)
	}
	// 删除视频
	const removeVideo = () => {
		data.videoList.splice(0, 1)
	}
	const handleComplete = () => {
		// 解决安卓机器点击提交按钮触发键盘弹起的问题
		textarea.value.blur()
		data.pushAnimate = true
		if (data.locked) {
			// '表单提交中\n请稍候'
			Toast.fail($t('text_submiting'))
			return
		}
		if (!data.form.content) {
			Toast($t('text_reopen_label'))
			return
		}
		data.form.files = [...data.imgList, ...data.videoList]
		const params = JSON.parse(JSON.stringify(data.form))
		params.files = JSON.stringify(params.files)
		data.locked = true
		reopenTicket(params).then(() => {
			// 提交成功打点
			setLog({
				is_succeed: 'true',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
      clickChild()
			data.locked = false
		}, (res: string) => {
			// 重开提交失败打点
			setLog({
				is_succeed: 'false',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
			data.locked = false
			Toast(res)
		}).catch((err: string) => {
			// 重开提交失败打点
			setLog({
				is_succeed: 'false',
				position: 'reopen_info_submit',
        ticket_id: data.form.ticket_id, // 关联的工单id
        click_time: new Date().getTime(), // 什么时间
        action: 'click', // 点击
        cat_id: props.catIdFromParent, // 问题分类id
				text_content: data.form.content // 提交的文本内容
			})
			data.locked = false
			Toast(err)
		})
	}
  const emit = defineEmits(['show-success'])
  const clickChild = () => {
    emit('show-success')
  }

	const { form, pushAnimate } = toRefs(data)
</script>

<style lang="scss" scoped>
.content {
	word-wrap: break-word;
	word-break: break-all;
	overflow: hidden;
	font-size: 37.352px;
	.title {
		overflow: hidden;
		margin: 0px 21.334px 26.68px 0px;
		box-sizing: border-box;
		width: 100%;
		padding: 8Px 2Px 10Px 8Px;
		height: auto;
		line-height: 58.696px;
    color: #cecfc9;
    font-size: 37.352px;
		border-radius: 0px 2Px 2Px 0px;
		position: relative;
		background: rgba(0, 0, 0, 0.2);
		text-shadow: 0px 1px 2px rgba(76, 79, 88, 0.6), 1px 1px 2px rgba(76, 79, 88, 0.6), 0px 0px 2px rgba(76, 79, 88, 0.6), 1px 0px 2px rgba(76, 79, 88, 0.6);
		border-left: 4Px solid rgba(245, 193, 51, 0.7);

		img {
			display: block;
			height: 37.352px;
			width: 37.352px;
			float: left;
			opacity: 0.8;
			margin: 10.672px 6Px 0px 0px;
		}
	}

	.form-wrapper {
		box-sizing: border-box;
		padding: 0px 26.68px 0px 13.34px;
		.form-item-wrap {
			padding-bottom: 26.68px;
		}
		.form-item {
			box-sizing: border-box;
			width: 97.7%;
			margin-left: 2.1% ;
		}
	}

	& ::v-deep(.van-form) {
		font-size: 37.352px;
	}

	& ::v-deep(.van-cell) {
		background-color: rgba(253, 239, 233, 0.2);
		border: 2px solid #817042;
		padding: 16px 26.68px;

		&::after {
			border-bottom: 0;
		}
	}

& ::v-deep(.van-form) {
		font-size: 37.352px;
	}

	& ::v-deep(.van-cell) {
		background-color: rgba(103, 89, 58, 0.3);
		border: 2px solid #bea55f;
		padding: 16px 26.68px;
		text-shadow: none !important;
		color: #E9C86F;

		&::after {
			border-bottom: 0;
		}
	}

	& ::v-deep(.van-field__control) {
		font-size: 34.684px;
		color: #E9C86F;
	}
	& ::v-deep(.van-field__body) {
		::-webkit-input-placeholder {
			/* WebKit, Blink, Edge */
			color: #b6ae9b;
		}
		:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #b6ae9b;
		}
		::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #b6ae9b;
		}
		:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #b6ae9b;
		}
		::-ms-input-placeholder {
			/* Microsoft Edge */
			color: #b6ae9b;
		}
	}

	& ::v-deep(.van-field__right-icon) {
		width: 53.36px;
		padding: 0;
		line-height: 0;

		img {
			width: 100%;
			height: auto;
		}
	}
}

@media all and (orientation : portrait) {
	.content {
		font-size: 44px;
		.form-wrapper {
			.form-item {
				width: 96.5%;
				margin-left: 3.3% ;
			}
		}
		& ::v-deep(.van-form) {
			font-size: 44px;
		}
		& ::v-deep(.van-field__control) {
			font-size: 42px;
		}
		& ::v-deep(.van-field__right-icon) {
			width: 60px;
		}
		& ::v-deep(.van-cell) {
			border: 2Px solid #bea55f;
			padding: 16px 30px;

			&::after {
				border-bottom: 0;
			}
		}
		.title {
			height: auto;
			line-height: 88px;
			font-size: 50px;

			img {
				height: 60px;
				width: 60px;
				margin: 15px 6Px 0px 0px;
			}
		}
	}
}
</style>
