{"btn_self_help": "SELF SERVICE TOOLS", "btn_send": "Send", "text_self_service": "Self Service Query", "text_placeholder_issues": "Please enter your question", "text_resolved": "Do you find this article helpful?", "text_unmatch": "Sorry, no relevant results were found. <span>Please click here to select the type of problem</span>, then submit a ticket for manual service.", "btn_more": "VIEW MORE", "btn_back": "GO BACK", "text_title": "SMART TOOLS", "text_choose": "The following results were found. Please tap to view details.", "text_data": "No changes were found.", "text_fail": "Failed", "th_time": "Time", "th_befor": "Before", "th_after": "After", "th_reason": "Cause", "text_history": "My Service History", "text_record": "Service History", "text_created_at": "Time of submission", "text_category": "Issue category", "text_progress": "Progress", "text_detail": "Details", "btn_view": "View", "text_in_progress": "Processing", "text_done": "Completed", "text_need_complete": "To be added", "text_timeout": "Closed due to timeout", "text_question_detail": "Issue details", "text_none_record": "Couldn't find your service history", "btn_submit_question": "Submit issue", "text_reply": "Reply from customer support", "text_reply_at": "Time of reply", "btn_appraise": "Give a rating", "text_appraise": "Please rate your experience with our customer support service.", "text_suggest_important": "Your opinion is very important to us.", "text_submit": "Submit", "text_receive_rate": "We've received your comments.", "text_receive_question": "Feedback received. Tap <span>My Service History</span> to view progress.", "text_filled_data": "We've received your additional info and will process it as soon as possible.", "text_uploading": "Uploading", "text_upload_failed": "Upload failed", "text_success": "Success", "text_failed": "Failed", "text_system_issue": "System error", "text_submit_wait": "You've already submitted an issue under this category. Please wait for the result.", "text_resolve_wait": "The issue you submitted is being processed, it may take some time to resolve. Please wait patiently.", "text_pro_wait": "This issue has been submitted to our expert team. Please be patient.", "text_pro_working_wait": "Our expert team is working on a solution, we will get back to you as soon as possible.", "text_fill_info": "This issue requires the submission of additional information in order to solve it as soon as possible!", "text_timeout_close": "Because we did not receive the necessary information, this case has now been closed. You are welcome to contact us again!", "text_fill_close": "Thank you for supplying additional information. We will process it as soon as possible.", "text_solved": "Your issue has been resolved. Thank you for your help!", "text_no_info_abort": "Sorry, because the information you provided is incomplete, this case cannot be processed any further for the time being. You are welcome to contact us again!", "text_solve_fast": "Satisfied with the response time", "text_result_satisfy": "Satisfied with the outcome", "text_services_satisfy": "Satisfied with the service", "text_handle_speed_unsatisfy": "Dissatisfied with the response time", "text_handle_result_unsatisfy": "Dissatisfied with the outcome", "text_handle_attitude_unsatisfy": "Dissatisfied with the service", "btn_confirm": "CONFIRM", "btn_cancel": "CANCEL", "text_txt_add": "Add Text", "text_img_add": "Add Image", "text_video_add": "Add Video", "text_upgrade_pro": "Your problem is too complicated. Customer Service has forwarded the issue to a team of experts.", "text_upgrade_highpro": "Your problem is too complicated. Customer Services has forwarded the issue to a team of senior experts", "text_select_count": "Please select a rating", "text_select_reason": "Please select a reason", "text_add_proof": "Add Evidence", "text_add_more_info": "Please provide additional information", "text_video_exceed_50M": "Video size too big: >50M", "text_manual_handle": "We apologize that you couldn't find the information you were looking for. Please fully describe your question and submit it so that it can be forwarded to one of our team members to be processed.", "text_submit_cstickets": "Submit Ticket", "text_ticket": "Ticket ID", "text_nodata": "Temporarily no data available", "text_had": "Content already exists\nPlease avoid duplicate selection.", "text_submiting": "Submitting\nPlease be patient.", "text_selection_sort": "Please select the category of the feedback that you would like to submit.", "text_selection_sort_sub": "Please select the detailed feedback category of “{q}”", "text_fill_data": "Additional info", "text_complete_close": "Dear player: please note that we need more information to process your ticket. You can click the [Add Information] button to finish this process.<br/><br/>Reminder: please upgrade your information within 24 hours, otherwise the ticket will be automatically closed. Thank you for your cooperation. Ticket closing time:", "text_time_close": "Sorry, since you did not add information within the specified time, the ticket has been automatically closed. If your problem hasn't been solved, please resubmit the ticket and we will continue to do our best to help.", "th_goods": "Item Names", "th_resource": "Resource Names", "th_buff": "Benefit Types", "th_change_rate": "Score Changes", "th_highest_rate": "Highest Scores", "text_data_delay": "Please note that the search result shown has a 10-minute delay", "text_last_day": "Search Time: Last {num} days", "text_no_last_day_record": "No changes found for the last {num} days.", "DislikeWrongContent": "The article had incorrect content", "DislikeBadExperience": "I didn't like the text font and/or layout of the article", "DislikeUnResolved": "My problem was not resolved", "DislikeClickWrong": "I misclicked", "DislikeOther": "Others", "text_not_solve_reason": "Please select a reason why you find it unhelpful:", "text_reason_get": "Thank you. We have received your feedback.", "text_reason_select": "Please select the reason(s) that made you dissatisfied:", "text_reason_placeholder": "Please elaborate so we can understand this issue better.", "ann_info_text": "Announcement", "hot_ques_text": "Hot Topics", "text_dislike_ticket": "We are sorry that your problem was not resolved. You can submit a form and our customer service team will take over.", "text_dislike_tip": "Thanks for your feedback. We will continue to optimize the content of the articles.", "FpRetCodeReasonRepeat": "You have already submitted the reason and do not need to submit again.", "unmatch_first_tip": "We apologize that you couldn't find the information you were looking for. Please provide as much detail as possible in your question. <br/><br/>(For example, (1) Why can’t I participate in the xx Event? (2) What can I do if I didn’t receive the event Rewards in the xx Event?)", "NoneOfAboveTipsOne": "We apologize that you couldn't find the information you were looking for. Please provide as much detail as possible in your question. <br/><br/>(For example, (1) Why can’t I participate in the xx Event? (2) What can I do if I didn’t receive the event Rewards in the xx Event?)", "NoneOfAboveTipsTwo": "We apologize that we were unable to provide you with the answer you were looking for. Rest assured that we will investigate this issue and continue to make improvements to the game.", "NoneOfAbove": "None of the above", "auto_flow_tip_text": "Tap here to solve your problem.", "auto_only_text": "Please do not spam this action. If you'd like to make edits, select a form type again.", "data_query_resource_date_key": "Time", "data_query_resource_desc_key": "Description", "data_query_resource_before_key": "Before", "data_query_resource_change_amount_key": "Change", "data_query_resource_after_key": "After", "data_query_resource_reason_key": "Reason", "data_query_show_title_resource_material": "Check Food", "data_query_show_title_resource_ammo": "<PERSON>", "data_query_show_title_resource_energy": "Check Metal", "data_query_show_title_resource_gold": "Check Gas", "data_query_show_title_resource_diamond": "Check Biocaps", "data_query_show_title_resource_chief_stamina": "Check Chief <PERSON>", "data_query_show_title_resource_exploration_stamina": "Check Squad Stamina", "data_query_show_title_benefit_troops": "Check Troop Stats", "data_query_show_title_benefit_remote_soldiers": "Check Hunter Stats", "data_query_show_title_benefit_infantry": "Check Infantry Stats", "data_query_show_title_benefit_cavalry": "Check Rider Stats", "data_query_show_title_infantry": "Infantry Number", "data_query_show_title_cavalry": "Check Riders Changes", "data_query_show_title_ranged": "Check Hunters Changes", "data_query_show_title_resource_diamond_i": "Check Biocap", "data_query_show_title_survivor_chip": "Check Hero Fragments", "data_query_show_title_targetteleport": "Check Advanced Relocators", "data_query_show_title_honor_coin": "Check Season Store Points", "data_query_show_title_alliance_craft_coin": "Check Alliance Showdown Coins", "data_query_show_title_plasma_core": "Check Plasma Cores", "data_query_show_title_lord_equipment": "Check Chief Gears History", "data_query_show_title_survivor_equipment": "Check Hero Gears History", "data_query_show_title_lord_gem": "Check Chief Badge History", "cs_self_show_title_6001166": "Query: <PERSON><PERSON><PERSON>", "cs_self_show_title_6003952": "Query: <PERSON>", "cs_self_show_title_6001143": "Query: Recruitment Banner", "cs_self_show_title_food": "Query: Food", "cs_self_show_title_wood": "Query: <PERSON>", "cs_self_show_title_ore": "Query: Iron", "cs_self_show_title_silver": "Query: Silver", "cs_self_show_title_steel": "Query: Steel", "cs_self_show_title_gold2": "Query: Gold", "cs_self_show_title_benefit2": "Benefit Change Query", "cs_self_show_title_highscore2": "Highest Record Query", "btn_push_after_completion": "Resubmit", "InvalidQstDescTipstrue": "We apologize that we are currently unable to identify the issue you are facing. Please provide as much detail as possible in your question. (For example, (1) Why can't I participate in the xx event? (2) What can I do if I didn't receive the Rewards in the xx event?)", "text_more_advice": "You might also be interested in:", "InvalidQstDescTipsfalse": "Please describe your issue in detail or check out the suggested results below.", "text_goto_home": "Submission Success! <span>Tap here</span> to return to Help & Support Homepage", "text_problem_resolve": "Has your problem been solved?", "text_button_resolved": "Resolved", "text_button_unresolved": "Unresolved", "text_button_drop": "Ignore", "text_button_reopen": "Reopen", "text_tips_reopen": "We apologize for any inconvenience. Would you like to reopen the ticket and continue to deal with the problem?", "text_title_info": "Reopening Description", "text_tips_success": "Feedback received. Tap <span>My Service History</span> to view progress", "text_tips_processing": "Your reopening is In Progress", "text_reopen_label": "Please enter your reopening appeals", "text_reopen_result": "Reopening Result", "backend": "Please refrain from submitting duplicate evaluations", "text_appraise_attitude": "Service Attitude Rating", "text_appraise_speed": "Processing Speed Rating", "text_appraise_treatment": "Processing Method Rating", "not_resolved_appraise": "We sincerely apologize for not being able to provide a solution to your problem. Please tap <span>Rate</span> to rate the service provided on this occasion.", "history_text_short": "History Tickets", "guides0": "I'm thinking...", "guides1": "Good question!", "guides2": "Please give me a moment to think...", "guides3": "You have truly perplexed me. Let me consider...", "guides4": "This question has presented a challenge. Let me think...", "safety_word": "Apologies, I am unable to provide an answer.", "title_soc": "String of Siren", "links_cs": "Contact Support", "links_mob": "Contact Mod", "links_web": "Game Gifts", "txt_placeholder": "Please enter the contents", "title_hot": "Top Picks", "txt_submit": "SEND", "txt_watch_more": "DETAILS", "network_err": "Network error. Please try again!", "retry": "RECONNECT", "title_warning": "NOTE!", "txt_cancle": "CANCEL", "txt_confirm": "CONFIRM", "txt_back_top": "Back to Top", "txt_no_data": "No More Info", "tab_home": "NEWS", "tab_ai": "AI Assistant", "tab_ai_new": "AI Secretary", "txt_first": "Your <PERSON>, I'm here to guide you through any perplexities you may have. Feel free to choose from the preset questions below or enter your specific inquiry.", "sim_txt_first": "Feel free to ask me questions!", "text_sorry_help": "Is there anything else you need help with?", "btn_none": "No further assistance needed.", "btn_conversation": "Continue the chat.", "text_supplement": "You may enter any further details to be submitted to customer service.", "text_question_survey": "Survey", "text_feedback": "Your feedback is important to us. Please fill it out honestly!", "text_service_rating": "Please rate your experience with our service.", "text_recommendation": "Please select your willingness to recommend this game to others.", "text_other_instructions": "Additional Comments (Optional)", "text_talk_info": "Chat details", "btn_radio_five": "5 stars", "btn_radio_four": "4 stars", "btn_radio_three": "3 stars", "btn_radio_two": "2 stars", "btn_radio_one": "1 star", "text_vague_cover": "Hello! Thank you for reaching out. To assist you effectively, could you please provide more details about the problems you are experiencing? (For example, (1) Why can’t I participate in the xx Event? (2) What can I do if I didn’t receive the event Rewards in the xx Event?)", "text_transfer_ticket": "Hello! Thank you for your patience. To ensure that your issue is addressed thoroughly and professionally, please follow the link below to fill out a form. This will guide you through the necessary steps, allowing our team to get in touch directly and offer personalized assistance.", "text_unknown_script": "Hello! Thank you for reaching out. To assist you effectively, could you please provide more details about the problems you are experiencing? (For example, (1) Why can’t I participate in the xx Event? (2) What can I do if I didn’t receive the event Rewards in the xx Event?)", "text_disclaimer_copy": "Note: The above content is from AI Secretary aggregated reasoning results, for reference only", "btn_error_correction": "Correct <PERSON>", "text_correct_content": "Corrections to this article", "text_content_error": "Incorrect content", "text_incomplete": "Incomplete content", "text_enter_correction": "Please enter the correction", "text_recommend_content": "Recommended content", "text_cant_empty": "Please add additional information", "text_sorry_suggest": "Hello, thank you for reaching out to us. Please see if the following results help answer your question:", "btn_problem_unresolved": "Question unresolved", "title_general_my_cs": "Customer Support", "text_please_enter": "Enter here", "text_please_rate": "Please give your rating", "text_qn_tips": "5 stars means you're highly satisfied🙂, while 1–3 stars means you're not satisfied☹️.", "text_qn_submit_success": "Submission Success! Thanks for your participation.", "text_uid_tip": "We welcome you to leave your game UID to help us improve", "text_auto_close_tip": "Automatic shutdown time", "text_player_stay_page_prompt_online": "Are you still there? (This session will close in 5 minutes if there's no response.)", "text_player_stay_page_session_ended": "Session closed!", "text_post_refusal_guidance": "To ensure that we can resolve your issue quickly and effectively, could you please provide more detailed information?", "text_info_collection_event": "In which event or feature did you encounter this issue?", "text_info_collection_time": "Please select the time when you encountered this issue.", "text_info_collection_description": "Please describe the issue in detail.", "text_info_collection_picture": "Please provide screenshots to help us resolve the issue faster.", "text_info_collection_suggestion": "Please provide your suggestion in detail.", "text_button_continue_conversation": "Continue from the previous session", "text_button_continue_submission": "Continue with the form", "text_auto_generated_ticket_guidance_detailed": "Thank you for your detailed feedback! We have received your request and <b>will process it as soon as possible</b>. Once completed, <b>we will notify you immediately</b>. You can also click here <span>My Service History</span> to check the progress. Below are the key points of information you confirmed:", "text_auto_generated_ticket_progress_update": "During the process, we will <b>keep you updated on the progress</b>. If you have any further questions, please feel free to contact us. Thank you for your trust!", "text_select_time": "Select Time", "text_upload_image": "Upload Image", "text_upload_video": "Upload Video", "text_please_select": "Please select", "text_select_issue_category": "Please select an issue type.", "text_typing": "Entering", "text_no_valid_information_collected": "No valid information collected", "text_unknown_script_out_dc.global.prod_1": "This inquiry has triggered a Level-2 security protocol. The relevant logs have been encrypted into fragmented memory crystals and are currently inaccessible.", "text_unknown_script_out_dc.global.prod_2": "Alert! This term is restricted under <PERSON>'s classified mission logs. Access denied!", "text_vague_question_script_dc.global.prod_1": "Unclear command detected. Please refine your question to unlock relevant information.", "text_vague_question_script_dc.global.prod_2": "Insufficient data for target identification. Please provide more details to unlock relevant intel.", "text_unknown_script_in_dc.global.prod_1": "The Wayne Manor data center is undergoing an upgrade. Knowledge repository expansion in progress. Please try again later.", "text_unknown_script_in_dc.global.prod_2": "Apologies, but this request involves <PERSON>'s classified mission logs. I am unable to provide that information.", "text_unknown_script_out_gog_global_1": "When it comes to matters outside the game, I find it hard to lend my support. However, I can help you overcome the enemies plaguing the kingdom and restore its glory!", "text_unknown_script_out_gog_global_2": "My Lord, with utmost loyalty to the throne, I assist you in governing the kingdom. Let us set aside trivial matters outside the game and focus on the kingdom's military and political affairs.", "text_vague_question_script_gog_global_1": "My Lord, this puzzle is like a broken magical rune, missing a crucial clue. Please provide more details to help me precisely analyze the solution using astrology.", "text_vague_question_script_gog_global_2": "To offer sound advice, I need a clear picture. My Lord, share more details with me, and I will come up with a strategic plan for you.", "text_unknown_script_in_gog_global_1": "This issue is like a sealed ancient scroll to me. However, assisting you is my sworn duty. Please provide more details, and I will use all available resources—knights, scholars, and intelligence group, to find the answer for you.", "text_unknown_script_in_gog_global_2": "My Lord, this mystery is akin to a fog-shrouded ancient battlefield. Please provide more details, and I will initiate Magical Exploration to quickly map out the answer with intelligence from the bards.", "text_unknown_script_out_koa_global_1": "My verses only capture the rhymes of adventure. For questions beyond the game, please await another bard to assist you.", "text_unknown_script_out_koa_global_2": "The sands of time inscribe epics upon the land of Avalon, but time fragments outside the game have yet to blend into our melody.", "text_vague_question_script_koa_global_1": "I need more clues to unravel the mysteries.", "text_vague_question_script_koa_global_2": "Which land's mist obscures your vision? Please share more details so I can assist you.", "text_unknown_script_in_koa_global_1": "The answer you seek might be hidden on a forgotten petal. Please provide more details to help me find it.", "text_unknown_script_in_koa_global_2": "This question is like an unopened scroll of magic. Please share more details to help me find a way to unravel it.", "text_unknown_script_out_mce_global_1": "Parts of <PERSON>'s Notes have been damaged and are under repair.", "text_unknown_script_out_mce_global_2": "I know nearly everything about this mysterious land, yet it seems this issue has never arisen here before—how strange!", "text_vague_question_script_mce_global_1": "Sea coordinates are blurry? Please provide more details to help me pinpoint the issue!", "text_vague_question_script_mce_global_2": "<PERSON>'s <PERSON> is attempting to respond, but the missing pages are seeping seawater. Please provide more details.", "text_unknown_script_in_mce_global_1": "System Error! The coordinates of <PERSON><PERSON><PERSON>'s altar are incorrect. Please provide more details to help me rectify this issue!", "text_unknown_script_in_mce_global_2": "The pages containing the answer to this issue have been torn by ghosts and are under repair. Please provide more details to help me fix this!", "text_unknown_script_out_mo_global_1": "Did you hear the ancient conches whisper among the coral? The answer floats within the migrating whales' secret bubbles.", "text_unknown_script_out_mo_global_2": "Ah... it seems that this question has breached a taboo of the sea god's lineage!", "text_vague_question_script_mo_global_1": "The conches must hear the full tide's frequency before they can answer. It seems that you need to provide more details.", "text_vague_question_script_mo_global_2": "Listen! The ocean is asking you to provide more details, so it can reveal the answer buried in the salt.", "text_unknown_script_in_mo_global_1": "The tides are rearranging the fragments of the answer. Let me weave the next chapter before you ask again.", "text_unknown_script_in_mo_global_2": "Like a pearl sleeping in its shell, the answer to this question needs more time to surface.", "text_unknown_script_out_st_global_1": "Parts of <PERSON>'s Notes have been damaged and are under repair.", "text_unknown_script_out_st_global_2": "I know nearly everything about this mysterious land, yet it seems this issue has never arisen here before—how strange!", "text_vague_question_script_st_global_1": "Sea coordinates are blurry? Please provide more details to help me pinpoint the issue!", "text_vague_question_script_st_global_2": "<PERSON>'s <PERSON> is attempting to respond, but the missing pages are seeping seawater. Please provide more details.", "text_unknown_script_in_st_global_1": "System Error! The coordinates of <PERSON><PERSON><PERSON>'s altar are incorrect. Please provide more details to help me rectify this issue!", "text_unknown_script_in_st_global_2": "The pages containing the answer to this issue have been torn by ghosts and are under repair. Please provide more details to help me fix this!", "text_unknown_script_out_ss_global_1": "Warning! An unknown section of the knowledge base has been corrupted!", "text_unknown_script_out_ss_global_2": "This question falls within the towers' blind spot and cannot be answered.", "text_vague_question_script_ss_global_1": "Memory altered. Please share more details to reconstruct the event.", "text_vague_question_script_ss_global_2": "Mutation possibility detected. Please provide more details to help me address this issue.", "text_unknown_script_in_ss_global_1": "My database has lost 37% of its data after an attack and is currently under repair. Please provide more details in your question.", "text_unknown_script_in_ss_global_2": "The survival database is being reconstructed. Please provide more details to help me address this issue."}