/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
interface ApiT {
	(params: Record<string, unknown>): any
}
declare interface Window {
	chooseFinish: {(params: any): void},
	backImgUrl: {(params: string): void},
	UBT: any // UBT 构造函数
	// ubt 实例定义在 ubt.d.ts 中
}

declare module 'markdown-it'
declare module 'markdown-it-katex'
