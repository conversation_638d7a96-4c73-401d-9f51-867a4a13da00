import { createStore } from 'vuex'
import { reactive } from "vue"
import conversation from './modules/conversation'
import global from './modules/global'

type ObjAnyT = {
  // eslint-disable-next-line
  [key: string]: any
}
interface StateType {
  userInfo: ObjAnyT,
  channel: ObjAnyT,
	uploading: boolean,
  customInfo: ObjAnyT,
  loadingCount: number,
  baseInfo: ObjAnyT,
  globalEnvConfig: {
    isInWebview: boolean
  },
}
const state: StateType = {
  userInfo: {},
  channel: {},
	uploading: false,
  customInfo: {},
  loadingCount: 0,
  baseInfo: {
    banner_lists: [],
    card_lists: [],
    temp_title: '',
    article_lists: [],
    bottom_data: [],
    temp_desc: '',
    is_open_input: true,
    ticket_flag: false,
    vipUser: false
  },
  globalEnvConfig: {
    isInWebview: false
  },
}
const store = createStore({
  state: state,
  mutations: {
    setUserInfo(state: StateType, data: { [key: string]: any }) {
      state.userInfo = { ...data }
      console.log('userInfo', state.userInfo)
      // 使用类型断言完全绕过TypeScript的检查
      if (typeof window.ubt !== 'undefined' && typeof window.ubt.setUser === 'function') {
        (window.ubt.setUser as any)({ ...state.userInfo });
      }
    },
		// 临时解决上传混乱问题
		setUploadingType(state: StateType, data: boolean) {
			state.uploading = data
		},
    setCustomInfo: (state, data) => {
      state.customInfo = data
      window.sessionStorage.setItem('c', JSON.stringify(data))
    },
    setLoadingCount: (state, data) => {
      state.loadingCount = data
    },
    setBaseInfo: (state, data) => {
      const d = reactive({
        banner_lists: [],
        card_lists: [],
        temp_title: '',
        article_lists: [],
        bottom_data: [],
        temp_id: -1,
        temp_desc: '',
        is_open_input: true,
        ticket_flag: false,
        vipUser: false
      })
      d.banner_lists = data.banner_data.banner_lists || []
      d.temp_title = data.banner_data.temp_title || ''
      d.article_lists = data.banner_data.article_lists
      d.bottom_data = data.bottom_data
      d.ticket_flag = data.ticket_flag
      d.vipUser = data.vipUser
      d.card_lists = data.banner_data.card_lists || []
      d.temp_id = data.banner_data.temp_id || -1
      d.temp_desc = data.banner_data.temp_desc || ''
      state.baseInfo = d
    },
    SET_GLOBAL_ENV_CONFIG(state, payload) {
      state.globalEnvConfig = {
        ...state.globalEnvConfig,
        ...payload
      }
    },
  },
  actions: {
    updateGlobalEnvConfig({ commit }, payload) {
      commit('SET_GLOBAL_ENV_CONFIG', payload)
    },
  },
  modules: {
    conversation,
    global
  }
})
export default store
