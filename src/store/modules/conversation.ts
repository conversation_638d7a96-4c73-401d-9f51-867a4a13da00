import { ActionContext } from 'vuex'
import {
  updateConversation,
  getConversationHistory,
  chatConversation,
  createConversationTicket,
  closeContinueConversationStatus
} from '@/api/conversation'
import { ticketDetail, createTicket } from '@/api/tickets'
import {
  RoleType,
  QuestionKeyType,
  QuestionKeyLang,
  MessageItem,
  QuestionGetListItem,
  HistoryAnswerItem,
  NowAnswerContentItem
} from '@/enum/ticketConversation'
import { Toast } from 'vant'
import { t } from '@/plugins/i18n' // 导入 t 函数
import { setLogs } from '@/utils/log'

// API 响应类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 定义状态类型
export interface ConversationState {
  // 基础数据
  categoryTitle: string // 分类标题
  conversationId: string // 对话ID
  catId: number // 分类ID
  hasActiveConversation: boolean // 是否存在活跃会话
  currentTicketId: string // 当前工单ID
  finishMessageContent: string // 完成消息内容
  userAvatar: string | null // 用户头像

  // 消息数据
  messages: MessageItem[] // 消息列表
  showInput: boolean // 是否显示输入框

  // 控件数据
  timeValue: string // 时间值
  timePickerValue: Date // 时间选择器值
  showTimePicker: boolean // 是否显示时间选择器
  uploadStatus: string // 上传状态

  // 当前问题
  isInputLoading: boolean // 是否输入中
  questionAskCount: number // 问题询问次数
  currentQuestionKey: string // 当前问题key
  currentQuestionContent: string // 当前问题内容
  historyAnswers: HistoryAnswerItem[] // 历史回答
  currentHistoryList: MessageItem[] // 当前历史列表
  currentQuestionGetList: QuestionGetListItem[] // 当前问题列表
  questionGetListAnswers: QuestionGetListItem[] // 问题列表回答
  isCompleted: boolean // 是否完成
  submitStatus: boolean // 提交状态
  nowAnswerContent: NowAnswerContentItem[] // 当前回答内容
  chatQuestionList: QuestionGetListItem[] // 聊天问题列表
  conversationCloseStatus: boolean // 会话关闭状态

  // 定时器
  chatTimer: number | null

  // 工单详情
  ticketDetailInfo: any
  ticketFormFields: any[]
}

// 初始状态
const state: ConversationState = {
  // 基础数据
  isInputLoading: false,
  categoryTitle: '',
  conversationId: '',
  catId: 0,
  hasActiveConversation: false,
  currentTicketId: '',
  finishMessageContent: '',
  userAvatar: null,

  // 消息数据
  messages: [],
  showInput: true,

  // 控件数据
  timeValue: '',
  timePickerValue: new Date(),
  showTimePicker: false,
  uploadStatus: '',

  // 当前问题
  questionAskCount: 0,
  currentQuestionKey: '',
  currentQuestionContent: '',
  historyAnswers: [],
  currentHistoryList: [],
  currentQuestionGetList: [],
  questionGetListAnswers: [],
  isCompleted: false,
  submitStatus: false,
  nowAnswerContent: [],
  chatQuestionList: [],
  conversationCloseStatus: false,

  // 定时器
  chatTimer: null,

  // 工单详情
  ticketDetailInfo: null,
  ticketFormFields: []
}

// 定义 getters
const getters = {
  isConversationCompleted: (state: ConversationState) => state.isCompleted,
  currentMessages: (state: ConversationState) => state.messages,
  currentQuestionKey: (state: ConversationState) => state.currentQuestionKey
}

// 定义 mutations
const mutations = {
  RESET_STATE(state: ConversationState) {
    // 清除定时器
    if (state.chatTimer) {
      clearTimeout(state.chatTimer)
      state.chatTimer = null
    }

    state.isInputLoading = false
    state.categoryTitle = ''
    state.conversationId = ''
    state.catId = 0
    state.hasActiveConversation = false
    state.currentTicketId = ''
    state.finishMessageContent = ''
    state.userAvatar = null
    state.messages = []
    state.showInput = true
    state.timeValue = ''
    state.timePickerValue = new Date()
    state.showTimePicker = false
    state.uploadStatus = ''
    state.questionAskCount = 0
    state.currentQuestionKey = ''
    state.currentQuestionContent = ''
    state.historyAnswers = []
    state.currentHistoryList = []
    state.currentQuestionGetList = []
    state.questionGetListAnswers = []
    state.isCompleted = false
    state.submitStatus = false
    state.nowAnswerContent = []
    state.chatQuestionList = []
    state.conversationCloseStatus = false
  },
  SET_IS_INPUT_LOADING(state: ConversationState, loading: boolean) {
    state.isInputLoading = loading
  },
  SET_CATEGORY_TITLE(state: ConversationState, title: string) {
    state.categoryTitle = title
  },
  SET_CONVERSATION_ID(state: ConversationState, id: string) {
    state.conversationId = id
    sessionStorage.setItem('conversationId', id)
  },
  SET_CAT_ID(state: ConversationState, id: number) {
    state.catId = id
  },
  SET_HAS_ACTIVE_CONVERSATION(state: ConversationState, hasActive: boolean) {
    state.hasActiveConversation = hasActive
  },
  SET_CURRENT_TICKET_ID(state: ConversationState, id: string) {
    state.currentTicketId = id
  },
  SET_FINISH_MESSAGE_CONTENT(state: ConversationState, content: string) {
    state.finishMessageContent = content
  },
  SET_USER_AVATAR(state: ConversationState, avatar: string | null) {
    state.userAvatar = avatar
  },
  SET_MESSAGES(state: ConversationState, messages: MessageItem[]) {
    console.log('SET_MESSAGES 设置消息, messages', messages)
    state.messages = messages
  },
  ADD_MESSAGE(state: ConversationState, message: MessageItem) {
    state.messages.push(message)
    console.log('ADD_MESSAGE 添加消息, message', message)
  },
  SET_SHOW_INPUT(state: ConversationState, show: boolean) {
    state.showInput = show
  },
  SET_TIME_VALUE(state: ConversationState, value: string) {
    state.timeValue = value
  },
  SET_TIME_PICKER_VALUE(state: ConversationState, value: Date) {
    state.timePickerValue = value
  },
  SET_SHOW_TIME_PICKER(state: ConversationState, show: boolean) {
    state.showTimePicker = show
  },
  SET_UPLOAD_STATUS(state: ConversationState, status: string) {
    state.uploadStatus = status
  },
  SET_QUESTION_ASK_COUNT(state: ConversationState, count: number) {
    state.questionAskCount = count
  },
  SET_CURRENT_QUESTION_KEY(state: ConversationState, key: string) {
    state.currentQuestionKey = key
    console.log('SET_CURRENT_QUESTION_KEY 设置当前问题key, key', key)
  },
  SET_CURRENT_QUESTION_CONTENT(state: ConversationState, content: string) {
    state.currentQuestionContent = content
    console.log('SET_CURRENT_QUESTION_CONTENT 设置当前问题内容, content', content)
  },
  SET_HISTORY_ANSWERS(state: ConversationState, answers: HistoryAnswerItem[]) {
    state.historyAnswers = answers
  },
  ADD_HISTORY_ANSWER(state: ConversationState, answer: HistoryAnswerItem) {
    state.historyAnswers.push(answer)
  },
  SET_CURRENT_HISTORY_LIST(state: ConversationState, list: MessageItem[]) {
    state.currentHistoryList = list
  },
  SET_CURRENT_QUESTION_GET_LIST(state: ConversationState, list: QuestionGetListItem[]) {
    state.currentQuestionGetList = list
  },
  UPDATE_QUESTION_GET_LIST_ANSWER(
    state: ConversationState,
    { key, hasAnswer, answer }: { key: string; hasAnswer: boolean; answer: any }
  ) {
    state.currentQuestionGetList = state.currentQuestionGetList.map(item => {
      if (item.question_key === key) {
        return {
          ...item,
          has_answer: hasAnswer,
          answer: answer
        }
      }
      return item
    })
  },
  SET_QUESTION_GET_LIST_ANSWERS(state: ConversationState, answers: QuestionGetListItem[]) {
    state.questionGetListAnswers = answers
  },
  SET_IS_COMPLETED(state: ConversationState, completed: boolean) {
    state.isCompleted = completed

    // 如果会话完成，清除定时器
    if (completed && state.chatTimer) {
      clearTimeout(state.chatTimer)
      state.chatTimer = null
    }
  },
  SET_SUBMIT_STATUS(state: ConversationState, status: boolean) {
    state.submitStatus = status
  },
  SET_NOW_ANSWER_CONTENT(state: ConversationState, content: NowAnswerContentItem[]) {
    state.nowAnswerContent = content
  },
  ADD_NOW_ANSWER_CONTENT(state: ConversationState, content: NowAnswerContentItem) {
    state.nowAnswerContent.push(content)
    console.log('ADD_NOW_ANSWER_CONTENT 添加当前回答内容, content', content)
  },
  CLEAR_NOW_ANSWER_CONTENT(state: ConversationState) {
    state.nowAnswerContent = []
  },
  SET_CHAT_QUESTION_LIST(state: ConversationState, list: QuestionGetListItem[]) {
    state.chatQuestionList = list
  },
  SET_CONVERSATION_CLOSE_STATUS(state: ConversationState, status: boolean) {
    state.conversationCloseStatus = status
  },
  SET_CHAT_TIMER(state: ConversationState, timer: number | null) {
    state.chatTimer = timer
  },
  SET_TICKET_DETAIL_INFO(state: ConversationState, info: any) {
    state.ticketDetailInfo = info
  },
  SET_TICKET_FORM_FIELDS(state: ConversationState, fields: any[]) {
    state.ticketFormFields = fields
  },
}

// 定义 actions
const actions = {
  // 初始化会话
  async initConversation(
    { commit, state, dispatch }: ActionContext<ConversationState, any>,
    { catId, conversationId }: { catId: number; conversationId: string }
  ) {
    try {
      // 清除定时器
      dispatch('clearChatTimer')

      commit('SET_CAT_ID', catId)
      commit('SET_CONVERSATION_ID', conversationId)

      if (!state.conversationId) {
        // 创建新对话
        await dispatch('updateConversationInfo')
      }

      // 获取历史记录
      await dispatch('getHistory')

      // 更新历史回答
      await dispatch('updateHistoryAnswers')

      return true
    } catch (error) {
      Toast(t('text_system_issue')) // '初始化对话失败'
      console.error('初始化对话失败:', error)
      return false
    }
  },

  // 更新会话信息
  async updateConversationInfo({ commit, state, rootState }: ActionContext<ConversationState, any>) {
    try {
      const userId = rootState.user?.userId || 'anonymous'

      const params = {
        conversation_id: state.conversationId,
        user_id: userId,
        cat_id: state.catId,
        history: state.historyAnswers,
        question_get_list: state.currentQuestionGetList
      }

      const res = (await updateConversation(params)) as unknown as ApiResponse

      if (res.code === 0) {
        commit('SET_CONVERSATION_ID', res.data.conversation_id)
        return res
      } else {
        console.log('更新会话信息失败, res', res)
        Toast(t('text_system_issue')) // '更新会话信息失败'
        return null
      }
    } catch (error) {
      console.error('更新会话信息失败:', error)
      Toast(t('text_system_issue')) // '更新会话信息失败'
      return null
    }
  },

  // 关闭会话-接口closeContinueConversationStatus
  async closeConversation({ commit, state, rootState }: ActionContext<ConversationState, any>) {
    try {
      const res = (await closeContinueConversationStatus({
        conversation_id: state.conversationId
      })) as unknown as ApiResponse

      if (res.code === 0) {
        commit('SET_CONVERSATION_CLOSE_STATUS', true)
      }
    } catch (error) {
      console.error('关闭会话失败:', error)
    }
  },

  // 获取历史记录
  async getHistory({ commit, state, rootState }: ActionContext<ConversationState, any>) {
    commit('setLoadingCount', 1, { root: true })

    try {
      const res = (await getConversationHistory({
        conversation_id: state.conversationId
      })) as unknown as ApiResponse

      console.log('getHistory 获取历史记录, res', res)

      if (res.code === 0) {
        const { history, question_get_list, ticket_id, conversation_status } = res.data || {}

        // conversation_status 10表示工单会话结束，结束原因：1. 用户主动结束 2. 表单提交结束 3. 超时结束
        // 如果conversation_status为10，则设置弹窗提示语，禁止用户再次提交
        if (conversation_status === 10) {
          commit('SET_CONVERSATION_CLOSE_STATUS', true)
        } else {
          commit('SET_CONVERSATION_CLOSE_STATUS', false)
        }

        console.log('getHistory 获取历史记录, ticket_id', ticket_id)
        // 如果ticket_id存在，则设置当前工单ID，并设置会话完成
        if (ticket_id) {
          commit('SET_CURRENT_TICKET_ID', ticket_id)
          commit('SET_IS_COMPLETED', true)
        } else {
          // 如果ticket_id不存在，则判断question_get_list中是否所有项的has_answer都为true，如果都为true，则设置会话完成，否则设置会话未完成
          const isCompleted = question_get_list.filter((item: QuestionGetListItem) => item.question_key).every((item: QuestionGetListItem) => item.has_answer)
          commit('SET_IS_COMPLETED', isCompleted)
        }

        // 处理 fields 与 question_get_list 比对
        let updatedQuestionGetList = [...(question_get_list.filter((item: QuestionGetListItem) => item.question_key) || [])]

        // 获取 conversationCatInfo 中的 fields
        const conversationCatInfo = rootState.global.conversationCatInfo || {}
        console.log('getHistory 获取 conversationCatInfo 中的 fields, conversationCatInfo', conversationCatInfo)
        if (conversationCatInfo.fields) {
          try {
            // 解析 fields
            let fieldsData = [] as any[]
            if (typeof conversationCatInfo.fields === 'string') {
              fieldsData = JSON.parse(conversationCatInfo.fields)
            }

            console.log('getHistory 解析 fields 数据:', fieldsData)

            // 遍历 question_get_list，添加 is_required 属性
            updatedQuestionGetList = updatedQuestionGetList.map(item => {
              // 查找对应的 field
              const matchedField = fieldsData.find((field: any) => field.field_key === item.question_key)
              if (matchedField) {
                return {
                  ...item,
                  is_required: matchedField.is_required,
                  field_extend: matchedField.field_extend || {}
                }
              }
              return item
            })

            console.log('getHistory 更新后的 question_get_list:', updatedQuestionGetList)
          } catch (error) {
            console.error('解析 fields 失败:', error)
          }
        }

        commit('SET_CURRENT_QUESTION_GET_LIST', updatedQuestionGetList)
        commit('SET_CURRENT_HISTORY_LIST', history || [])
        commit('SET_MESSAGES', history || [])

        // 构建问题列表
        const chatQuestionList = updatedQuestionGetList.map((item: QuestionGetListItem) => {
          return {
            question_key: item.question_key,
            question_content: QuestionKeyLang[item.question_key as keyof typeof QuestionKeyLang]
          }
        })
        commit('SET_CHAT_QUESTION_LIST', chatQuestionList)

        // 判断是否完成
        // const isCompleted = updatedQuestionGetList.every((item: QuestionGetListItem) => item.has_answer)
        // commit('SET_IS_COMPLETED', isCompleted) // 根据实际情况判断是否完成

        return res
      } else {
        Toast(t('text_system_issue')) // '获取历史记录失败'
        return null
      }
    } catch (error) {
      console.error('获取历史记录失败:', error)
      Toast(t('text_system_issue')) // '获取历史记录失败'
      return null
    } finally {
      commit('setLoadingCount', 0, { root: true })
    }
  },

  // 更新历史回答
  async updateHistoryAnswers({ commit, state, dispatch }: ActionContext<ConversationState, any>) {
    // 更新历史回答-如果history为空，或者最后一条消息为系统消息时，则过滤question_get_list与history中question_key不同的项，然后判断不同项中的has_answer为false项，则将该项组装成historyAnswers，填充到messages中
    // 否则更新currentQuestionKey为history中最后一条消息的question_key
    if (
      state.currentHistoryList.length === 0 ||
      state.currentHistoryList[state.currentHistoryList.length - 1].role === RoleType.user
    ) {
      // const differentItems = state.currentQuestionGetList.filter(
      //   (item: QuestionGetListItem) =>
      //     !state.currentHistoryList.some(
      //       (h: MessageItem) => h.question_key === item.question_key
      //     )
      // )

      const requiredNextItems =
        state.currentQuestionGetList.filter((item: QuestionGetListItem) => !item.has_answer && item.is_required) || []

      const nonRequiredNextItems =
        state.currentQuestionGetList.filter((item: QuestionGetListItem) => !item.has_answer && !item.is_required) || []

      const nextItems = requiredNextItems.length > 0 ? requiredNextItems : nonRequiredNextItems

      console.log('updateHistoryAnswers 更新历史回答, nextItems', nextItems)

      if (nextItems.length > 0) {
        commit('SET_QUESTION_GET_LIST_ANSWERS', [nextItems[0]])
        commit('SET_CURRENT_QUESTION_KEY', nextItems[0].question_key)

        const questionKey = nextItems[0].question_key
        const content = QuestionKeyLang[questionKey as keyof typeof QuestionKeyLang]

        const historyAnswers = [
          {
            role: RoleType.system,
            question_key: questionKey,
            content
          }
        ]
        commit('SET_HISTORY_ANSWERS', historyAnswers)

        commit('SET_NOW_ANSWER_CONTENT', [
          {
            text: content
          }
        ])

        commit('SET_MESSAGES', [...state.messages, ...historyAnswers])

        await dispatch('updateConversationInfo')
      }
    } else {
      commit('SET_CURRENT_QUESTION_KEY', state.currentHistoryList[state.currentHistoryList.length - 1].question_key)
    }
  },

  // 发送system的会话
  async sendSystemMessage({ commit, state, dispatch }: ActionContext<ConversationState, any>, content: string) {
    console.log('sendSystemMessage 发送system的会话, content', content)
    if (!content) {
      return
    }

    try {
      commit('ADD_NOW_ANSWER_CONTENT', {
        text: content
      })

      const historyAnswer = {
        role: RoleType.system,
        question_key: 'limit_time',
        content,
        hideControls: true
      }
      commit('SET_HISTORY_ANSWERS', [historyAnswer])

      commit('ADD_MESSAGE', historyAnswer)

      await dispatch('updateConversationInfo')

      commit('SET_SUBMIT_STATUS', true)

      return true
    } catch (error) {
      console.error('发送system的会话失败:', error)
      return false
    }
  },

  // 发送结束语
  async sendFinishMessage({ commit, state, dispatch }: ActionContext<ConversationState, any>, content: string) {
    console.log('sendFinishMessage 发送结束语, content', content)
    if (!content) {
      return
    }

    const historyAnswer = {
      role: RoleType.system,
      question_key: 'limit_time',
      content,
      hideControls: true
    }

    // 设置历史回答
    commit('SET_HISTORY_ANSWERS', [historyAnswer])

    // 添加消息
    commit('ADD_MESSAGE', historyAnswer)

    // 更新会话信息
    await dispatch('updateConversationInfo')

    return true
  },

  // 发送消息
  async sendMessage({ commit, state, dispatch }: ActionContext<ConversationState, any>, content: string) {
    console.log('sendMessage 发送消息, content', content)
    if (!content) {
      return
    }

    try {
      commit('SET_IS_INPUT_LOADING', true)

      // 添加用户回答到总结答案
      commit('ADD_NOW_ANSWER_CONTENT', {
        text: content
      })

      // 设置历史回答
      const historyAnswer = {
        role: RoleType.user,
        question_key: state.currentQuestionKey,
        content
      }
      console.log('sendMessage 设置历史回答, historyAnswer', historyAnswer)
      commit('SET_HISTORY_ANSWERS', [historyAnswer])

      // 添加用户消息到列表
      commit('ADD_MESSAGE', historyAnswer)

      // 单次提交都会更新会话信息
      await dispatch('updateConversationInfo')

      // 设置提交状态
      commit('SET_SUBMIT_STATUS', true)

      // 判断state.currentQuestionKey 不属于 时间或者图片类型，则设置定时器
      const isTimeOrPicture =
        state.currentQuestionKey === QuestionKeyType.time || state.currentQuestionKey === QuestionKeyType.picture

      // 只有在定时器不存在时才创建新的定时器
      console.log('sendMessage 设置聊天定时提交, chatTimer', state.chatTimer)
      if (!state.chatTimer && !isTimeOrPicture) {
        dispatch('setupChatTimer')
      }

      // 如果当前问题key属于时间或者图片类型，则直接提交聊天内容
      if (isTimeOrPicture) {
        await dispatch('submitChat')
      }

      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      Toast(t('text_system_issue')) // '发送消息失败'
      return false
    }
  },

  // 跳过当前问题
  async sendSkipMessage({ commit, state, dispatch }: ActionContext<ConversationState, any>, content: string) {
    console.log('sendSkipMessage 跳过当前问题, content', content)
    if (!content) {
      return
    }

    try {
      // 添加用户回答到总结答案
      commit('ADD_NOW_ANSWER_CONTENT', {
        text: content
      })

      // 设置历史回答
      const historyAnswer = {
        role: RoleType.user,
        question_key: state.currentQuestionKey,
        content
      }
      console.log('sendSkipMessage 设置历史回答, historyAnswer', historyAnswer)
      commit('SET_HISTORY_ANSWERS', [historyAnswer])

      // 设置提交状态
      commit('SET_SUBMIT_STATUS', true)

      // 直接提交聊天内容
      await dispatch('submitChat')

      return true
    } catch (error) {
      console.error('跳过当前问题失败:', error)
      Toast(t('text_system_issue')) // '跳过当前问题失败'
      return false
    }
  },

  // 设置聊天定时提交
  setupChatTimer({ commit, state, dispatch }: ActionContext<ConversationState, any>) {
    // 清除之前的定时器
    dispatch('clearChatTimer')

    console.log('setupChatTimer 设置聊天定时提交, state.isCompleted', state.isCompleted)
    console.log('setupChatTimer 设置聊天定时提交, state.currentQuestionKey', state.currentQuestionKey)
    // 只有在未完成且有当前问题时才设置定时器
    if (!state.isCompleted && state.currentQuestionKey) {
      // 10秒后提交聊天内容
      const timer = window.setTimeout(() => {
        console.log('setupChatTimer 设置聊天定时提交, timer', timer)
        // 再次检查是否已完成，避免在10秒内状态已变更
        if (!state.isCompleted && state.currentQuestionKey) {
          dispatch('submitChat')
        }
      }, 2000)

      // 保存定时器ID
      commit('SET_CHAT_TIMER', timer)
    }
  },

  // 清除聊天定时提交
  clearChatTimer({ commit, state }: ActionContext<ConversationState, any>) {
    if (state.chatTimer) {
      clearTimeout(state.chatTimer)
      commit('SET_CHAT_TIMER', null)
    }
  },

  // 提交聊天内容
  async submitChat({ commit, state, dispatch }: ActionContext<ConversationState, any>) {
    try {
      // 清除定时器
      dispatch('clearChatTimer')

      // 检查是否有内容需要提交
      if (!state.nowAnswerContent.length) {
        console.log('没有内容需要提交')
        return null
      }

      const params = {
        conversation_id: state.conversationId,
        now_question_key: state.currentQuestionKey,
        now_question_content: QuestionKeyLang[state.currentQuestionKey as keyof typeof QuestionKeyLang],
        now_answer_content: state.nowAnswerContent,
        question_list: state.chatQuestionList
      }

      console.log('submitChat 提交聊天内容, params', params)

      const res = (await chatConversation(params)) as unknown as ApiResponse

      console.log('submitChat 提交聊天内容, res', res)

      // 设置顶部loading状态
      commit('SET_IS_INPUT_LOADING', false)

      if (res.code === 0) {
        const { history_answer_list, now_question_key, now_answer_content, hit_question } = res.data

        // 判断当前问题key是否必填
        const isRequired = state.currentQuestionGetList.find(
          (item: QuestionGetListItem) => item.question_key === now_question_key
        )?.is_required

        // 清除定时器
        dispatch('clearChatTimer')

        // 清空nowAnswerContent
        commit('CLEAR_NOW_ANSWER_CONTENT')

        // 判断是否命中，如果命中，则获取下一条问题，填充到messages中，并更新currentQuestionGetList中命中now_question_key的项的has_answer为true，然后更新会话信息
        // 否则判断questionAskCount是否大于0，如果大于0，则获取下一条问题，填充到messages中，并更新currentQuestionGetList中命中now_question_key的项的has_answer为true，然后更新会话信息
        const historyAnswersItem = {
          role: RoleType.system,
          question_key: now_question_key,
          content: QuestionKeyLang[now_question_key as keyof typeof QuestionKeyLang]
        }

        if (hit_question || (!hit_question && (state.questionAskCount > 0 || !isRequired))) {
          commit('SET_QUESTION_ASK_COUNT', 0)

          // 更新currentQuestionGetList中命中now_question_key的项的has_answer为true
          commit('UPDATE_QUESTION_GET_LIST_ANSWER', {
            key: now_question_key,
            hasAnswer: true,
            answer: now_answer_content
          })

          // 过滤currentQuestionGetList中question_key与now_question_key相同的项且has_answer为false的项
          // 优先获取必填项
          const requiredQuestions = state.currentQuestionGetList.filter(
            (item: QuestionGetListItem) =>
              item.question_key !== now_question_key && !item.has_answer && item.is_required === true
          )

          const nonRequiredQuestions = state.currentQuestionGetList.filter(
            (item: QuestionGetListItem) =>
              item.question_key !== now_question_key && !item.has_answer && item.is_required !== true
          )

          // 优先选择必填项，如果没有必填项再选择非必填项
          const nextQuestion: QuestionGetListItem =
            requiredQuestions.length > 0
              ? requiredQuestions[0]
              : nonRequiredQuestions.length > 0
              ? nonRequiredQuestions[0]
              : ({} as QuestionGetListItem)

          if (nextQuestion && nextQuestion.question_key) {
            commit('ADD_MESSAGE', {
              role: RoleType.system,
              content: QuestionKeyLang[nextQuestion.question_key as keyof typeof QuestionKeyLang],
              question_key: nextQuestion.question_key
            })

            commit('SET_QUESTION_GET_LIST_ANSWERS', [nextQuestion])
            commit('SET_CURRENT_QUESTION_KEY', nextQuestion.question_key)

            // 更新historyAnswers
            historyAnswersItem.question_key = nextQuestion.question_key
            historyAnswersItem.content = QuestionKeyLang[nextQuestion.question_key as keyof typeof QuestionKeyLang]
          }
        } else {
          // 判断questionAskCount是否大于0，如果大于0，则获取下一条问题，填充到messages中，并更新currentQuestionGetList中命中now_question_key的项的has_answer为true，然后更新会话信息，否则questionAskCount++, 填充描述语
          commit('SET_QUESTION_ASK_COUNT', state.questionAskCount + 1)

          // 填充描述语- 为了确保我们能够快速有效地解决您的问题，能否请您提供更详细的信息？
          commit('ADD_MESSAGE', {
            role: RoleType.system,
            content: t('text_post_refusal_guidance'), // '为了确保我们能够快速有效地解决您的问题，能否请您提供更详细的信息？'
            question_key: now_question_key
          })

          // 更新historyAnswers
          historyAnswersItem.content = t('text_post_refusal_guidance') // '为了确保我们能够快速有效地解决您的问题，能非请您提供更详细的信息？'
        }

        commit('SET_HISTORY_ANSWERS', [historyAnswersItem])

        // 计算completed，逻辑是state.currentQuestionGetList中的hasAnswer全部为true，则completed为true，否则为false
        const completed = state.currentQuestionGetList.filter(item => item.question_key).every(item => item.has_answer)

        // 更新会话信息
        if (!completed) {
          await dispatch('updateConversationInfo')
        }

        // 检查是否完成
        commit('SET_IS_COMPLETED', completed || false)

        // 工单关闭状态
        commit('SET_CONVERSATION_CLOSE_STATUS', completed)

        return res
      } else {
        Toast(t('text_system_issue')) // '提交聊天失败'
        return null
      }
    } catch (error) {
      console.error('提交聊天失败:', error)
      Toast(t('text_system_issue')) // '提交聊天失败'
      return null
    } finally {
      commit('SET_IS_INPUT_LOADING', false)
    }
  },

  // 图片上传成功回调
  async handleImageUploadSuccess(
    { commit, state, dispatch }: ActionContext<ConversationState, any>,
    images: Array<Record<string, string>>
  ) {
    commit('SET_CURRENT_QUESTION_KEY', QuestionKeyType.picture)
    const imgListString = images.map(item => item.url).join(',')
    console.log('handleImageUploadSuccess 图片上传成功回调, imgListString', imgListString)
    return dispatch('sendMessage', imgListString)
  },

  // 图片删除回调
  handleImageRemove(
    { commit }: ActionContext<ConversationState, any>,
    { index, currentImages }: { index: number; currentImages: any[] }
  ) {
    if (currentImages.length === 0) {
      commit('SET_UPLOAD_STATUS', '')
    } else {
      commit('SET_UPLOAD_STATUS', `已上传${currentImages.length}张图片`)
    }
  },

  // 图片上传状态更新
  handleImageStatusUpdate({ commit }: ActionContext<ConversationState, any>, status: string) {
    commit('SET_UPLOAD_STATUS', status)
  },

  // 时间确认
  async handleTimeConfirm({ commit, state, dispatch }: ActionContext<ConversationState, any>, formattedTime: string) {
    commit('SET_SHOW_TIME_PICKER', false)
    commit('SET_TIME_VALUE', formattedTime)

    if (formattedTime) {
      return dispatch('sendMessage', formattedTime)
    }
  },

  // 创建工单
  async createTicket({ commit, state, dispatch, rootState }: ActionContext<ConversationState, any>, params: any) {
    try {
      // 设置加载状态
      commit('setLoadingCount', 1, { root: true })

      // 构建请求参数
      const requestParams = {
        conversation_id: state.conversationId,
        cat_id: state.catId,
        ...params
      }

      console.log('createTicket 创建工单, requestParams', requestParams)

      // 调用创建工单接口
      const res = await createTicket(requestParams)

      console.log('createTicket 创建工单, res', res)
      if (res) {
        const finishMessageContent = `${state.catId}_${state.conversationId}_${res.ticket_id}_finish`
        // 更新工单ID
        commit('SET_CURRENT_TICKET_ID', res.ticket_id)

        commit('SET_FINISH_MESSAGE_CONTENT', finishMessageContent)

        // 更新会话信息
        const historyAnswersItem = {
          role: RoleType.system,
          content: finishMessageContent,
          question_key: 'finish',
          timestamp: new Date().getTime()
        }

        // 添加完成消息到消息列表
        commit('ADD_MESSAGE', historyAnswersItem)

        commit('SET_HISTORY_ANSWERS', [historyAnswersItem])

        dispatch('updateConversationInfo')

        // 设置会话已完成
        commit('SET_IS_COMPLETED', true)

        // 隐藏输入框
        commit('SET_SHOW_INPUT', false)

        Toast(t('text_success')) // '工单提交成功'

        // 打点
        setLogs({
          button: 1,
          action: 'click',
          result: 1,
          position: 'form_fill',
          conversation_id: state.conversationId,
          ticket_type: true, // 新增参数
        })
        return res
      }
    } catch (error) {
      console.error('创建工单失败:', error)
      Toast(t('text_system_issue')) // '创建工单失败'
      // 打点
      setLogs({
        button: 1,
        action: 'click',
        result: 0,
        position: 'form_fill',
        conversation_id: state.conversationId,
        ticket_type: true, // 新增参数
      })
      return null
    } finally {
      // 清除加载状态
      commit('setLoadingCount', 0, { root: true })
    }
  },

  // 获取工单详情
  async getTicketDetail({ commit, state }: ActionContext<ConversationState, any>) {
    try {
      if (!state.currentTicketId) {
        console.log('工单ID不存在，无法获取工单详情')
        return null
      }

      commit('setLoadingCount', 1, { root: true })

      // 由于是之前的接口逻辑，所以res都被拦截了，只返回了res.data
      const res = await ticketDetail({
        ticket_id: Number(state.currentTicketId)
      })

      console.log('getTicketDetail 获取工单详情, res', res)

      // 接口直接返回 data，不需要检查 res.code
      if (res) {
        commit('SET_TICKET_DETAIL_INFO', res)

        commit('SET_CAT_ID', res.cat_id)

        // 解析工单字段
        if (res.filed) {
          try {
            let fieldsData = []

            // 尝试解析 filed 字段
            console.log(res.filed, typeof res.filed)
            if (typeof res.filed === 'string') {
              fieldsData = JSON.parse(res.filed)
            } else if (typeof res.filed === 'object') {
              fieldsData = res.filed
            }

            console.log('getTicketDetail 解析工单字段, fieldsData', fieldsData)

            // console.log('Object.entries(fieldsData)', Object.entries(fieldsData))

            // 格式化字段数据为展示格式
            const formattedFields = Object.entries(fieldsData)
              .map(([key, value]: [string, any]) => {
                // 处理图片类型
                if (typeof value === 'object' && value?.[0]?.file_type === 'image') {
                  return {
                    label: key,
                    value,
                    type: 'image'
                  }
                }

                return {
                  label: key,
                  value,
                  type: 'text'
                }
              })
              .filter(item => item.label.toLowerCase() !== 'uid_key')

            console.log('getTicketDetail 解析工单字段, formattedFields', formattedFields)

            commit('SET_TICKET_FORM_FIELDS', formattedFields)
          } catch (error) {
            console.error('解析工单字段失败:', error)
            commit('SET_TICKET_FORM_FIELDS', [])
          }
        }

        return res
      } else {
        Toast(t('text_system_issue')) // '获取工单详情失败'
        return null
      }
    } catch (error) {
      console.error('获取工单详情失败:', error)
      Toast(t('text_system_issue')) // '获取工单详情失败'
      return null
    } finally {
      commit('setLoadingCount', 0, { root: true })
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
