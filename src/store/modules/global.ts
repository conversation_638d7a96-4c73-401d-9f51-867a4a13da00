import { ActionContext } from 'vuex'

export interface GlobalState {
  isShowChat: boolean,
  conversationCatInfo: Record<string, any>
}

const state: GlobalState = {
  isShowChat: sessionStorage.getItem('isShowChat') === 'true' || false,
  conversationCatInfo: JSON.parse(localStorage.getItem('conversationCatInfo') || '{}')
}

const mutations = {
  SET_IS_SHOW_CHAT(state: GlobalState, isShowChat: boolean) {
    state.isShowChat = isShowChat
  },
  SET_CONVERSATION_CAT_INFO(state: GlobalState, conversationCatInfo: Record<string, any>) {
    // 获取fields并转换为数组
    const fields = conversationCatInfo.fields || '[]'
    console.log('fields', fields);
    if (fields) {
      const fieldsArray = JSON.parse(fields)
      conversationCatInfo.fieldsArray = fieldsArray
    } else {
      conversationCatInfo.fieldsArray = []
    }
    state.conversationCatInfo = conversationCatInfo
    localStorage.setItem('conversationCatInfo', JSON.stringify(conversationCatInfo))
  }
}

const actions = {
  setIsShowChat({ commit }: ActionContext<GlobalState, any>, isShowChat: boolean) {
    commit('SET_IS_SHOW_CHAT', isShowChat)
  },
  setConversationCatInfo({ commit }: ActionContext<GlobalState, any>, conversationCatInfo: Record<string, any>) {
    commit('SET_CONVERSATION_CAT_INFO', conversationCatInfo)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
