var fs = require('fs')
var xlsx = require('node-xlsx')
var path = require('path')
var program = require('commander')
const getParams = (argv) => {
  var t = new RegExp(/^--/g)
  const params = {}
  argv.filter(item => {
    return item.match(t)
  }).forEach(item => {
    const r = item.replace('--', '').split('=')
    params[r[0]] = r[1]
  })
  return params
}

var params = getParams(process.argv)
var data = xlsx.parse('./客服系统web多语言.xlsx')
var table = (data[0] || [{}]).data || []
// 遍历lang key
for (let j = 2; j < table[0].length; j++) {
  let langCode = table[0][j]
  console.log(langCode)
  let obj = ''
  // 遍历每一行内容
  for (let i = 1; i < table.length; i++) {
    // 判断key是否为空
    if (table[i][0] && table[i][0] !== '') {
      if (table[i][j]) {
        obj += ',\n"' + table[i][0].replace('"', '') + '":"' + table[i][j].replace(/["]+/gm, '').replace(/\r|\n/g, '<br/>') + '"'
      }
    }
  }
  let result = '{' + obj.substr(1) + '}'
  fs.writeFile(path.resolve('../src/assets/lang' + (params.game ? `/${params.game}` : '') + '/' + langCode + '.json'), result, function (err) {
    if (err) {
      console.log('Error! ' + err)
      return
    }
    console.log(langCode + '.js写入完成')
  })
}
