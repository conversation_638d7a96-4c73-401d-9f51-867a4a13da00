[{"index": 0, "name": "questionv2", "desc": "公共分类", "add_time": 1636605204, "up_time": 1675062930, "list": [{"query_path": {"path": "/front/v1/s3/token", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 987, "method": "POST", "catid": 25, "title": "s3获取临时token接口", "path": "/front/v1/s3/token", "project_id": 16, "req_params": [], "res_body_type": "json", "uid": 26, "add_time": 1637752371, "up_time": 1637752407, "req_query": [], "req_headers": [{"required": "1", "_id": "619e1e5743281c70c74f0071", "name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "req_body_form": [], "__v": 0, "desc": "", "markdown": "", "req_body_type": "form", "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"code\":{\"type\":\"number\"},\"msg\":{\"type\":\"string\"},\"data\":{\"type\":\"object\",\"properties\":{\"AccessKeyId\":{\"type\":\"string\"},\"Expiration\":{\"type\":\"string\"},\"SecretAccessKey\":{\"type\":\"string\"},\"SessionToken\":{\"type\":\"string\"}}}}}"}, {"query_path": {"path": "/backend/v2/question/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["questionv2"], "_id": 3607, "method": "POST", "title": "新增问题_v2", "path": "/backend/v2/question/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a3", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"question_id\": {\n      \"description\": \"标准问题id\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionV2Resp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"answers\",\n    \"category_id\",\n    \"effective_time\",\n    \"expire_time\",\n    \"game_project\",\n    \"lang\",\n    \"question_content\"\n  ],\n  \"properties\": {\n    \"answers\": {\n      \"description\": \"问题答案\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"answer_rich_show_eval\",\n          \"answer_type\"\n        ],\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_title\": {\n            \"description\": \"查询文本-查询数据接口描述\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段:1展示；2:不展示； 3:展示New\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"工单系统：提交工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型\",\n            \"type\": \"integer\"\n          },\n          \"filter_pkg_channel_lists\": {\n            \"description\": \"触发条件：包体限定 包对应渠道\",\n            \"type\": \"string\"\n          },\n          \"filter_server_lists\": {\n            \"description\": \"触发条件：服务器 限定\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.QuestionAnswerV2\"\n      }\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestionV2\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionV2Req\"\n}", "project_id": 16, "catid": 25, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/simplifylist", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["questionv2"], "_id": 10047, "method": "POST", "title": "查询精简知识列表_v2", "path": "/backend/v2/question/simplifylist", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8ab", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"name\": {\n            \"description\": \"展示名称名称\",\n            \"type\": \"string\"\n          },\n          \"value\": {\n            \"description\": \"问题ID\",\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.SimplifyQuestionDetail\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.SimplifyQuestionListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"lang\",\n    \"search_field\",\n    \"search_val\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"search_field\": {\n      \"description\": \"搜索字段： question_id: 知识ID, question_content: 知识名称\",\n      \"type\": \"string\"\n    },\n    \"search_val\": {\n      \"description\": \"搜索值：\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.SimplifyQuestionListReq\"\n}", "project_id": 16, "catid": 25, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["questionv2"], "_id": 3612, "method": "POST", "title": "查询问题详情_v2", "path": "/backend/v2/question/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a4", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"answers\"\n  ],\n  \"properties\": {\n    \"answers\": {\n      \"description\": \"问题答案\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"answer_rich_show_eval\",\n          \"answer_type\"\n        ],\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_title\": {\n            \"description\": \"查询文本-查询数据接口描述\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段:1展示；2:不展示； 3:展示New\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"工单系统：提交工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型\",\n            \"type\": \"integer\"\n          },\n          \"filter_pkg_channel_lists\": {\n            \"description\": \"触发条件：包体限定 包对应渠道\",\n            \"type\": \"string\"\n          },\n          \"filter_server_lists\": {\n            \"description\": \"触发条件：服务器 限定\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.QuestionAnswerV2\"\n      }\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"create_time\": {\n      \"description\": \"增加时间\",\n      \"type\": \"string\"\n    },\n    \"creator\": {\n      \"description\": \"添加人\",\n      \"type\": \"string\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestion\"\n      }\n    },\n    \"status\": {\n      \"description\": \"问题状态\",\n      \"type\": \"integer\"\n    },\n    \"update_time\": {\n      \"description\": \"update time\",\n      \"type\": \"string\"\n    },\n    \"updator\": {\n      \"description\": \"最后一次修改人\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionDetailV2Resp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"question_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionDetailV2Req\"\n}", "project_id": 16, "catid": 25, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["questionv2"], "_id": 3617, "method": "POST", "title": "编辑问题_v2", "path": "/backend/v2/question/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a5", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"question_id\": {\n      \"description\": \"标准问题id\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionV2Resp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"answers\",\n    \"category_id\",\n    \"effective_time\",\n    \"expire_time\",\n    \"game_project\",\n    \"lang\",\n    \"question_content\",\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"answers\": {\n      \"description\": \"问题答案\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"answer_rich_show_eval\",\n          \"answer_type\"\n        ],\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_title\": {\n            \"description\": \"查询文本-查询数据接口描述\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段:1展示；2:不展示； 3:展示New\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"工单系统：提交工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型\",\n            \"type\": \"integer\"\n          },\n          \"filter_pkg_channel_lists\": {\n            \"description\": \"触发条件：包体限定 包对应渠道\",\n            \"type\": \"string\"\n          },\n          \"filter_server_lists\": {\n            \"description\": \"触发条件：服务器 限定\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.QuestionAnswerV2\"\n      }\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestionV2\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.EditQuestionV2Req\"\n}", "project_id": 16, "catid": 25, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/get_config_map", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["enum_v2"], "_id": 3602, "method": "POST", "title": "自助查询接口类型返回", "path": "/backend/v2/get_config_map", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a0", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"object\",\n    \"additionalProperties\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": {\n          \"description\": \"枚举名称\",\n          \"type\": \"string\"\n        },\n        \"value\": {\n          \"description\": \"枚举值\",\n          \"type\": \"string\"\n        }\n      },\n      \"$$ref\": \"#/definitions/question.GetLogTypeResp\"\n    }\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"enum_names\": {\n      \"description\": \"枚举的名字,LogType：接口查询; Langs:所有的语种; PkgChannel:根据gameProject获取渠道; Countries:所有的国家；MarkActEvent:标记交互类型\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"example\": [\n        \"LogType\",\n        \"Langs\",\n        \"PkgChannel\",\n        \"Countries\",\n        \"MarkActEvent\"\n      ]\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.GetLogTypeReq\"\n}", "project_id": 16, "catid": 25, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}]}, {"index": 0, "name": "upload", "desc": "upload", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v1/upload_image", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["upload"], "_id": 413, "method": "POST", "title": "上传图片", "path": "/backend/v1/upload_image", "req_params": [], "req_body_form": [{"required": "1", "_id": "6600e13bc0d4d108db80f895", "name": "file", "desc": "图片选中", "type": "file"}], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f896", "name": "Content-Type", "value": "multipart/form-data"}], "req_query": [], "req_body_type": "form", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"string\"\n  }\n}", "project_id": 16, "catid": 36, "uid": 26, "add_time": 1636613267, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/batch_import", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["upload"], "_id": 421, "method": "POST", "title": "保存导入的excel文件地址 - 待处理", "path": "/backend/v1/batch_import", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e138c0d4d108db80f86c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"batch_log_id\": {\n      \"description\": \"待处理文件logid\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BatchImportResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"file_name\",\n    \"filter_group\",\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"file_name\": {\n      \"description\": \"上传后文件路径\",\n      \"type\": \"string\"\n    },\n    \"filter_group\": {\n      \"description\": \"枚举约定值\",\n      \"type\": \"string\",\n      \"example\": \"ImportGroupQuestion\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"original_file_name\": {\n      \"description\": \"原始文件\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BatchImportReq\"\n}", "project_id": 16, "catid": 36, "uid": 26, "add_time": 1636613267, "up_time": 1711333688, "__v": 0}, {"query_path": {"path": "/backend/v1/upload_excel", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["upload"], "_id": 429, "method": "POST", "title": "批量导入上传 excel 文件", "path": "/backend/v1/upload_excel", "req_params": [], "req_body_form": [{"required": "1", "_id": "6600e13bc0d4d108db80f893", "name": "file", "desc": "图片选中", "type": "file"}], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f894", "name": "Content-Type", "value": "multipart/form-data"}], "req_query": [], "req_body_type": "form", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"string\"\n  }\n}", "project_id": 16, "catid": 36, "uid": 26, "add_time": 1636613267, "up_time": 1711333691, "__v": 0}]}, {"index": 0, "name": "question", "desc": "question", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v1/elfin_egress", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 15576, "method": "POST", "title": "AI精灵系统B端接口代理", "path": "/backend/v1/elfin_egress", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f87a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": true\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"additionalProperties\": true\n}", "project_id": 16, "catid": 38, "uid": 28, "add_time": 1710994383, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/question/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 437, "method": "POST", "title": "删除问题", "path": "/backend/v1/question/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f87e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.DelQuestionReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/egress", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 3562, "method": "POST", "title": "工单系统B端接口代理", "path": "/backend/v1/egress", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f879", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": true\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"additionalProperties\": true\n}", "project_id": 16, "catid": 38, "uid": 288, "add_time": 1649670186, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/question/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 445, "method": "POST", "title": "新增问题", "path": "/backend/v1/question/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "625d4f1950947e1b4f33cb08", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"question_id\": {\n      \"description\": \"标准问题id\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"answer_type\",\n    \"category_id\",\n    \"effective_time\",\n    \"expire_time\",\n    \"game_project\",\n    \"lang\",\n    \"question_content\"\n  ],\n  \"properties\": {\n    \"answer_card_id\": {\n      \"description\": \"答案 卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_filer\": {\n      \"description\": \"AnswerQueryUrlDesc string `json:\\\"answer_query_url_desc\\\"` // 查询文本-查询数据接口描述\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_title\": {\n      \"description\": \"查询文本-查询数据接口描述\",\n      \"type\": \"string\"\n    },\n    \"answer_rich_show_eval\": {\n      \"description\": \"展示评价信息字段\",\n      \"type\": \"integer\"\n    },\n    \"answer_rich_text\": {\n      \"description\": \"富文本内容\",\n      \"type\": \"string\"\n    },\n    \"answer_ticket_id\": {\n      \"description\": \"工单系统：提交工单ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_type\": {\n      \"description\": \"答案类型\",\n      \"type\": \"integer\"\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestion\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1650282265, "__v": 0}, {"query_path": {"path": "/backend/v1/batchimport/log", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 453, "method": "POST", "title": "查询批量导入记录", "path": "/backend/v1/batchimport/log", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e138c0d4d108db80f86d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"create_time\": {\n            \"type\": \"string\"\n          },\n          \"creator\": {\n            \"type\": \"string\"\n          },\n          \"fail_reason\": {\n            \"description\": \"失败原因\",\n            \"type\": \"string\"\n          },\n          \"file_group\": {\n            \"description\": \"分组\",\n            \"type\": \"string\"\n          },\n          \"game_project\": {\n            \"description\": \"游戏项目名称\",\n            \"type\": \"string\"\n          },\n          \"id\": {\n            \"description\": \"日志ID\",\n            \"type\": \"integer\"\n          },\n          \"origin_file\": {\n            \"description\": \"原始文件地址\",\n            \"type\": \"string\"\n          },\n          \"origin_file_name\": {\n            \"description\": \"原始文件名称\",\n            \"type\": \"string\"\n          },\n          \"remark\": {\n            \"type\": \"string\"\n          },\n          \"result_file\": {\n            \"description\": \"处理结果文件\",\n            \"type\": \"string\"\n          },\n          \"status\": {\n            \"description\": \"当前状态\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.BatchImportLog\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BatchImportLogResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BatchImportLogReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1711333688, "__v": 0}, {"query_path": {"path": "/backend/v1/question/trainlog", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 461, "method": "POST", "title": "查询模型训练记录", "path": "/backend/v1/question/trainlog", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f881", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"ai_type\": {\n            \"description\": \"ai类型\",\n            \"type\": \"integer\"\n          },\n          \"create_time\": {\n            \"type\": \"string\"\n          },\n          \"creator\": {\n            \"type\": \"string\"\n          },\n          \"fail_reason\": {\n            \"type\": \"string\"\n          },\n          \"game_project\": {\n            \"description\": \"游戏项目名称\",\n            \"type\": \"string\"\n          },\n          \"lang\": {\n            \"type\": \"string\"\n          },\n          \"log_id\": {\n            \"type\": \"integer\"\n          },\n          \"status\": {\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.QuestionTrainLog\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionTrainLogResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionTrainLogReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/question/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 469, "method": "POST", "title": "查询问题list", "path": "/backend/v1/question/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f87f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"category_id\": {\n            \"description\": \"类目ID\",\n            \"type\": \"integer\"\n          },\n          \"create_time\": {\n            \"description\": \"增加时间\",\n            \"type\": \"string\"\n          },\n          \"creator\": {\n            \"description\": \"添加人\",\n            \"type\": \"string\"\n          },\n          \"effective_time\": {\n            \"description\": \"问题开始生效时间\",\n            \"type\": \"string\"\n          },\n          \"expire_time\": {\n            \"description\": \"问题截止时间\",\n            \"type\": \"string\"\n          },\n          \"game_project\": {\n            \"description\": \"游戏项目名称\",\n            \"type\": \"string\"\n          },\n          \"lang\": {\n            \"description\": \"语言\",\n            \"type\": \"string\"\n          },\n          \"logic_status\": {\n            \"description\": \"状态字段 - 逻辑判断当前文章状态\",\n            \"type\": \"integer\"\n          },\n          \"question_content\": {\n            \"description\": \"标准问题\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"type\": \"integer\"\n          },\n          \"status\": {\n            \"description\": \"问题状态\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"description\": \"update time\",\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"description\": \"最后一次修改人\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.ListQuestionDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ListQuestionResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"logic_status\": {\n      \"description\": \"状态字段 - 逻辑判断当前文章状态\",\n      \"type\": \"integer\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    },\n    \"search\": {\n      \"description\": \"搜索文本\",\n      \"type\": \"string\"\n    },\n    \"status\": {\n      \"description\": \"状态字段 - 废弃该字段(old)\",\n      \"type\": \"integer\"\n    },\n    \"update_end_tm\": {\n      \"description\": \"更新时间 - 结束时间\",\n      \"type\": \"string\"\n    },\n    \"update_start_tm\": {\n      \"description\": \"更新时间 - 开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ListQuestionReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/question/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 477, "method": "POST", "title": "查询问题详情", "path": "/backend/v1/question/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "625d4f1950947e1b4f33cb0a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"answer_card_id\": {\n      \"description\": \"答案 卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_filer\": {\n      \"description\": \"调用远程查询条件\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_title\": {\n      \"description\": \"查询文本-文本提示\",\n      \"type\": \"string\"\n    },\n    \"answer_rich_show_eval\": {\n      \"description\": \"展示评价信息字段\",\n      \"type\": \"integer\"\n    },\n    \"answer_rich_text\": {\n      \"description\": \"富文本内容\",\n      \"type\": \"string\"\n    },\n    \"answer_ticket_id\": {\n      \"description\": \"提交工单对应工单ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_type\": {\n      \"description\": \"答案类型\",\n      \"type\": \"integer\"\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"create_time\": {\n      \"description\": \"增加时间\",\n      \"type\": \"string\"\n    },\n    \"creator\": {\n      \"description\": \"添加人\",\n      \"type\": \"string\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestion\"\n      }\n    },\n    \"status\": {\n      \"description\": \"问题状态\",\n      \"type\": \"integer\"\n    },\n    \"update_time\": {\n      \"description\": \"update time\",\n      \"type\": \"string\"\n    },\n    \"updator\": {\n      \"description\": \"最后一次修改人\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"question_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionDetailReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1650282265, "__v": 0}, {"query_path": {"path": "/backend/v1/question/training", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 485, "method": "POST", "title": "模型训练", "path": "/backend/v1/question/training", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "65095ac7c0d4d108db80e55d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"训练 ai 模型的类型表示 - C端接口调用不传此字段\",\n      \"type\": \"array\",\n      \"items\": {\n        \"description\": \"ai引擎类型： 1:自研AI算法 2：chatGPT\",\n        \"type\": \"integer\"\n      }\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"is_all\": {\n      \"description\": \"1:表示全部训练；\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"执行语言训练\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionTrainingReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1695111879, "__v": 0}, {"query_path": {"path": "/backend/v1/question/training_core", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 15579, "method": "POST", "title": "模型训练", "path": "/backend/v1/question/training_core", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f880", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"训练 ai 模型的类型表示 - C端接口调用不传此字段\",\n      \"type\": \"array\",\n      \"items\": {\n        \"description\": \"ai引擎类型： 1:自研AI算法 2：chatGPT\",\n        \"type\": \"integer\"\n      }\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"is_all\": {\n      \"description\": \"1:表示全部训练；\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"执行语言训练\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.QuestionTrainingReq\"\n}", "project_id": 16, "catid": 38, "uid": 28, "add_time": 1710994383, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/question/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["question"], "_id": 493, "method": "POST", "title": "编辑问题", "path": "/backend/v1/question/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "625d4f1950947e1b4f33cb0b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"question_id\": {\n      \"description\": \"标准问题id\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AddQuestionResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"answer_type\",\n    \"category_id\",\n    \"effective_time\",\n    \"expire_time\",\n    \"game_project\",\n    \"lang\",\n    \"question_content\",\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"answer_card_id\": {\n      \"description\": \"答案 卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_filer\": {\n      \"description\": \"AnswerQueryUrlDesc string `json:\\\"answer_query_url_desc\\\"` // 查询文本-调用接口\",\n      \"type\": \"integer\"\n    },\n    \"answer_query_title\": {\n      \"description\": \"查询文本-调用接口\",\n      \"type\": \"string\"\n    },\n    \"answer_rich_show_eval\": {\n      \"description\": \"展示评价信息字段\",\n      \"type\": \"integer\"\n    },\n    \"answer_rich_text\": {\n      \"description\": \"富文本内容\",\n      \"type\": \"string\"\n    },\n    \"answer_ticket_id\": {\n      \"description\": \"工单系统：提交工单ID\",\n      \"type\": \"integer\"\n    },\n    \"answer_type\": {\n      \"description\": \"答案类型\",\n      \"type\": \"integer\"\n    },\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"effective_time\": {\n      \"description\": \"问题开始生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"问题截止时间\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"标准问题\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    },\n    \"similar_question\": {\n      \"description\": \"相似问题\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"similar_question_content\": {\n            \"description\": \"相似问题\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AddSimilarQuestion\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.EditQuestionReq\"\n}", "project_id": 16, "catid": 38, "uid": 26, "add_time": 1636613268, "up_time": 1650282265, "__v": 0}]}, {"index": 0, "name": "card", "desc": "card", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v1/card/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 501, "method": "POST", "title": "修改卡片", "path": "/backend/v1/card/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f871", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.EditCardResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"card_id\",\n    \"card_name\",\n    \"game_project\",\n    \"prompt_title\"\n  ],\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    },\n    \"card_name\": {\n      \"type\": \"string\"\n    },\n    \"children\": {\n      \"description\": \"子卡片\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"answer_text\",\n          \"card_image\",\n          \"child_title\"\n        ],\n        \"properties\": {\n          \"answer_text\": {\n            \"description\": \"点击后回复文本； key：语言，val 为具体文本\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          },\n          \"card_image\": {\n            \"description\": \"图片地址\",\n            \"type\": \"string\"\n          },\n          \"child_title\": {\n            \"description\": \"标题 多语言；  key：语言，val 为具体标题\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"$$ref\": \"#/definitions/card.AddCardChild\"\n      }\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称l\",\n      \"type\": \"string\"\n    },\n    \"guide_title\": {\n      \"description\": \"引导语 多语言；  key：语言，val 为具体引导语\",\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"prompt_title\": {\n      \"description\": \"提示语 多语言；  key：语言，val 为具体引导语\",\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"show_type\": {\n      \"description\": \"展示样式： 1:卡片形式；2:列表形式\",\n      \"type\": \"integer\"\n    },\n    \"status\": {\n      \"description\": \"状态是\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.EditCardReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613268, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/card/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 509, "method": "POST", "title": "删除卡片", "path": "/backend/v1/card/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f86f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.DelCardReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613268, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/card/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 517, "method": "POST", "title": "卡片列表", "path": "/backend/v1/card/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f872", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"card_id\": {\n            \"description\": \"card id\",\n            \"type\": \"integer\"\n          },\n          \"card_name\": {\n            \"type\": \"string\"\n          },\n          \"card_unique_key\": {\n            \"description\": \"卡片加密后唯一ID， C端使用，\",\n            \"type\": \"string\"\n          },\n          \"create_time\": {\n            \"type\": \"string\"\n          },\n          \"creator\": {\n            \"type\": \"string\"\n          },\n          \"effective_time\": {\n            \"description\": \"生效时间\",\n            \"type\": \"string\"\n          },\n          \"expire_time\": {\n            \"description\": \"过期时间\",\n            \"type\": \"integer\"\n          },\n          \"game_project\": {\n            \"description\": \"游戏项目名称\",\n            \"type\": \"string\"\n          },\n          \"last_updator\": {\n            \"type\": \"string\"\n          },\n          \"show_type\": {\n            \"description\": \"展示样式： 1:卡片形式；2:列表形式\",\n            \"type\": \"integer\"\n          },\n          \"status\": {\n            \"description\": \"当前状态\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/card.ListCardDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.ListCardResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"search\": {\n      \"description\": \"搜索文本\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.ListCardReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613268, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/card/simplifylist", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 525, "method": "POST", "title": "卡片精简信息列表", "path": "/backend/v1/card/simplifylist", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f873", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"name\": {\n        \"description\": \"卡片名称\",\n        \"type\": \"string\"\n      },\n      \"value\": {\n        \"description\": \"卡片ID\",\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/card.SimplifyCard\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"search\": {\n      \"description\": \"搜索文本\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.SimplifyCardListReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613268, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/card/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 533, "method": "POST", "title": "卡片详情", "path": "/backend/v1/card/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f870", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"card_id\": {\n      \"description\": \"card id\",\n      \"type\": \"integer\"\n    },\n    \"card_name\": {\n      \"type\": \"string\"\n    },\n    \"card_unique_key\": {\n      \"description\": \"卡片加密后唯一ID， C端使用，\",\n      \"type\": \"string\"\n    },\n    \"children\": {\n      \"description\": \"子卡片\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"answer_text\": {\n            \"description\": \"点击后回复文本； key：语言，val 为具体文本\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          },\n          \"card_child_id\": {\n            \"description\": \"每个子卡片id\",\n            \"type\": \"integer\"\n          },\n          \"card_image\": {\n            \"description\": \"图片地址\",\n            \"type\": \"string\"\n          },\n          \"child_title\": {\n            \"description\": \"标题 多语言；  key：语言，val 为具体标题\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"$$ref\": \"#/definitions/card.CardChildDetail\"\n      }\n    },\n    \"create_time\": {\n      \"type\": \"string\"\n    },\n    \"creator\": {\n      \"type\": \"string\"\n    },\n    \"effective_time\": {\n      \"description\": \"生效时间\",\n      \"type\": \"string\"\n    },\n    \"expire_time\": {\n      \"description\": \"过期时间\",\n      \"type\": \"integer\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"guide_title\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"prompt_title\": {\n      \"description\": \"提示语 多语言\",\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"show_type\": {\n      \"description\": \"展示样式： 1:卡片形式；2:列表形式\",\n      \"type\": \"integer\"\n    },\n    \"status\": {\n      \"description\": \"当前状态\",\n      \"type\": \"integer\"\n    },\n    \"update_time\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.CardDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"card_id\"\n  ],\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.CardDetailReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613268, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/card/child_opts", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 11052, "method": "POST", "title": "子卡片选项", "path": "/backend/v1/card/child_opts", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6458610b50947e1b4f3433a8", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"desc\": {\n            \"type\": \"string\"\n          },\n          \"idx\": {\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/card.CardChildOpt\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/card.CardChildOptsResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"card_id\"\n  ],\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.CardDetailReq\"\n}", "project_id": 16, "catid": 40, "uid": 28, "add_time": 1683513611, "up_time": 1683513611, "__v": 0}, {"query_path": {"path": "/backend/v1/card/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["card"], "_id": 541, "method": "POST", "title": "新增卡片", "path": "/backend/v1/card/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f86e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"card_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.AddCardResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"card_name\",\n    \"children\",\n    \"game_project\",\n    \"guide_title\",\n    \"prompt_title\"\n  ],\n  \"properties\": {\n    \"card_name\": {\n      \"type\": \"string\"\n    },\n    \"children\": {\n      \"description\": \"子卡片\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"required\": [\n          \"answer_text\",\n          \"card_image\",\n          \"child_title\"\n        ],\n        \"properties\": {\n          \"answer_text\": {\n            \"description\": \"点击后回复文本； key：语言，val 为具体文本\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          },\n          \"card_image\": {\n            \"description\": \"图片地址\",\n            \"type\": \"string\"\n          },\n          \"child_title\": {\n            \"description\": \"标题 多语言；  key：语言，val 为具体标题\",\n            \"type\": \"object\",\n            \"additionalProperties\": {\n              \"type\": \"string\"\n            }\n          }\n        },\n        \"$$ref\": \"#/definitions/card.AddCardChild\"\n      }\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"guide_title\": {\n      \"description\": \"引导语 多语言；  key：语言，val 为具体引导语\",\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"prompt_title\": {\n      \"description\": \"提示语 多语言；  key：语言，val 为具体引导语\",\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"type\": \"string\"\n      }\n    },\n    \"show_type\": {\n      \"description\": \"展示样式： 1:卡片形式；2:列表形式\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/card.AddCardReq\"\n}", "project_id": 16, "catid": 40, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}]}, {"index": 0, "name": "category", "desc": "category", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v1/category/del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["category"], "_id": 549, "method": "POST", "title": "del类目", "path": "/backend/v1/category/del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f875", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"category_id\"\n  ],\n  \"properties\": {\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.DelCategoryReq\"\n}", "project_id": 16, "catid": 42, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/category/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["category"], "_id": 557, "method": "POST", "title": "增加类目", "path": "/backend/v1/category/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f874", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"category_id\": {\n      \"description\": \"返回类目ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.AddCategoryResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"category_name\",\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"category_name\": {\n      \"description\": \"类目名称\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"parent_category_id\": {\n      \"description\": \"父类目ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.AddCategoryReq\"\n}", "project_id": 16, "catid": 42, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/category/tree", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["category"], "_id": 565, "method": "POST", "title": "类目全量查询", "path": "/backend/v1/category/tree", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f878", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"children\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"$ref\": \"#/definitions/category.CategoryTreeDetail\"\n        }\n      },\n      \"id\": {\n        \"type\": \"integer\"\n      },\n      \"label\": {\n        \"description\": \"栏目名称\",\n        \"type\": \"string\"\n      }\n    },\n    \"$$ref\": \"#/definitions/category.CategoryTreeDetail\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.CategoryTreeReq\"\n}", "project_id": 16, "catid": 42, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/category/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["category"], "_id": 573, "method": "POST", "title": "类目查询列表", "path": "/backend/v1/category/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f877", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"category_id\": {\n            \"description\": \"栏目\",\n            \"type\": \"integer\"\n          },\n          \"category_name\": {\n            \"description\": \"栏目名称\",\n            \"type\": \"string\"\n          },\n          \"create_time\": {\n            \"description\": \"创建状态  -- copier:\\\"-\\\" copier时忽略该字段\",\n            \"type\": \"integer\"\n          },\n          \"creator\": {\n            \"description\": \"创建人\",\n            \"type\": \"string\"\n          },\n          \"game_project\": {\n            \"description\": \"游戏项目名称\",\n            \"type\": \"string\"\n          },\n          \"parent_category_id\": {\n            \"description\": \"父栏目id\",\n            \"type\": \"integer\"\n          },\n          \"status\": {\n            \"description\": \"当前状态\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"description\": \"最后更新时间\",\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/category.ListCategoryDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.ListCategoryResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"parent_category_id\": {\n      \"description\": \"所有父类目下的子类目 =0 表示搜索第一层级\",\n      \"type\": \"integer\"\n    },\n    \"search\": {\n      \"description\": \"搜索文本\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.ListCategoryReq\"\n}", "project_id": 16, "catid": 42, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}, {"query_path": {"path": "/backend/v1/category/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["category"], "_id": 581, "method": "POST", "title": "编辑类目", "path": "/backend/v1/category/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f876", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.EditCategoryResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"category_id\",\n    \"category_name\"\n  ],\n  \"properties\": {\n    \"category_id\": {\n      \"description\": \"类目ID\",\n      \"type\": \"integer\"\n    },\n    \"category_name\": {\n      \"description\": \"类目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.EditCategoryReq\"\n}", "project_id": 16, "catid": 42, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}]}, {"index": 0, "name": "config", "desc": "config", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v2/show_config/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["config"], "_id": 11062, "method": "POST", "title": "展示配置 - 列表", "path": "/backend/v2/show_config/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6458610d50947e1b4f3433e4", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"card_child_idx\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"card_desc\": {\n            \"type\": \"string\"\n          },\n          \"card_id\": {\n            \"type\": \"integer\"\n          },\n          \"config_group\": {\n            \"type\": \"integer\"\n          },\n          \"game_project\": {\n            \"type\": \"string\"\n          },\n          \"group_desc\": {\n            \"type\": \"string\"\n          },\n          \"operator\": {\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.ShowConfigListDetail\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ShowConfigListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.HotCardListReq\"\n}", "project_id": 16, "catid": 44, "uid": 28, "add_time": 1683513613, "up_time": 1683513613, "__v": 0}, {"query_path": {"path": "/backend/v2/show_config/bind", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["config"], "_id": 11057, "method": "POST", "title": "展示配置绑定", "path": "/backend/v2/show_config/bind", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6458610d50947e1b4f3433e3", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"card_id\",\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"card_child_idx\": {\n      \"description\": \"子卡片index -- 在`VIP频道`必要; 从1开始；\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"card_id\": {\n      \"description\": \"热门卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"config_group\": {\n      \"description\": \"具体分组类型: 详细看枚举: 3:热门大卡 ID配置; 4:VIP频道 - 客服入口\",\n      \"type\": \"integer\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ShowBindReq\"\n}", "project_id": 16, "catid": 44, "uid": 28, "add_time": 1683513613, "up_time": 1683513613, "__v": 0}, {"query_path": {"path": "/backend/v1/config/base_add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["config"], "_id": 589, "method": "POST", "title": "新增基础配置", "path": "/backend/v1/config/base_add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "625d4f1850947e1b4f33cb05", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"config_id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BaseConfigAddResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"config_group\",\n    \"details\",\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"config_group\": {\n      \"description\": \"具体分组类型: 详细看枚举\",\n      \"type\": \"string\"\n    },\n    \"config_sub_group\": {\n      \"type\": \"string\"\n    },\n    \"details\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BaseConfigAddReq\"\n}", "project_id": 16, "catid": 44, "uid": 26, "add_time": 1636613269, "up_time": 1650282264, "__v": 0}, {"query_path": {"path": "/backend/v1/hotcard/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["config"], "_id": 8562, "method": "POST", "title": "热门大卡 - 列表", "path": "/backend/v1/hotcard/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f87d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"game_project\": {\n            \"type\": \"string\"\n          },\n          \"hot_card_desc\": {\n            \"type\": \"string\"\n          },\n          \"hot_card_id\": {\n            \"type\": \"integer\"\n          },\n          \"operator\": {\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.HotCardListDetail\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/question.HotCardListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.HotCardListReq\"\n}", "project_id": 16, "catid": 44, "uid": 28, "add_time": 1662627178, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/hotcard/bind", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["config"], "_id": 8557, "method": "POST", "title": "热门大卡配置", "path": "/backend/v1/hotcard/bind", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f87c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\",\n    \"hot_card_id\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"hot_card_id\": {\n      \"description\": \"热门卡片ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.HotCardBindReq\"\n}", "project_id": 16, "catid": 44, "uid": 28, "add_time": 1662627178, "up_time": 1711333689, "__v": 0}]}, {"index": 0, "name": "enum", "desc": "enum", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/backend/v1/get_log_type", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["enum"], "_id": 597, "method": "POST", "title": "自助查询接口类型返回", "path": "/backend/v1/get_log_type", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "625548ca50947e1b4f33c229", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\"type\":\"object\",\"additionalProperties\":{\"type\":\"object\",\"additionalProperties\":{\"type\":\"object\",\"properties\":{\"name\":{\"description\":\"枚举名称\",\"type\":\"string\"},\"value\":{\"description\":\"枚举值\",\"type\":\"string\"}},\"$$ref\":\"#/definitions/question.GetLogTypeResp\"}},\"properties\":{\"Coutries\":{\"type\":\"object\",\"properties\":{},\"description\":\"\\\"Countries\\\": {\\\"CN\\\":\\\"中国(CN)\\\",\\\"US\\\":\\\"美国(US)\\\"}\"},\"Langs\":{\"type\":\"object\",\"properties\":{},\"description\":\"\\\"Langs\\\":{\\\"zh-cn\\\":\\\"简体中文(zh-cn)\\\"}\"},\"...\":{\"type\":\"object\",\"properties\":{},\"description\":\"根据请求参数返回对应的节点\"}}}", "req_body_other": "{\"type\":\"object\",\"properties\":{\"enum_names\":{\"description\":\"枚举的名字,LogType：接口查询; Langs:所有的语种; PkgChannel:根据gameProject获取渠道; Countries:所有的国家\",\"type\":\"array\",\"items\":{\"type\":\"string\"},\"example\":[\"LogType\",\"Langs\"]},\"game_project\":{\"description\":\"游戏项目名称\",\"type\":\"string\"}},\"$$ref\":\"#/definitions/question.GetLogTypeReq\"}", "project_id": 16, "catid": 46, "uid": 26, "add_time": 1636613269, "up_time": 1649756362, "__v": 0, "desc": "", "markdown": ""}, {"query_path": {"path": "/backend/v1/get_enum", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["enum"], "_id": 605, "method": "POST", "title": "获取枚举值", "path": "/backend/v1/get_enum", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e139c0d4d108db80f87b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"additionalProperties\": {\n    \"type\": \"object\",\n    \"additionalProperties\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": {\n          \"description\": \"枚举名\",\n          \"type\": \"string\"\n        },\n        \"value\": {\n          \"description\": \"枚举值\",\n          \"type\": \"integer\"\n        }\n      },\n      \"$$ref\": \"#/definitions/question.RetEnum\"\n    }\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"enum_names\": {\n      \"description\": \"枚举的名字\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"example\": [\n        \"DataStatus\",\n        \"ProcessStatus\",\n        \"AnswerType\",\n        \"QueryFilterTime\",\n        \"AnnType\",\n        \"AnnouncementType\",\n        \"HasMarkedType\",\n        \"LogicStatus\",\n        \"PubStatus\"\n      ]\n    }\n  },\n  \"$$ref\": \"#/definitions/question.EnumsReq\"\n}", "project_id": 16, "catid": 46, "uid": 26, "add_time": 1636613269, "up_time": 1711333689, "__v": 0}]}, {"index": 0, "name": "api_question", "desc": "api_question", "add_time": 1636613267, "up_time": 1636613267, "list": [{"query_path": {"path": "/front/v1/question/self_check", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 613, "method": "POST", "title": "拉取自助查询信息 -- 只有在 answer_type = 2 时调用； 注意在返回code=10时 表示数据再异步整理中，需前端轮训拉取", "path": "/front/v1/question/self_check", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b2", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"after\": {\n        \"description\": \"变动后\",\n        \"type\": \"string\"\n      },\n      \"before\": {\n        \"description\": \"变动前\",\n        \"type\": \"string\"\n      },\n      \"change_amount\": {\n        \"description\": \"变化量 -- add\",\n        \"type\": \"string\"\n      },\n      \"date\": {\n        \"description\": \"时间\",\n        \"type\": \"string\"\n      },\n      \"desc\": {\n        \"description\": \"物品名称 -- add\",\n        \"type\": \"string\"\n      },\n      \"group\": {\n        \"description\": \"类型 ：enums.SelfCategoryItemGroup\",\n        \"type\": \"integer\"\n      },\n      \"high_num\": {\n        \"description\": \"历史最高分 -- add\",\n        \"type\": \"string\"\n      },\n      \"org_desc\": {\n        \"description\": \"物品名称 - 原始串\",\n        \"type\": \"string\"\n      },\n      \"org_reason\": {\n        \"description\": \"原因 - 原始串\",\n        \"type\": \"string\"\n      },\n      \"reason\": {\n        \"description\": \"原因\",\n        \"type\": \"string\"\n      },\n      \"ts\": {\n        \"description\": \"时间戳\",\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/frontentity.QuestionSelfCheckResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query_filter\": {\n      \"description\": \"查询条件\",\n      \"type\": \"integer\"\n    },\n    \"query_url_desc\": {\n      \"description\": \"查询地址\",\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionSelfCheckReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1636613269, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v2/question/self_check", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 10052, "method": "POST", "title": "拉取自助查询信息 -- 只有在 answer_type = 2 时调用； 注意在返回code=10时 表示数据再异步整理中，需前端轮训拉取", "path": "/front/v2/question/self_check", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b6", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"detail\": {\n      \"description\": \"详细信息 - list\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"after\": {\n            \"description\": \"变动后\",\n            \"type\": \"string\"\n          },\n          \"before\": {\n            \"description\": \"变动前\",\n            \"type\": \"string\"\n          },\n          \"change_amount\": {\n            \"description\": \"变化量 -- add\",\n            \"type\": \"string\"\n          },\n          \"date\": {\n            \"description\": \"时间\",\n            \"type\": \"string\"\n          },\n          \"desc\": {\n            \"description\": \"物品名称 -- add\",\n            \"type\": \"string\"\n          },\n          \"group\": {\n            \"description\": \"类型 ：enums.SelfCategoryItemGroup\",\n            \"type\": \"integer\"\n          },\n          \"high_num\": {\n            \"description\": \"历史最高分 -- add\",\n            \"type\": \"string\"\n          },\n          \"org_desc\": {\n            \"description\": \"物品名称 - 原始串\",\n            \"type\": \"string\"\n          },\n          \"org_reason\": {\n            \"description\": \"原因 - 原始串\",\n            \"type\": \"string\"\n          },\n          \"reason\": {\n            \"description\": \"原因\",\n            \"type\": \"string\"\n          },\n          \"ts\": {\n            \"description\": \"时间戳\",\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.QuestionSelfCheckResp\"\n      }\n    },\n    \"group\": {\n      \"description\": \"类型 ：enums.SelfCategoryItemGroup\",\n      \"type\": \"integer\"\n    },\n    \"meta\": {\n      \"description\": \"详细信息 -- 只针对ss 返回\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"desc\": {\n            \"type\": \"string\"\n          },\n          \"key\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.SelfCheckMetaDef\"\n      }\n    },\n    \"org_detail\": {\n      \"description\": \"原始串打印\",\n      \"type\": \"object\"\n    },\n    \"show_title\": {\n      \"description\": \"展示标题 -- 只针对ss 返回\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionSelfCheckRespV2\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query_filter\": {\n      \"description\": \"查询条件\",\n      \"type\": \"integer\"\n    },\n    \"query_url_desc\": {\n      \"description\": \"查询地址\",\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionSelfCheckReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1675047396, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/report/log", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 986, "method": "POST", "title": "点击日志上报", "path": "/front/v1/report/log", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b4", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"track_key\"\n  ],\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"log\": {\n      \"description\": \"上报点击日志\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ReportLogReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1637752212, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/dislike/save", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 8617, "method": "POST", "title": "点踩 - 原因保存", "path": "/front/v1/dislike/save", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8ae", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"question_id\",\n    \"reason_key\",\n    \"track_key\"\n  ],\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"对应的question_id\",\n      \"type\": \"integer\"\n    },\n    \"reason_key\": {\n      \"description\": \"玩家选择的结果\",\n      \"type\": \"string\"\n    },\n    \"reason_remark\": {\n      \"description\": \"备注\",\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.DislikeSaveReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1662627180, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/front/v1/dislike/opt", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 8612, "method": "POST", "title": "点踩 - 原因列表", "path": "/front/v1/dislike/opt", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8ad", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"desc\": {\n        \"description\": \"展示文案\",\n        \"type\": \"string\"\n      },\n      \"key\": {\n        \"description\": \"key\",\n        \"type\": \"string\"\n      }\n    },\n    \"$$ref\": \"#/definitions/frontentity.DislikeOptDetail\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.BaseFrontReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1662627180, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/front/v2/question/tk_cats", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 10057, "method": "POST", "title": "点踩后 - 工单分类列表", "path": "/front/v2/question/tk_cats", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b7", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"children\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"$ref\": \"#/definitions/category.CategoryTreeDetail\"\n      }\n    },\n    \"id\": {\n      \"type\": \"integer\"\n    },\n    \"label\": {\n      \"description\": \"栏目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/category.CategoryTreeDetail\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"track_key\"\n  ],\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"cat_id\": {\n      \"description\": \"二级/三级 使用此字段\",\n      \"type\": \"integer\"\n    },\n    \"cat_list\": {\n      \"description\": \"强制过滤ID - 获取一级使用此字段\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QstEvalCatsReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1675047396, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/question/card", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 621, "method": "POST", "title": "热门卡片", "path": "/front/v1/question/card", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8af", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ann_detail\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"ann_question_id\": {\n          \"description\": \"热门问题时的问题ID -- 业务暂时无用\",\n          \"type\": \"integer\"\n        },\n        \"ann_text\": {\n          \"description\": \"具体文案：文本类型： 具体展示的文本； 2：热门问题： 热门问题的描述\",\n          \"type\": \"string\"\n        },\n        \"ann_type\": {\n          \"description\": \"答案类型：1：文本； 2：热门问题\",\n          \"type\": \"integer\"\n        },\n        \"title\": {\n          \"description\": \"标题\",\n          \"type\": \"string\"\n        }\n      },\n      \"$$ref\": \"#/definitions/frontentity.FrontAnnDetail\"\n    },\n    \"card_guide_title\": {\n      \"description\": \"卡片引导语\",\n      \"type\": \"string\"\n    },\n    \"card_id\": {\n      \"description\": \"卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"card_prompt_title\": {\n      \"description\": \"卡片提示语\",\n      \"type\": \"string\"\n    },\n    \"children\": {\n      \"description\": \"子卡片信息\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"card_child_id\": {\n            \"description\": \"子卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"card_image\": {\n            \"description\": \"子卡片图片\",\n            \"type\": \"string\"\n          },\n          \"card_response_text\": {\n            \"description\": \"点击回复文本\",\n            \"type\": \"string\"\n          },\n          \"card_title\": {\n            \"description\": \"多语言信息\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.CardChild\"\n      }\n    },\n    \"faql_list\": {\n      \"description\": \"热门问题列表\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"question_content\": {\n            \"description\": \"问题 - 标准问\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"问题ID\",\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.FrontFaqlDetail\"\n      }\n    },\n    \"show_type\": {\n      \"description\": \"CardUniqueID string `json:\\\"card_unique_id\\\"` // 唯一标示\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionCardResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"track_key\"\n  ],\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"card_id\": {\n      \"type\": \"integer\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    },\n    \"with_ann_flag\": {\n      \"description\": \"是否获取公告信息 - bool：\",\n      \"type\": \"boolean\"\n    },\n    \"with_faql_flag\": {\n      \"description\": \"是否获取 热门问题列表 - bool：\",\n      \"type\": \"boolean\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionCardReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1636613269, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/front/v1/question/user_info", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 985, "method": "POST", "title": "用户游戏内详情数据", "path": "/front/v1/question/user_info", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b3", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"avatar\": {\n      \"type\": \"string\"\n    },\n    \"name\": {\n      \"type\": \"string\"\n    },\n    \"vip_level\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.UserInfoResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.BaseFrontReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1637752212, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/question/hot_cardid", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 8622, "method": "POST", "title": "获取游戏热门卡片ID", "path": "/front/v1/question/hot_cardid", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b0", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"card_id\": {\n      \"description\": \"游戏 - 热门卡片ID\",\n      \"type\": \"integer\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏标识：\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionHotCardResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.BaseFrontReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1662627180, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/question/score", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 629, "method": "POST", "title": "评价", "path": "/front/v1/question/score", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b1", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"对应的question_id\",\n      \"type\": \"integer\"\n    },\n    \"score\": {\n      \"description\": \"1:解决问题； -1：未解决问题\",\n      \"type\": \"integer\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionScoreReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1636613270, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v1/question/answer", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 637, "method": "POST", "title": "问题答案", "path": "/front/v1/question/answer", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "65095ac8c0d4d108db80e58b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"ai引擎类型： 1:自研AI算法 2：chatGPT\",\n      \"type\": \"integer\"\n    },\n    \"answer_details\": {\n      \"description\": \"具体问题详情： MatchType in (1,2)时有值\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_id\": {\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_url_desc\": {\n            \"description\": \"查询文本-调用接口\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"提交工单对应工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型: 1:富文本; 2:自助查询; 3:固定卡片；4:提交工单；100:以上都不是；\",\n            \"type\": \"integer\"\n          },\n          \"fuzzy_num\": {\n            \"description\": \"第N次匹配不佳 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"integer\"\n          },\n          \"question_desc\": {\n            \"description\": \"标准问题描述\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"标准问题ID\",\n            \"type\": \"integer\"\n          },\n          \"real_question_desc\": {\n            \"description\": \"玩家问题的详细 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.AnswerDetail\"\n      }\n    },\n    \"categories\": {\n      \"description\": \"分类\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"match_type\": {\n      \"description\": \"匹配类型 ： 1:准确匹配；2：模糊匹配；3：未匹配到\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionAnswerResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"ts\"\n  ],\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"fuzzy_num\": {\n      \"description\": \"点：\\\"以上都不是\\\" 时，使用 - 第N次匹配不佳\",\n      \"type\": \"integer\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"question_desc\": {\n      \"description\": \"问题描述\",\n      \"type\": \"string\"\n    },\n    \"question_refer\": {\n      \"description\": \"操作类型：1：玩家输入提问; 2：玩家点击问题\",\n      \"type\": \"integer\"\n    },\n    \"real_question_desc\": {\n      \"description\": \"玩家问题的详细 - \\\"以上都不是\\\"时透传\",\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ticket\": {\n      \"description\": \"工单标识\",\n      \"type\": \"boolean\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionAnswerReq\"\n}", "project_id": 16, "catid": 48, "uid": 26, "add_time": 1636613270, "up_time": 1695111880, "__v": 0}, {"query_path": {"path": "/front/v2/question/answer", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_question"], "_id": 14616, "method": "POST", "title": "问题答案", "path": "/front/v2/question/answer", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b5", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"ai引擎类型： 1:自研AI算法 2：chatGPT\",\n      \"type\": \"integer\"\n    },\n    \"answer_details\": {\n      \"description\": \"具体问题详情： MatchType in (1,2)时有值\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_id\": {\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_url_desc\": {\n            \"description\": \"查询文本-调用接口\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"提交工单对应工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型: 1:富文本; 2:自助查询; 3:固定卡片；4:提交工单；100:以上都不是；\",\n            \"type\": \"integer\"\n          },\n          \"fuzzy_num\": {\n            \"description\": \"第N次匹配不佳 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"integer\"\n          },\n          \"question_desc\": {\n            \"description\": \"标准问题描述\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"标准问题ID\",\n            \"type\": \"integer\"\n          },\n          \"real_question_desc\": {\n            \"description\": \"玩家问题的详细 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.AnswerDetail\"\n      }\n    },\n    \"categories\": {\n      \"description\": \"分类\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"invalid\": {\n      \"description\": \"问题是否无效 - 玩家输入内容“小于等于N个字符”时 为1，其他为0\",\n      \"type\": \"integer\"\n    },\n    \"match_type\": {\n      \"description\": \"匹配类型 ： 1:准确匹配；2：模糊匹配；3：未匹配到\",\n      \"type\": \"integer\"\n    },\n    \"more_list\": {\n      \"description\": \"更多参考问题列表\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"answer_card_id\": {\n            \"description\": \"答案 卡片ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_id\": {\n            \"type\": \"integer\"\n          },\n          \"answer_query_filer\": {\n            \"description\": \"调用远程查询条件\",\n            \"type\": \"integer\"\n          },\n          \"answer_query_prompt\": {\n            \"description\": \"自助查询 - 提示语\",\n            \"type\": \"string\"\n          },\n          \"answer_query_url_desc\": {\n            \"description\": \"查询文本-调用接口\",\n            \"type\": \"string\"\n          },\n          \"answer_rich_show_cat\": {\n            \"description\": \"评价绑定工单分类列表 [1,3,4]\",\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"integer\"\n            }\n          },\n          \"answer_rich_show_eval\": {\n            \"description\": \"展示评价信息字段\",\n            \"type\": \"integer\"\n          },\n          \"answer_rich_text\": {\n            \"description\": \"富文本内容\",\n            \"type\": \"string\"\n          },\n          \"answer_ticket_id\": {\n            \"description\": \"提交工单对应工单ID\",\n            \"type\": \"integer\"\n          },\n          \"answer_type\": {\n            \"description\": \"答案类型: 1:富文本; 2:自助查询; 3:固定卡片；4:提交工单；100:以上都不是；\",\n            \"type\": \"integer\"\n          },\n          \"fuzzy_num\": {\n            \"description\": \"第N次匹配不佳 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"integer\"\n          },\n          \"question_desc\": {\n            \"description\": \"标准问题描述\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"标准问题ID\",\n            \"type\": \"integer\"\n          },\n          \"real_question_desc\": {\n            \"description\": \"玩家问题的详细 - \\\"以上都不是\\\"时透传\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.AnswerDetail\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionAnswerV2Resp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"fuzzy_num\": {\n      \"description\": \"点：\\\"以上都不是\\\" 时，使用 - 第N次匹配不佳\",\n      \"type\": \"integer\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"question_desc\": {\n      \"description\": \"问题描述\",\n      \"type\": \"string\"\n    },\n    \"question_refer\": {\n      \"description\": \"操作类型：1：玩家输入提问; 2：玩家点击问题\",\n      \"type\": \"integer\"\n    },\n    \"real_question_desc\": {\n      \"description\": \"玩家问题的详细 - \\\"以上都不是\\\"时透传\",\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ticket\": {\n      \"description\": \"工单标识\",\n      \"type\": \"boolean\"\n    },\n    \"track_key\": {\n      \"description\": \"当前会话唯一ID\",\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.QuestionAnswerReq\"\n}", "project_id": 16, "catid": 48, "uid": 28, "add_time": 1695111880, "up_time": 1711333693, "__v": 0}]}, {"index": 0, "name": "statistics", "desc": "statistics", "add_time": 1637752210, "up_time": 1637752210, "list": [{"query_path": {"path": "/backend/v1/statistics/dialog/cate_tree", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8577, "method": "POST", "title": "会话原因分类", "path": "/backend/v1/statistics/dialog/cate_tree", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f885", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"child\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"$ref\": \"#/definitions/stats.DialCateDetailTree\"\n              }\n            },\n            \"desc\": {\n              \"type\": \"string\"\n            },\n            \"val\": {\n              \"type\": \"string\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.DialCateDetailTree\"\n        }\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.DialogueCateTreeResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"$$ref\": \"#/definitions/stats.Empty\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/none_above", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 10607, "method": "POST", "title": "匹配不佳报表接口", "path": "/backend/v1/statistics/none_above", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"current_page\": {\n        \"type\": \"integer\"\n      },\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"lang\": {\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"type\": \"string\"\n            },\n            \"session_id\": {\n              \"description\": \"会话id times大于1取第一条\",\n              \"type\": \"string\"\n            },\n            \"times\": {\n              \"description\": \"提问次数\",\n              \"type\": \"integer\"\n            },\n            \"uv\": {\n              \"description\": \"人次 - uv\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.QuestionList\"\n        }\n      },\n      \"per_page\": {\n        \"type\": \"integer\"\n      },\n      \"total\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.QuestionListResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"end\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"has_marked\": {\n      \"description\": \"是否完成原因分析 :1:是；-1：否\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"start\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.ListStatisticsReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1679543883, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/article/core", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8567, "method": "POST", "title": "文章报表", "path": "/backend/v1/statistics/article/core", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f883", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"appraise_count\": {\n            \"description\": \"评价次数\",\n            \"type\": \"integer\"\n          },\n          \"appraise_rate\": {\n            \"description\": \"评价率\",\n            \"type\": \"number\"\n          },\n          \"dislike_count\": {\n            \"description\": \"点踩次数\",\n            \"type\": \"integer\"\n          },\n          \"dislike_rate\": {\n            \"description\": \"点踩率\",\n            \"type\": \"number\"\n          },\n          \"lang\": {\n            \"type\": \"string\"\n          },\n          \"like_count\": {\n            \"description\": \"点赞次数\",\n            \"type\": \"integer\"\n          },\n          \"like_rate\": {\n            \"description\": \"点赞率\",\n            \"type\": \"number\"\n          },\n          \"pv\": {\n            \"description\": \"访问量\",\n            \"type\": \"integer\"\n          },\n          \"question_desc\": {\n            \"description\": \"文章名称\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"文章ID\",\n            \"type\": \"integer\"\n          },\n          \"uv\": {\n            \"description\": \"访问人数\",\n            \"type\": \"integer\"\n          }\n        },\n        \"$$ref\": \"#/definitions/stats.StatArticleCoreDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.StatArticleCoreResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"content_like\": {\n      \"description\": \"文章名称 - 模糊匹配\",\n      \"type\": \"string\"\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"dislike_count\": {\n      \"description\": \"点踩量\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"like_rate\": {\n      \"description\": \"点赞率\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"question_id\": {\n      \"description\": \"文章ID\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.StatArticleCoreReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627178, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/article/core_download", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8572, "method": "POST", "title": "文章报表-下载", "path": "/backend/v1/statistics/article/core_download", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f884", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"$$ref\": \"#/definitions/stats.Empty\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"content_like\": {\n      \"description\": \"文章名称 - 模糊匹配\",\n      \"type\": \"string\"\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"dislike_count\": {\n      \"description\": \"点踩量\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"like_rate\": {\n      \"description\": \"点赞率\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"question_id\": {\n      \"description\": \"文章ID\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.StatArticleCoreReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/miss", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 645, "method": "POST", "title": "未命中问题列表接口", "desc": "", "path": "/backend/v1/statistics/miss", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"current_page\": {\n        \"type\": \"integer\"\n      },\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"lang\": {\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"type\": \"string\"\n            },\n            \"session_id\": {\n              \"description\": \"会话id times大于1取第一条\",\n              \"type\": \"string\"\n            },\n            \"times\": {\n              \"description\": \"提问次数\",\n              \"type\": \"integer\"\n            },\n            \"uv\": {\n              \"description\": \"人次 - uv\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.QuestionList\"\n        }\n      },\n      \"per_page\": {\n        \"type\": \"integer\"\n      },\n      \"total\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.QuestionListResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"end\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"has_marked\": {\n      \"description\": \"是否完成原因分析 :1:是；-1：否\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"start\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.ListStatisticsReq\"\n}", "project_id": 16, "catid": 159, "uid": 26, "add_time": 1636613270, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/dialog/mark", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8592, "method": "POST", "title": "标记会话原因", "path": "/backend/v1/statistics/dialog/mark", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f888", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"$$ref\": \"#/definitions/stats.Empty\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"detail\": {\n      \"description\": \"保存数据\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"act_event\": {\n            \"description\": \"交互类型\",\n            \"type\": \"string\"\n          },\n          \"dial_cate\": {\n            \"description\": \"一级原因\",\n            \"type\": \"string\"\n          },\n          \"dial_sub_cate\": {\n            \"description\": \"二级原因\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"问题ID\",\n            \"type\": \"integer\"\n          },\n          \"session_id\": {\n            \"description\": \"会话ID\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/stats.DialogueMarkSaveSingle\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.DialogueMarkReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/overview/core", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 3567, "method": "POST", "title": "概览核心数据接口", "path": "/backend/v1/statistics/overview/core", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"answer_match_count\": {\n      \"description\": \"召回次数\",\n      \"type\": \"integer\"\n    },\n    \"answer_match_rate\": {\n      \"description\": \"召回率\",\n      \"type\": \"number\"\n    },\n    \"answer_mismatch_count\": {\n      \"description\": \"未召回次数\",\n      \"type\": \"integer\"\n    },\n    \"appraise_count\": {\n      \"description\": \"评价次数\",\n      \"type\": \"integer\"\n    },\n    \"appraise_rate\": {\n      \"description\": \"评价率\",\n      \"type\": \"number\"\n    },\n    \"consecutive_misses_count\": {\n      \"description\": \"连续未命中次数\",\n      \"type\": \"integer\"\n    },\n    \"consecutive_misses_rate\": {\n      \"description\": \"连续未命中率\",\n      \"type\": \"number\"\n    },\n    \"dislike_count\": {\n      \"description\": \"点踩次数\",\n      \"type\": \"integer\"\n    },\n    \"dislike_rate\": {\n      \"description\": \"点踩率\",\n      \"type\": \"number\"\n    },\n    \"dt\": {\n      \"description\": \"时间\",\n      \"type\": \"string\"\n    },\n    \"first_none_of_above_rate\": {\n      \"description\": \"首次匹配不佳率\",\n      \"type\": \"number\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"like_count\": {\n      \"description\": \"点赞次数\",\n      \"type\": \"integer\"\n    },\n    \"like_rate\": {\n      \"description\": \"点赞率\",\n      \"type\": \"number\"\n    },\n    \"match_correct_rate\": {\n      \"description\": \"推送准确率 = (有匹配结果-以上都不是)/有匹配结果\",\n      \"type\": \"number\"\n    },\n    \"match_type_precise_count\": {\n      \"description\": \"精确匹配到文章的数量（剔除卡片类型的结果）\",\n      \"type\": \"integer\"\n    },\n    \"new_users_count\": {\n      \"description\": \"新增用户数\",\n      \"type\": \"integer\"\n    },\n    \"none_of_above_count\": {\n      \"description\": \"add 2023.03.22\",\n      \"type\": \"integer\"\n    },\n    \"pv\": {\n      \"description\": \"访问量\",\n      \"type\": \"integer\"\n    },\n    \"resolve_rate\": {\n      \"description\": \"解决率\",\n      \"type\": \"number\"\n    },\n    \"second_hit_count\": {\n      \"description\": \"二次命中 -- 第一次未命中，第二次命中\",\n      \"type\": \"integer\"\n    },\n    \"second_hit_rate\": {\n      \"description\": \"二次命中率 -- 二次命中/总会话数\",\n      \"type\": \"number\"\n    },\n    \"second_none_of_above_count\": {\n      \"description\": \"连续匹配不佳次数 -- 玩家第二次点击“以上都没有”选项的次数\",\n      \"type\": \"integer\"\n    },\n    \"second_none_of_above_rate\": {\n      \"description\": \"连续匹配不佳率 前10次模糊匹配出现2次\\\"以上都不是\\\"/模糊匹配会话次数\",\n      \"type\": \"number\"\n    },\n    \"turn_artificial_count\": {\n      \"description\": \"转人工次数\",\n      \"type\": \"integer\"\n    },\n    \"turn_artificial_rate\": {\n      \"description\": \"转人工率\",\n      \"type\": \"number\"\n    },\n    \"uv\": {\n      \"description\": \"访问人数\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewRes\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"new_user\": {\n      \"type\": \"boolean\"\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewReq\"\n}", "project_id": 16, "catid": 159, "uid": 288, "add_time": 1649670186, "up_time": 1711333691, "__v": 0, "desc": "", "markdown": ""}, {"query_path": {"path": "/backend/v1/statistics/overview/detail/download", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 3577, "method": "POST", "title": "概览详情下载接口", "path": "/backend/v1/statistics/overview/detail/download", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"$$ref\": \"#/definitions/stats.Empty\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"new_user\": {\n      \"type\": \"boolean\"\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewReq\"\n}", "project_id": 16, "catid": 159, "uid": 288, "add_time": 1649670186, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/overview/detail/download_detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8597, "method": "POST", "title": "概览详情下载明细数据", "path": "/backend/v1/statistics/overview/detail/download_detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f890", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"$$ref\": \"#/definitions/stats.Empty\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"new_user\": {\n      \"type\": \"boolean\"\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/overview/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 3572, "method": "POST", "title": "概览详情数据接口", "path": "/backend/v1/statistics/overview/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"all\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"answer_match_count\": {\n          \"description\": \"召回次数\",\n          \"type\": \"integer\"\n        },\n        \"answer_match_rate\": {\n          \"description\": \"召回率\",\n          \"type\": \"number\"\n        },\n        \"answer_mismatch_count\": {\n          \"description\": \"未召回次数\",\n          \"type\": \"integer\"\n        },\n        \"appraise_count\": {\n          \"description\": \"评价次数\",\n          \"type\": \"integer\"\n        },\n        \"appraise_rate\": {\n          \"description\": \"评价率\",\n          \"type\": \"number\"\n        },\n        \"consecutive_misses_count\": {\n          \"description\": \"连续未命中次数\",\n          \"type\": \"integer\"\n        },\n        \"consecutive_misses_rate\": {\n          \"description\": \"连续未命中率\",\n          \"type\": \"number\"\n        },\n        \"dislike_count\": {\n          \"description\": \"点踩次数\",\n          \"type\": \"integer\"\n        },\n        \"dislike_rate\": {\n          \"description\": \"点踩率\",\n          \"type\": \"number\"\n        },\n        \"dt\": {\n          \"description\": \"时间\",\n          \"type\": \"string\"\n        },\n        \"first_none_of_above_rate\": {\n          \"description\": \"首次匹配不佳率\",\n          \"type\": \"number\"\n        },\n        \"lang\": {\n          \"description\": \"语种\",\n          \"type\": \"string\"\n        },\n        \"like_count\": {\n          \"description\": \"点赞次数\",\n          \"type\": \"integer\"\n        },\n        \"like_rate\": {\n          \"description\": \"点赞率\",\n          \"type\": \"number\"\n        },\n        \"match_correct_rate\": {\n          \"description\": \"推送准确率 = (有匹配结果-以上都不是)/有匹配结果\",\n          \"type\": \"number\"\n        },\n        \"match_type_precise_count\": {\n          \"description\": \"精确匹配到文章的数量（剔除卡片类型的结果）\",\n          \"type\": \"integer\"\n        },\n        \"new_users_count\": {\n          \"description\": \"新增用户数\",\n          \"type\": \"integer\"\n        },\n        \"none_of_above_count\": {\n          \"description\": \"add 2023.03.22\",\n          \"type\": \"integer\"\n        },\n        \"pv\": {\n          \"description\": \"访问量\",\n          \"type\": \"integer\"\n        },\n        \"resolve_rate\": {\n          \"description\": \"解决率\",\n          \"type\": \"number\"\n        },\n        \"second_hit_count\": {\n          \"description\": \"二次命中 -- 第一次未命中，第二次命中\",\n          \"type\": \"integer\"\n        },\n        \"second_hit_rate\": {\n          \"description\": \"二次命中率 -- 二次命中/总会话数\",\n          \"type\": \"number\"\n        },\n        \"second_none_of_above_count\": {\n          \"description\": \"连续匹配不佳次数 -- 玩家第二次点击“以上都没有”选项的次数\",\n          \"type\": \"integer\"\n        },\n        \"second_none_of_above_rate\": {\n          \"description\": \"连续匹配不佳率 前10次模糊匹配出现2次\\\"以上都不是\\\"/模糊匹配会话次数\",\n          \"type\": \"number\"\n        },\n        \"turn_artificial_count\": {\n          \"description\": \"转人工次数\",\n          \"type\": \"integer\"\n        },\n        \"turn_artificial_rate\": {\n          \"description\": \"转人工率\",\n          \"type\": \"number\"\n        },\n        \"uv\": {\n          \"description\": \"访问人数\",\n          \"type\": \"integer\"\n        }\n      },\n      \"$$ref\": \"#/definitions/stats.OverviewRes\"\n    },\n    \"self_service\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"card_pv\": {\n          \"description\": \"卡片查询量\",\n          \"type\": \"integer\"\n        },\n        \"fuzzy_match_pv\": {\n          \"description\": \"模糊匹配查询量\",\n          \"type\": \"integer\"\n        },\n        \"fuzzy_match_rate\": {\n          \"description\": \"模糊匹配查询率\",\n          \"type\": \"number\"\n        },\n        \"invalid_count\": {\n          \"description\": \"无效匹配查询量\",\n          \"type\": \"integer\"\n        },\n        \"invalid_rate\": {\n          \"description\": \"无效匹配查询率\",\n          \"type\": \"number\"\n        },\n        \"synthesis_dislike_count\": {\n          \"description\": \"富文本查询点踩次数\",\n          \"type\": \"integer\"\n        },\n        \"synthesis_dislike_rate\": {\n          \"description\": \"富文本查询点踩率\",\n          \"type\": \"number\"\n        },\n        \"synthesis_like_count\": {\n          \"description\": \"富文本查询点赞次数\",\n          \"type\": \"integer\"\n        },\n        \"synthesis_like_rate\": {\n          \"description\": \"富文本查询点赞率\",\n          \"type\": \"number\"\n        },\n        \"synthesis_pv\": {\n          \"description\": \"富文本查询量\",\n          \"type\": \"integer\"\n        },\n        \"table_dislike_count\": {\n          \"description\": \"自助查询点踩次数\",\n          \"type\": \"integer\"\n        },\n        \"table_dislike_rate\": {\n          \"description\": \"自助查询点踩率\",\n          \"type\": \"number\"\n        },\n        \"table_like_count\": {\n          \"description\": \"自助查询点赞次数\",\n          \"type\": \"integer\"\n        },\n        \"table_like_rate\": {\n          \"description\": \"自助查询点赞率\",\n          \"type\": \"number\"\n        },\n        \"table_pv\": {\n          \"description\": \"自助查询量\",\n          \"type\": \"integer\"\n        }\n      },\n      \"$$ref\": \"#/definitions/stats.OverviewDetailResSelServ\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewDetailRes\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"channel\": {\n      \"description\": \"渠道\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"country\": {\n      \"description\": \"国家code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言code\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"new_user\": {\n      \"type\": \"boolean\"\n    },\n    \"server_arr\": {\n      \"description\": \"区服数组，如: [[1,100],[101,200]]\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\"\n      }\n    },\n    \"server_ids\": {\n      \"description\": \"多个区服id用逗号分隔，如：\\\"1,2,3\\\"\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewReq\"\n}", "project_id": 16, "catid": 159, "uid": 288, "add_time": 1649670186, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/overview/trend", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 3582, "method": "POST", "title": "概览趋势图接口", "path": "/backend/v1/statistics/overview/trend", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f891", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"data\": {\n        \"description\": \"趋势图数据, [ [\\\"2022-04-01\\\":80], [\\\"2022-04-02\\\":85] ]\",\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\"\n          }\n        }\n      },\n      \"name\": {\n        \"description\": \"趋势图折线名，answer_match_rate:召回率 resolve_rate:解决率 like_rate:点赞率\",\n        \"type\": \"string\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.OverviewTrendRes\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"days\": {\n      \"description\": \"查询天数，默认：7/30\",\n      \"type\": \"integer\"\n    },\n    \"game\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.OverviewTrendReq\"\n}", "project_id": 16, "catid": 159, "uid": 288, "add_time": 1649670187, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/similarity", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 653, "method": "POST", "title": "相似问题列表接口", "desc": "", "path": "/backend/v1/statistics/similarity", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f892", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"current_page\": {\n        \"type\": \"integer\"\n      },\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"lang\": {\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"type\": \"string\"\n            },\n            \"session_id\": {\n              \"description\": \"会话id times大于1取第一条\",\n              \"type\": \"string\"\n            },\n            \"times\": {\n              \"description\": \"提问次数\",\n              \"type\": \"integer\"\n            },\n            \"uv\": {\n              \"description\": \"人次 - uv\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.QuestionList\"\n        }\n      },\n      \"per_page\": {\n        \"type\": \"integer\"\n      },\n      \"total\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.QuestionListResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"end\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"has_marked\": {\n      \"description\": \"是否完成原因分析 :1:是；-1：否\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"start\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.ListStatisticsReq\"\n}", "project_id": 16, "catid": 159, "uid": 26, "add_time": 1636613270, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/export", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 677, "method": "POST", "title": "统计报表下载接口", "desc": "", "path": "/backend/v1/statistics/export", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f88a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"next\": {\n        \"type\": \"string\"\n      },\n      \"prev\": {\n        \"type\": \"string\"\n      },\n      \"question\": {\n        \"type\": \"string\"\n      },\n      \"session\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"answer_lists\": {\n              \"description\": \"返回问题列表\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"question_desc\": {\n                    \"type\": \"string\"\n                  },\n                  \"question_id\": {\n                    \"type\": \"integer\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueSimilarList\"\n              }\n            },\n            \"auto_list\": {\n              \"description\": \"自助查询list\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"after\": {\n                    \"type\": \"string\"\n                  },\n                  \"before\": {\n                    \"type\": \"string\"\n                  },\n                  \"change_amount\": {\n                    \"description\": \"变化量 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"date\": {\n                    \"type\": \"string\"\n                  },\n                  \"desc\": {\n                    \"description\": \"物品名称 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"group\": {\n                    \"description\": \"类型 ：enums.SelfCategoryItemGroup\",\n                    \"type\": \"integer\"\n                  },\n                  \"high_num\": {\n                    \"description\": \"历史最高分 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"reason\": {\n                    \"type\": \"string\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueAutoList\"\n              }\n            },\n            \"card_list\": {\n              \"description\": \"查询卡片列表\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"card_child_id\": {\n                    \"type\": \"integer\"\n                  },\n                  \"card_id\": {\n                    \"type\": \"integer\"\n                  },\n                  \"card_image\": {\n                    \"description\": \"子卡片图片\",\n                    \"type\": \"string\"\n                  },\n                  \"card_response_text\": {\n                    \"description\": \"点击回复文本\",\n                    \"type\": \"string\"\n                  },\n                  \"card_title\": {\n                    \"description\": \"多语言信息\",\n                    \"type\": \"string\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueCardList\"\n              }\n            },\n            \"create_time\": {\n              \"description\": \"添加时间\",\n              \"type\": \"string\"\n            },\n            \"dialogue_group\": {\n              \"description\": \"操作类型 ： 1: 答案列表；2:字符串； 3:富文本; 4: 自助查询; 5:卡片详情； 6：评分; 7:未匹配到\",\n              \"type\": \"integer\"\n            },\n            \"fpid\": {\n              \"description\": \"对应fpid\",\n              \"type\": \"integer\"\n            },\n            \"fuzzy_num\": {\n              \"description\": \"\\\"以上都不是\\\"的次数\",\n              \"type\": \"integer\"\n            },\n            \"game_id\": {\n              \"description\": \"游戏对应game_id\",\n              \"type\": \"integer\"\n            },\n            \"game_project\": {\n              \"description\": \"游戏项目ID\",\n              \"type\": \"string\"\n            },\n            \"lang\": {\n              \"description\": \"对应语言\",\n              \"type\": \"string\"\n            },\n            \"other_qst_desc\": {\n              \"description\": \"\\\"以上都不是\\\"的具体问题\",\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"description\": \"对应问题\",\n              \"type\": \"string\"\n            },\n            \"result_text\": {\n              \"description\": \"文本 或者 富文本\",\n              \"type\": \"string\"\n            },\n            \"score\": {\n              \"description\": \"问题是否解决 ： 1解决；-1未解决；\",\n              \"type\": \"integer\"\n            },\n            \"session_id\": {\n              \"type\": \"string\"\n            },\n            \"uid\": {\n              \"description\": \"对应uid\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.DialogueSession\"\n        }\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.DialogueResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"type\"\n  ],\n  \"properties\": {\n    \"end\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"has_marked\": {\n      \"description\": \"是否完成原因分析 :1:是；-1：否\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"start\": {\n      \"type\": \"string\"\n    },\n    \"type\": {\n      \"description\": \"1：评价报表；2：相似问题报表；3：未命中报表；100：匹配不佳报表\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.ListStatsExportReq\"\n}", "project_id": 16, "catid": 159, "uid": 26, "add_time": 1636613270, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/appraise", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 661, "method": "POST", "title": "评价列表接口", "desc": "", "path": "/backend/v1/statistics/appraise", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f882", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"current_page\": {\n        \"type\": \"integer\"\n      },\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"has_marked\": {\n              \"description\": \"完成原因分析: >0 是； <=0 否\",\n              \"type\": \"integer\"\n            },\n            \"lang\": {\n              \"description\": \"语言\",\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"description\": \"问题\",\n              \"type\": \"string\"\n            },\n            \"question_id\": {\n              \"description\": \"question_id\",\n              \"type\": \"integer\"\n            },\n            \"session_id\": {\n              \"description\": \"会话id times大于1取第一条\",\n              \"type\": \"string\"\n            },\n            \"solved\": {\n              \"description\": \"解决\",\n              \"type\": \"integer\"\n            },\n            \"unsolved\": {\n              \"description\": \"未解决\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.AppraiseList\"\n        }\n      },\n      \"per_page\": {\n        \"type\": \"integer\"\n      },\n      \"total\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.AppraiseListResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"end\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"has_marked\": {\n      \"description\": \"是否完成原因分析 :1:是；-1：否\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"start\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.ListStatisticsReq\"\n}", "project_id": 16, "catid": 159, "uid": 26, "add_time": 1636613270, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/dialog/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8582, "method": "POST", "title": "评价报表列表", "path": "/backend/v1/statistics/dialog/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f886", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"current_page\": {\n        \"type\": \"integer\"\n      },\n      \"data\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"act_event\": {\n              \"description\": \"交互类型\",\n              \"type\": \"string\"\n            },\n            \"dial_cate\": {\n              \"description\": \"一级原因\",\n              \"type\": \"string\"\n            },\n            \"dial_sub_cate\": {\n              \"description\": \"二级原因\",\n              \"type\": \"string\"\n            },\n            \"fpid\": {\n              \"description\": \"fpid\",\n              \"type\": \"integer\"\n            },\n            \"question\": {\n              \"description\": \"问题描述\",\n              \"type\": \"string\"\n            },\n            \"question_id\": {\n              \"description\": \"问题id\",\n              \"type\": \"integer\"\n            },\n            \"session_id\": {\n              \"description\": \"session_id\",\n              \"type\": \"string\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.DialListDetail\"\n        }\n      },\n      \"per_page\": {\n        \"type\": \"integer\"\n      },\n      \"total\": {\n        \"type\": \"integer\"\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.DialListResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"dialogue_type\": {\n      \"type\": \"integer\"\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"evaluate_type\": {\n      \"description\": \"1: 点赞 2: 点踩\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"question\": {\n      \"description\": \"用户问题\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.DialListReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/dialog/list_export", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 8587, "method": "POST", "title": "评价报表列表 - 下载", "path": "/backend/v1/statistics/dialog/list_export", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13ac0d4d108db80f887", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"$$ref\": \"#/definitions/stats.Empty\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"dialogue_type\": {\n      \"type\": \"integer\"\n    },\n    \"end_time\": {\n      \"description\": \"结束时间\",\n      \"type\": \"string\"\n    },\n    \"evaluate_type\": {\n      \"description\": \"1: 点赞 2: 点踩\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    },\n    \"question\": {\n      \"description\": \"用户问题\",\n      \"type\": \"string\"\n    },\n    \"start_time\": {\n      \"description\": \"开始时间\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.DialListReq\"\n}", "project_id": 16, "catid": 159, "uid": 28, "add_time": 1662627179, "up_time": 1711333690, "__v": 0}, {"query_path": {"path": "/backend/v1/statistics/dialogue", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["statistics"], "_id": 669, "method": "POST", "title": "问题对话接口", "desc": "", "path": "/backend/v1/statistics/dialogue", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f889", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"next\": {\n        \"type\": \"string\"\n      },\n      \"prev\": {\n        \"type\": \"string\"\n      },\n      \"question\": {\n        \"type\": \"string\"\n      },\n      \"session\": {\n        \"type\": \"array\",\n        \"items\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"answer_lists\": {\n              \"description\": \"返回问题列表\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"question_desc\": {\n                    \"type\": \"string\"\n                  },\n                  \"question_id\": {\n                    \"type\": \"integer\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueSimilarList\"\n              }\n            },\n            \"auto_list\": {\n              \"description\": \"自助查询list\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"after\": {\n                    \"type\": \"string\"\n                  },\n                  \"before\": {\n                    \"type\": \"string\"\n                  },\n                  \"change_amount\": {\n                    \"description\": \"变化量 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"date\": {\n                    \"type\": \"string\"\n                  },\n                  \"desc\": {\n                    \"description\": \"物品名称 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"group\": {\n                    \"description\": \"类型 ：enums.SelfCategoryItemGroup\",\n                    \"type\": \"integer\"\n                  },\n                  \"high_num\": {\n                    \"description\": \"历史最高分 -- add\",\n                    \"type\": \"string\"\n                  },\n                  \"reason\": {\n                    \"type\": \"string\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueAutoList\"\n              }\n            },\n            \"card_list\": {\n              \"description\": \"查询卡片列表\",\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"card_child_id\": {\n                    \"type\": \"integer\"\n                  },\n                  \"card_id\": {\n                    \"type\": \"integer\"\n                  },\n                  \"card_image\": {\n                    \"description\": \"子卡片图片\",\n                    \"type\": \"string\"\n                  },\n                  \"card_response_text\": {\n                    \"description\": \"点击回复文本\",\n                    \"type\": \"string\"\n                  },\n                  \"card_title\": {\n                    \"description\": \"多语言信息\",\n                    \"type\": \"string\"\n                  }\n                },\n                \"$$ref\": \"#/definitions/stats.DialogueCardList\"\n              }\n            },\n            \"create_time\": {\n              \"description\": \"添加时间\",\n              \"type\": \"string\"\n            },\n            \"dialogue_group\": {\n              \"description\": \"操作类型 ： 1: 答案列表；2:字符串； 3:富文本; 4: 自助查询; 5:卡片详情； 6：评分; 7:未匹配到\",\n              \"type\": \"integer\"\n            },\n            \"fpid\": {\n              \"description\": \"对应fpid\",\n              \"type\": \"integer\"\n            },\n            \"fuzzy_num\": {\n              \"description\": \"\\\"以上都不是\\\"的次数\",\n              \"type\": \"integer\"\n            },\n            \"game_id\": {\n              \"description\": \"游戏对应game_id\",\n              \"type\": \"integer\"\n            },\n            \"game_project\": {\n              \"description\": \"游戏项目ID\",\n              \"type\": \"string\"\n            },\n            \"lang\": {\n              \"description\": \"对应语言\",\n              \"type\": \"string\"\n            },\n            \"other_qst_desc\": {\n              \"description\": \"\\\"以上都不是\\\"的具体问题\",\n              \"type\": \"string\"\n            },\n            \"question_content\": {\n              \"description\": \"对应问题\",\n              \"type\": \"string\"\n            },\n            \"result_text\": {\n              \"description\": \"文本 或者 富文本\",\n              \"type\": \"string\"\n            },\n            \"score\": {\n              \"description\": \"问题是否解决 ： 1解决；-1未解决；\",\n              \"type\": \"integer\"\n            },\n            \"session_id\": {\n              \"type\": \"string\"\n            },\n            \"uid\": {\n              \"description\": \"对应uid\",\n              \"type\": \"integer\"\n            }\n          },\n          \"$$ref\": \"#/definitions/stats.DialogueSession\"\n        }\n      }\n    },\n    \"$$ref\": \"#/definitions/stats.DialogueResp\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"dialogue_type\",\n    \"session_id\"\n  ],\n  \"properties\": {\n    \"dialogue_type\": {\n      \"description\": \"1: 评价报表； 2: 相似问题； 3: 未命中问题； 100: 匹配不佳报表\",\n      \"type\": \"integer\"\n    },\n    \"evaluate_type\": {\n      \"description\": \"1: 点赞 2: 点踩 3 精准匹配不统计评价结果\",\n      \"type\": \"integer\"\n    },\n    \"question\": {\n      \"type\": \"string\"\n    },\n    \"session_id\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/stats.DialogueReq\"\n}", "project_id": 16, "catid": 159, "uid": 26, "add_time": 1636613270, "up_time": 1711333691, "__v": 0}]}, {"index": 0, "name": "auto_save_temp", "desc": "auto_save_temp", "add_time": 1649670185, "up_time": 1649670185, "list": [{"query_path": {"path": "/backend/v2/autosave/del_all", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["auto_save_temp"], "_id": 3592, "method": "POST", "title": "清空数据", "path": "/backend/v2/autosave/del_all", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f89e", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"source_action\",\n    \"source_group\",\n    \"unique_id\"\n  ],\n  \"properties\": {\n    \"source_action\": {\n      \"description\": \"具体操作action ：CsAddCard/CsEditCard\",\n      \"type\": \"string\"\n    },\n    \"source_group\": {\n      \"description\": \"模版ID -- 固定字段cs-api\",\n      \"type\": \"string\"\n    },\n    \"unique_id\": {\n      \"description\": \"唯一标识： 新增：用户名(eg：yafei.wang)；修改：用户名_唯一ID(eg:yafei.wang_123)\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/autosave.AutoSaveDelAllReq\"\n}", "project_id": 16, "catid": 905, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/autosave/add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["auto_save_temp"], "_id": 3587, "method": "POST", "title": "自动保存", "path": "/backend/v2/autosave/add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f89d", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"detail\",\n    \"game_project\",\n    \"source_action\",\n    \"source_group\",\n    \"unique_id\"\n  ],\n  \"properties\": {\n    \"detail\": {\n      \"description\": \"具体详情 ， jsonstr\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言 - 非必填\",\n      \"type\": \"string\"\n    },\n    \"source_action\": {\n      \"description\": \"具体操作action ：CsAddCard/CsEditCard\",\n      \"type\": \"string\"\n    },\n    \"source_group\": {\n      \"description\": \"模版ID -- 固定字段cs-api\",\n      \"type\": \"string\"\n    },\n    \"unique_id\": {\n      \"description\": \"唯一标识： 新增：用户名(eg：yaife.wang)；修改：用户名_唯一ID(eg:yafei.wang_123)\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/autosave.AddAutoSaveReq\"\n}", "project_id": 16, "catid": 905, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/autosave/get_latest", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["auto_save_temp"], "_id": 3597, "method": "POST", "title": "自动保存", "path": "/backend/v2/autosave/get_latest", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f89f", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"create_time\",\n    \"detail\",\n    \"game_project\",\n    \"operator\",\n    \"source_action\",\n    \"source_group\",\n    \"unique_id\"\n  ],\n  \"properties\": {\n    \"create_time\": {\n      \"description\": \"新增时间\",\n      \"type\": \"string\"\n    },\n    \"detail\": {\n      \"description\": \"具体详情 ， jsonstr\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言 - 非必填\",\n      \"type\": \"string\"\n    },\n    \"operator\": {\n      \"description\": \"操作人\",\n      \"type\": \"string\"\n    },\n    \"source_action\": {\n      \"description\": \"具体操作action ：CsAddCard/CsEditCard\",\n      \"type\": \"string\"\n    },\n    \"source_group\": {\n      \"description\": \"模版ID -- 固定字段cs-api\",\n      \"type\": \"string\"\n    },\n    \"unique_id\": {\n      \"description\": \"唯一标识： 新增：用户名(eg：yafei.wang)；修改：用户名_唯一ID(eg:yafei.wang_123)\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/autosave.AutoSaveDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"source_action\",\n    \"source_group\",\n    \"unique_id\"\n  ],\n  \"properties\": {\n    \"source_action\": {\n      \"description\": \"具体操作action ：CsAddCard/CsEditCard\",\n      \"type\": \"string\"\n    },\n    \"source_group\": {\n      \"description\": \"模版ID -- 固定字段cs-api\",\n      \"type\": \"string\"\n    },\n    \"unique_id\": {\n      \"description\": \"唯一标识： 新增：用户名(eg：yafei.wang)；修改：用户名_唯一ID(eg:yafei.wang_123)\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/autosave.AutoSaveDetaiReq\"\n}", "project_id": 16, "catid": 905, "uid": 288, "add_time": 1649670187, "up_time": 1711333692, "__v": 0}]}, {"index": 0, "name": "Mirror", "desc": "Mirror", "add_time": 1662627177, "up_time": 1662627177, "list": [{"query_path": {"path": "/backend/v2/mirror/game_all", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Mirror"], "_id": 8602, "method": "POST", "title": "镜像数据", "path": "/backend/v2/mirror/game_all", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a1", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"from_game_project\",\n    \"to_game_project\"\n  ],\n  \"properties\": {\n    \"from_game_project\": {\n      \"description\": \"源项目\",\n      \"type\": \"string\"\n    },\n    \"to_game_project\": {\n      \"description\": \"to项目\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.MirrorGameInfoAllReq\"\n}", "project_id": 16, "catid": 1670, "uid": 28, "add_time": 1662627180, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/mirror/history", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Mirror"], "_id": 8607, "method": "POST", "title": "镜像数据", "path": "/backend/v2/mirror/history", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a2", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"desc\": {\n            \"description\": \"描述\",\n            \"type\": \"string\"\n          },\n          \"status\": {\n            \"description\": \"状态\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"description\": \"更新时间\",\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"description\": \"更新人\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.MirrorGameDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.MirrorGameListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.BatchImportLogReq\"\n}", "project_id": 16, "catid": 1670, "uid": 28, "add_time": 1662627180, "up_time": 1711333692, "__v": 0}]}, {"index": 0, "name": "Announcement", "desc": "Announcement", "add_time": 1675047393, "up_time": 1675047393, "list": [{"query_path": {"path": "/backend/v2/announcement/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Announcement"], "_id": 10007, "method": "POST", "title": "公告 - 详情", "path": "/backend/v2/announcement/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f89a", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ann_question_id\": {\n      \"description\": \"公告 - 关联热门问题\",\n      \"type\": \"integer\"\n    },\n    \"ann_text\": {\n      \"description\": \"公告文本\",\n      \"type\": \"string\"\n    },\n    \"ann_type\": {\n      \"description\": \"公告类型： 1: 文本；2：关联热门问题\",\n      \"type\": \"integer\"\n    },\n    \"desc\": {\n      \"description\": \"公告名称\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"对应游戏\",\n      \"type\": \"string\"\n    },\n    \"id\": {\n      \"description\": \"公告ID\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"pub_status\": {\n      \"description\": \"发布状态：1：启用； -1禁用；\",\n      \"type\": \"integer\"\n    },\n    \"title\": {\n      \"description\": \"引导语\",\n      \"type\": \"string\"\n    },\n    \"update_time\": {\n      \"description\": \"更新时间\",\n      \"type\": \"string\"\n    },\n    \"updator\": {\n      \"description\": \"操作人\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementDetailReq\"\n}", "project_id": 16, "catid": 1980, "uid": 28, "add_time": 1675047395, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v2/announcement/list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Announcement"], "_id": 10017, "method": "POST", "title": "公告列表", "path": "/backend/v2/announcement/list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f89c", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"ann_question_id\": {\n            \"description\": \"公告 - 关联热门问题\",\n            \"type\": \"integer\"\n          },\n          \"ann_text\": {\n            \"description\": \"公告文本\",\n            \"type\": \"string\"\n          },\n          \"ann_type\": {\n            \"description\": \"公告类型： 1: 文本；2：关联热门问题\",\n            \"type\": \"integer\"\n          },\n          \"desc\": {\n            \"description\": \"公告名称\",\n            \"type\": \"string\"\n          },\n          \"game_project\": {\n            \"description\": \"对应游戏\",\n            \"type\": \"string\"\n          },\n          \"id\": {\n            \"description\": \"公告ID\",\n            \"type\": \"integer\"\n          },\n          \"lang\": {\n            \"description\": \"语种\",\n            \"type\": \"string\"\n          },\n          \"pub_status\": {\n            \"description\": \"发布状态：1：启用； -1禁用；\",\n            \"type\": \"integer\"\n          },\n          \"title\": {\n            \"description\": \"引导语\",\n            \"type\": \"string\"\n          },\n          \"update_time\": {\n            \"description\": \"更新时间\",\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"description\": \"操作人\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.AnnouncementDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"desc\": {\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"id\": {\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementListReq\"\n}", "project_id": 16, "catid": 1980, "uid": 28, "add_time": 1675047395, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v2/announcement/change_state", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Announcement"], "_id": 10002, "method": "POST", "title": "公告变更状态", "path": "/backend/v2/announcement/change_state", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f899", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"change_state_flag\",\n    \"ident_id\"\n  ],\n  \"properties\": {\n    \"change_state_flag\": {\n      \"description\": \"变更状态\",\n      \"type\": \"integer\"\n    },\n    \"ident_id\": {\n      \"description\": \"对应ID\",\n      \"type\": \"integer\"\n    },\n    \"pub_status\": {\n      \"description\": \"发布状态：1：启用； -1禁用；\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ChangeStateReq\"\n}", "project_id": 16, "catid": 1980, "uid": 28, "add_time": 1675047395, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v2/announcement/edit", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Announcement"], "_id": 10012, "method": "POST", "title": "公告新增/修改", "path": "/backend/v2/announcement/edit", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f89b", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementEditResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"ann_type\",\n    \"desc\",\n    \"game_project\",\n    \"lang\"\n  ],\n  \"properties\": {\n    \"ann_question_id\": {\n      \"description\": \"公告 - 关联热门问题\",\n      \"type\": \"integer\"\n    },\n    \"ann_text\": {\n      \"description\": \"公告文本\",\n      \"type\": \"string\"\n    },\n    \"ann_type\": {\n      \"description\": \"公告类型： 1: 文本；2：关联热门问题\",\n      \"type\": \"integer\"\n    },\n    \"desc\": {\n      \"description\": \"公告名称\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"id\": {\n      \"description\": \"id > 0 为修改\",\n      \"type\": \"integer\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"title\": {\n      \"description\": \"公告引导语\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AnnouncementEditReq\"\n}", "project_id": 16, "catid": 1980, "uid": 28, "add_time": 1675047395, "up_time": 1711333691, "__v": 0}]}, {"index": 0, "name": "FaqlV2", "desc": "FaqlV2", "add_time": 1675047393, "up_time": 1675047393, "list": [{"query_path": {"path": "/backend/v2/question/faql_list", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["FaqlV2"], "_id": 10042, "method": "POST", "title": "热门问题列表，", "path": "/backend/v2/question/faql_list", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8aa", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"current_page\": {\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"game_project\": {\n            \"description\": \"游戏区分\",\n            \"type\": \"string\"\n          },\n          \"lang\": {\n            \"description\": \"语言\",\n            \"type\": \"string\"\n          },\n          \"logic_status\": {\n            \"description\": \"状态\",\n            \"type\": \"integer\"\n          },\n          \"pub_status\": {\n            \"description\": \"发布状态：启用/禁用\",\n            \"type\": \"integer\"\n          },\n          \"question_content\": {\n            \"description\": \"问题描述\",\n            \"type\": \"string\"\n          },\n          \"question_id\": {\n            \"description\": \"问题ID\",\n            \"type\": \"integer\"\n          },\n          \"update_time\": {\n            \"description\": \"操作时间\",\n            \"type\": \"string\"\n          },\n          \"updator\": {\n            \"description\": \"操作人\",\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/question.FaqlDetail\"\n      }\n    },\n    \"per_page\": {\n      \"type\": \"integer\"\n    },\n    \"total\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlListResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"logic_status\": {\n      \"description\": \"状态\",\n      \"type\": \"integer\"\n    },\n    \"page\": {\n      \"type\": \"integer\"\n    },\n    \"page_size\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlListReq\"\n}", "project_id": 16, "catid": 1985, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/faql_del", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["FaqlV2"], "_id": 10032, "method": "POST", "title": "热门问题删除", "path": "/backend/v2/question/faql_del", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a8", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\",\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlDelReq\"\n}", "project_id": 16, "catid": 1985, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/faql_change_state", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["FaqlV2"], "_id": 10027, "method": "POST", "title": "热门问题变更状态，", "path": "/backend/v2/question/faql_change_state", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a7", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"change_state_flag\",\n    \"ident_id\"\n  ],\n  \"properties\": {\n    \"change_state_flag\": {\n      \"description\": \"变更状态\",\n      \"type\": \"integer\"\n    },\n    \"ident_id\": {\n      \"description\": \"对应ID\",\n      \"type\": \"integer\"\n    },\n    \"pub_status\": {\n      \"description\": \"发布状态：1：启用； -1禁用；\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.ChangeStateReq\"\n}", "project_id": 16, "catid": 1985, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/faql_add", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["FaqlV2"], "_id": 10022, "method": "POST", "title": "热门问题新增", "path": "/backend/v2/question/faql_add", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a6", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\",\n    \"lang\",\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语言\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlAddReq\"\n}", "project_id": 16, "catid": 1985, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/backend/v2/question/faql_detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["FaqlV2"], "_id": 10037, "method": "POST", "title": "热门问题详情", "path": "/backend/v2/question/faql_detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8a9", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"所属 游戏\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"question_content\": {\n      \"description\": \"文章名称\",\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"question_id\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\",\n    \"question_id\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"type\": \"string\"\n    },\n    \"question_id\": {\n      \"description\": \"问题ID\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.FaqlDetailReq\"\n}", "project_id": 16, "catid": 1985, "uid": 28, "add_time": 1675047396, "up_time": 1711333692, "__v": 0}]}, {"index": 0, "name": "inner_front", "desc": "inner_front", "add_time": 1683513611, "up_time": 1683513611, "list": [{"query_path": {"path": "/front/inner/show_config/vip_show", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["inner_front"], "_id": 11067, "method": "POST", "title": "VIP频道-客服入口", "path": "/front/inner/show_config/vip_show", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6458610d50947e1b4f3433e5", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"sub_card\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"card_id\": {\n            \"type\": \"integer\"\n          },\n          \"desc\": {\n            \"type\": \"string\"\n          }\n        },\n        \"$$ref\": \"#/definitions/frontentity.InnerConfigShowVipD\"\n      }\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.InnerConfigShowVipResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\",\n    \"lang\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"game_project\",\n      \"type\": \"string\"\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.InnerConfigShowVipReq\"\n}", "project_id": 16, "catid": 2235, "uid": 28, "add_time": 1683513613, "up_time": 1683513613, "__v": 0}]}, {"index": 0, "name": "Aiset", "desc": "Aiset", "add_time": 1690511373, "up_time": 1690511373, "list": [{"query_path": {"path": "/backend/v2/aiset/detail", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Aiset"], "_id": 14352, "method": "POST", "title": "AI设置", "path": "/backend/v2/aiset/detail", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f897", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"AI类型\",\n      \"type\": \"integer\"\n    },\n    \"filter_is_all\": {\n      \"description\": \"触发条件：是否全选 true:全部生效\",\n      \"type\": \"boolean\"\n    },\n    \"filter_lang\": {\n      \"description\": \"触发条件：语言限定\",\n      \"type\": \"string\"\n    },\n    \"filter_pkg_channel_lists\": {\n      \"description\": \"触发条件：包体限定 包对应渠道\",\n      \"type\": \"string\"\n    },\n    \"filter_server_lists\": {\n      \"description\": \"触发条件：服务器 限定\",\n      \"type\": \"string\"\n    },\n    \"filter_white_uids\": {\n      \"description\": \"触发条件：白名单用户id\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"money_per_day\": {\n      \"description\": \"每日消耗\",\n      \"type\": \"integer\"\n    },\n    \"update_time\": {\n      \"description\": \"更新时间\",\n      \"type\": \"string\"\n    },\n    \"updator\": {\n      \"description\": \"操作人\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AisetDetailResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AisetDetailReq\"\n}", "project_id": 16, "catid": 2575, "uid": 28, "add_time": 1690511375, "up_time": 1711333691, "__v": 0}, {"query_path": {"path": "/backend/v2/aiset/save", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["Aiset"], "_id": 14357, "method": "POST", "title": "AI设置", "path": "/backend/v2/aiset/save", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13bc0d4d108db80f898", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"id\": {\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AisetSaveResp\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"required\": [\n    \"game_project\"\n  ],\n  \"properties\": {\n    \"ai_type\": {\n      \"description\": \"AI类型\",\n      \"type\": \"integer\"\n    },\n    \"filter_is_all\": {\n      \"description\": \"触发条件：是否全选 true:全部生效\",\n      \"type\": \"boolean\"\n    },\n    \"filter_lang\": {\n      \"description\": \"触发条件：语言限定\",\n      \"type\": \"string\"\n    },\n    \"filter_pkg_channel_lists\": {\n      \"description\": \"触发条件：包体限定 包对应渠道\",\n      \"type\": \"string\"\n    },\n    \"filter_server_lists\": {\n      \"description\": \"触发条件：服务器 限定\",\n      \"type\": \"string\"\n    },\n    \"filter_white_uids\": {\n      \"description\": \"触发条件：白名单用户id\",\n      \"type\": \"string\"\n    },\n    \"game_project\": {\n      \"description\": \"游戏项目名称\",\n      \"type\": \"string\"\n    },\n    \"money_per_day\": {\n      \"description\": \"每日消耗\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/question.AisetSaveReq\"\n}", "project_id": 16, "catid": 2575, "uid": 28, "add_time": 1690511375, "up_time": 1711333691, "__v": 0}]}, {"index": 0, "name": "api_elfin", "desc": "api_elfin", "add_time": 1710994382, "up_time": 1710994382, "list": [{"query_path": {"path": "/front/v3/elfin/index", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15588, "method": "POST", "title": "精灵主页获取", "path": "/front/v3/elfin/index", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8bc", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"banner_data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"article_lists\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"required\": [\n              \"art_id\"\n            ],\n            \"properties\": {\n              \"art_id\": {\n                \"description\": \"文章ID @gotags: validate:\\\"required\\\"\",\n                \"type\": \"integer\"\n              },\n              \"art_label\": {\n                \"description\": \"标签\",\n                \"type\": \"integer\"\n              },\n              \"art_title\": {\n                \"description\": \"文章ID - 标题\",\n                \"type\": \"string\"\n              }\n            },\n            \"$$ref\": \"#/definitions/frontentity.ElfinArticleDetail\"\n          }\n        },\n        \"banner_lists\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"required\": [\n              \"image_group\",\n              \"image_url\"\n            ],\n            \"properties\": {\n              \"art_id\": {\n                \"description\": \"图片关联内容ID[文章、问答] @gotags: validate:\\\"required_if=image_group 1 2,gt=0\\\"\",\n                \"type\": \"integer\"\n              },\n              \"image_group\": {\n                \"description\": \"图片关联内容分类 @gotags: validate:\\\"required,gt=0\\\"\",\n                \"type\": \"integer\"\n              },\n              \"image_title\": {\n                \"description\": \"图片标题 多语言\",\n                \"type\": \"string\"\n              },\n              \"image_url\": {\n                \"description\": \"图片 @gotags: validate:\\\"required\\\"\",\n                \"type\": \"string\"\n              },\n              \"jump_url\": {\n                \"description\": \"直接跳转[跳转、事件] @gotags: validate:\\\"required_if=image_group 3,required_if=image_group 4\\\"\",\n                \"type\": \"string\"\n              }\n            },\n            \"$$ref\": \"#/definitions/frontentity.ElfinBannerDetail\"\n          }\n        },\n        \"card_lists\": {\n          \"type\": \"array\",\n          \"items\": {\n            \"type\": \"object\",\n            \"required\": [\n              \"card_group\",\n              \"image_url\"\n            ],\n            \"properties\": {\n              \"art_id\": {\n                \"description\": \"卡片 - 关联内容分类 - 关联知识ID[文章、问答] @gotags: validate:\\\"required_if=card_group 2,gt=0\\\"\",\n                \"type\": \"integer\"\n              },\n              \"art_title\": {\n                \"description\": \"卡片 - 关联内容分类 - 关联知识ID[文章、问答] - 标题\",\n                \"type\": \"string\"\n              },\n              \"card_group\": {\n                \"description\": \"卡片 - 关联内容分类 @gotags: validate:\\\"required\\\"\",\n                \"type\": \"integer\"\n              },\n              \"child_card_list\": {\n                \"description\": \"卡片 - 关联内容分类 - 关联子卡片 @gotags: validate:\\\"required_if=card_group 1,gt=0\\\"\",\n                \"type\": \"array\",\n                \"items\": {\n                  \"$ref\": \"#/definitions/frontentity.ElfinTempCardDetail\"\n                }\n              },\n              \"image_title\": {\n                \"description\": \"卡片 - 图片标题 多语言\",\n                \"type\": \"string\"\n              },\n              \"image_url\": {\n                \"description\": \"卡片 - 展示图片 @gotags: validate:\\\"required\\\"\",\n                \"type\": \"string\"\n              },\n              \"ticket_cat_id\": {\n                \"description\": \"卡片 - 关联内容分类 - 关联工单 @gotags: validate:\\\"required_if=card_group 3,gt=0\\\"\",\n                \"type\": \"integer\"\n              }\n            },\n            \"$$ref\": \"#/definitions/frontentity.ElfinTempCardDetail\"\n          }\n        },\n        \"temp_id\": {\n          \"type\": \"integer\"\n        }\n      },\n      \"$$ref\": \"#/definitions/frontentity.BannerData\"\n    },\n    \"bottom_data\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"is_open_input\": {\n      \"type\": \"boolean\"\n    },\n    \"ticket_flag\": {\n      \"type\": \"boolean\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinIndexRes\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.BaseFrontReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1710994384, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/article", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15582, "method": "POST", "title": "精灵文章详情", "path": "/front/v3/elfin/article", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b8", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"art_content\": {\n      \"description\": \"文章内容 - 对应语种内容\",\n      \"type\": \"string\"\n    },\n    \"art_group\": {\n      \"description\": \"文章分组 - 分组\",\n      \"type\": \"string\"\n    },\n    \"art_id\": {\n      \"description\": \"文章ID\",\n      \"type\": \"integer\"\n    },\n    \"art_title\": {\n      \"description\": \"文章title - 对应语种title\",\n      \"type\": \"string\"\n    },\n    \"created_at\": {\n      \"description\": \"创建时间\",\n      \"type\": \"integer\"\n    },\n    \"faq_content\": {\n      \"description\": \"faq 多语言列表\",\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      }\n    },\n    \"lang\": {\n      \"description\": \"语种\",\n      \"type\": \"string\"\n    },\n    \"ticket_cat_id\": {\n      \"description\": \"关联工单分类\",\n      \"type\": \"integer\"\n    },\n    \"update_at\": {\n      \"description\": \"最后更新时间\",\n      \"type\": \"integer\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinArticleRes\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"art_id\": {\n      \"description\": \"文章ID @gotags: validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinArticleReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1710994384, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/article_faq", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15585, "method": "POST", "title": "精灵文章详情 - faq", "path": "/front/v3/elfin/article_faq", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8b9", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"code\": {\n      \"description\": \"返回code码\",\n      \"type\": \"integer\"\n    },\n    \"data\": {\n      \"type\": \"object\"\n    },\n    \"msg\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/fctx.Response\"\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"art_id\": {\n      \"description\": \"文章ID @gotags: validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinArticleReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1710994384, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/temp/sub_card", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15597, "method": "POST", "title": "精灵模版详情-子卡片", "path": "/front/v3/elfin/temp/sub_card", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8be", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"required\": [\n      \"image_group\",\n      \"image_url\"\n    ],\n    \"properties\": {\n      \"art_id\": {\n        \"description\": \"图片关联内容ID[文章、问答] @gotags: validate:\\\"required_if=image_group 1 2,gt=0\\\"\",\n        \"type\": \"integer\"\n      },\n      \"image_group\": {\n        \"description\": \"图片关联内容分类 @gotags: validate:\\\"required,gt=0\\\"\",\n        \"type\": \"integer\"\n      },\n      \"image_title\": {\n        \"description\": \"图片标题 多语言\",\n        \"type\": \"string\"\n      },\n      \"image_url\": {\n        \"description\": \"图片 @gotags: validate:\\\"required\\\"\",\n        \"type\": \"string\"\n      },\n      \"jump_url\": {\n        \"description\": \"直接跳转[跳转、事件] @gotags: validate:\\\"required_if=image_group 3,required_if=image_group 4\\\"\",\n        \"type\": \"string\"\n      }\n    },\n    \"$$ref\": \"#/definitions/frontentity.ElfinBannerDetail\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"card_idx\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"temp_id\": {\n      \"type\": \"integer\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinTempSubCardReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1711185276, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/query_fuzzy", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15591, "method": "POST", "title": "精灵热门问题 - 模糊匹配", "path": "/front/v3/elfin/query_fuzzy", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8bd", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"string\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinQueryFuzzyReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1710994384, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/inner/knowledge/searchElf", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15594, "method": "POST", "title": "精灵结果匹配", "path": "/front/inner/knowledge/searchElf", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13cc0d4d108db80f8ac", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"string\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"message_id\": {\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinChatStreamReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1711185276, "up_time": 1711333692, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/chatstream", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15603, "method": "POST", "title": "精灵结果匹配", "path": "/front/v3/elfin/chatstream", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8bb", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"string\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"message_id\": {\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinChatStreamReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1711333693, "up_time": 1711333693, "__v": 0}, {"query_path": {"path": "/front/v3/elfin/chat_appraise", "params": []}, "edit_uid": 0, "status": "undone", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": ["api_elfin"], "_id": 15600, "method": "POST", "title": "精灵结果点赞踩", "path": "/front/v3/elfin/chat_appraise", "req_params": [], "req_body_form": [], "req_headers": [{"required": "1", "_id": "6600e13dc0d4d108db80f8ba", "name": "Content-Type", "value": "application/json"}], "req_query": [], "req_body_type": "json", "res_body_type": "json", "res_body": "{\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"string\"\n  }\n}", "req_body_other": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"account_id\": {\n      \"type\": \"string\"\n    },\n    \"app_version\": {\n      \"type\": \"string\"\n    },\n    \"channel\": {\n      \"description\": \"sdk_pkg_channel sdk包对应的渠道\",\n      \"type\": \"string\"\n    },\n    \"country_code\": {\n      \"type\": \"string\"\n    },\n    \"device_type\": {\n      \"type\": \"string\"\n    },\n    \"fp_uid\": {\n      \"type\": \"string\"\n    },\n    \"fpid\": {\n      \"type\": \"integer\"\n    },\n    \"fpx_app_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"string\"\n    },\n    \"game_id\": {\n      \"description\": \"validate:\\\"required\\\"\",\n      \"type\": \"integer\"\n    },\n    \"game_token\": {\n      \"type\": \"string\"\n    },\n    \"json_data\": {\n      \"description\": \"json data  json 结构，内部包含 session_key 字段\",\n      \"type\": \"object\"\n    },\n    \"lang\": {\n      \"description\": \"validate:\\\"required\\\" - 上报日志渠道\",\n      \"type\": \"string\"\n    },\n    \"message_id\": {\n      \"type\": \"string\"\n    },\n    \"network_info\": {\n      \"type\": \"string\"\n    },\n    \"nick_name\": {\n      \"type\": \"string\"\n    },\n    \"os\": {\n      \"type\": \"string\"\n    },\n    \"os_version\": {\n      \"type\": \"string\"\n    },\n    \"query\": {\n      \"type\": \"string\"\n    },\n    \"sdk_version\": {\n      \"type\": \"string\"\n    },\n    \"sid\": {\n      \"description\": \"区服ID\",\n      \"type\": \"string\"\n    },\n    \"subchannel\": {\n      \"type\": \"string\"\n    },\n    \"track_key\": {\n      \"type\": \"string\"\n    },\n    \"ts\": {\n      \"type\": \"integer\"\n    },\n    \"uid\": {\n      \"type\": \"integer\"\n    },\n    \"uuid\": {\n      \"type\": \"string\"\n    }\n  },\n  \"$$ref\": \"#/definitions/frontentity.ElfinChatStreamReq\"\n}", "project_id": 16, "catid": 2693, "uid": 28, "add_time": 1711333693, "up_time": 1711333693, "__v": 0}]}]