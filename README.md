# general-cs-web

## 项目概述
这是一个基于Vue 3的客户服务系统前端项目，提供客户服务工单、智能问答等功能，支持移动端和PC端多种设备访问。

## 技术架构
- **前端框架**：Vue 3 + TypeScript
- **状态管理**：Vuex 4
- **路由管理**：Vue Router 4
- **UI组件库**：Vant 3
- **HTTP请求**：Axios
- **国际化**：Vue I18n
- **构建工具**：Vue CLI 4
- **CSS预处理器**：SCSS/SASS

## 项目目录结构
```
├── public/                 # 静态资源目录
├── src/                    # 源代码目录
│   ├── @types/             # TypeScript类型定义
│   ├── api/                # API接口定义
│   ├── assets/             # 项目资源文件
│   ├── components/         # 公共组件
│   ├── customConfig/       # 自定义配置
│   ├── enum/               # 枚举定义
│   ├── layout/             # 布局组件
│   ├── packages/           # 自定义包
│   ├── plugins/            # 插件配置
│   ├── router/             # 路由配置
│   ├── server/             # 服务端相关
│   ├── store/              # Vuex状态管理
│   ├── styles/             # 样式文件
│   ├── utils/              # 工具函数
│   ├── views/              # 页面视图组件
│   │   ├── New/            # 新版客服系统页面
│   │   ├── PC/             # PC端页面
│   │   └── QN/             # 问卷调查页面
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── nginx_config/           # Nginx配置
├── language/               # 多语言配置
├── vue.config.js           # Vue CLI配置
├── tsconfig.json           # TypeScript配置
├── babel.config.js         # Babel配置
├── postcss.config.js       # PostCSS配置
└── package.json            # 项目依赖
```

## 业务模块划分
1. **智能客服模块**
   - 智能问答系统
   - 常见问题解答

2. **工单系统模块**
   - 工单创建与提交
   - 工单详情查看
   - 工单历史记录
   - 工单评价

3. **问卷调查模块**
   - 用户反馈收集
   - 满意度调查

4. **多端适配**
   - 移动端界面
   - PC端界面

## 逻辑划分
- **视图层**：views目录下的各个页面组件
- **数据层**：store目录下的Vuex状态管理
- **服务层**：api目录下的接口定义和server目录下的拦截器
- **工具层**：utils目录下的各种工具函数
- **组件层**：components目录下的可复用组件

## 环境与部署

### Node version
v14.21.0

### 开发环境
```
npm install
npm run serve
```

### 测试环境
```
npm run serve:test
npm run build:test
```

### 生产环境
```
npm run build
```

### 代码检查
```
npm run lint
```

## 公网域名
### 测试环境
- **test**: fpcs-web-test.funplus.com
- **test-cn**: fpcs-web-test.funplus.com.cn

### 预发布环境
- **stage**: fpcs-web-stage.funplus.com
- **stage-cn**: fpcs-web-stage.funplus.com.cn

### 生产环境
- **master (global)**:
  - fpcs-web.funplus.com
  - fpcs-web-tx.kingsgroup.cn
  - fpcs-web-tx.yoo-mei.cn
  - fpcs-web.lightning-piggy.com

- **master-cn**:
  - fpcs-web.funplus.com.cn
  - fpcs-web.nenglianghe.cn
  - fpcs-web.yoo-mei.cn
  - fpcs-web.zmgames.cn

### DevOps部署
- **master (global)**: kg.global.prod.general-cs-web
  - https://devops-tx.kingsgroup.cn/sprinkle/k8s_deployment/512/history

- **master-cn**: funplus.cn.prod.general-cs-web
  - https://devops-tx.kingsgroup.cn/sprinkle/k8s_deployment/511/history

## 自定义配置
参见 [Vue CLI配置参考](https://cli.vuejs.org/config/)。
