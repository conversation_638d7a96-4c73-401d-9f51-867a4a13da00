'use strict'

const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const HardSourceWebpackPlugin = require('hard-source-webpack-plugin')

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  indexPath: 'index.html',
  filenameHashing: true,
  lintOnSave: true,
  runtimeCompiler: false,
  transpileDependencies: [],
  productionSourceMap: false,
  devServer: {
    open: false,
    host: '0.0.0.0',
    // host: '************',
    port: 8080,
    https: false,
    overlay: {
      warnings: false,
      errors: true
    },
    sockHost: '*************',
    disableHostCheck: true,
    proxy: {
      '/backend': {
        target: 'https://cs-api.funplus.com/front', // 修改为https
        // target: 'https://cs-api-stage.funplus.com/front', // 修改为https
        // target: 'https://cs-api-test.funplus.com/front', // 修改为https
        changeOrigin: true,
        ws: false,
        secure: false,
        pathRewrite: {
          '^/backend': ''
        },
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      },
      '/egress_survey': {
        target: 'https://ops-ticket-api-test.funplus.com/egress_survey', // test
        changeOrigin: true,
        ws: false,
        secure: false,
        pathRewrite: {
          '^/egress_survey': ''
        }
      }
    }
  },
  // configureWebpack: {
  //   devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
  //   output: {
  //     filename: 'static/js/[name].[hash].js',
  //     chunkFilename: 'static/js/[name].[hash].js'
  //   }
  // },
  configureWebpack: config => {
    // 开发环境配置
    if (process.env.NODE_ENV === 'development') {
      // 使用更好的源码映射方式，能够准确指向源码位置
      config.devtool = 'eval-cheap-module-source-map'

      // 添加缓存插件
      config.plugins.push(new HardSourceWebpackPlugin())

      // 禁用不必要的插件
      config.optimization = {
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false
      }
    } else {
      // 生产环境配置
      config.devtool = false
    }

    // 通用配置
    config.output = {
      ...config.output,
      filename: 'static/js/[name].[hash].js',
      chunkFilename: 'static/js/[name].[hash].js',
      // 让webpack保留更多的源码映射信息
      devtoolModuleFilenameTemplate: info => {
        const resourcePath = info.resourcePath
        if (resourcePath.match(/\.vue$/)) {
          return `webpack:///${resourcePath}?${info.hash}`
        }
        return `webpack:///${resourcePath}`
      }
    }

    // 缓存配置
    config.cache = true
  },
  chainWebpack: config => {
    // i18n warning
    config.resolve.alias.set('vue-i18n', 'vue-i18n/dist/vue-i18n.cjs.js')
    // 拆包
    config.optimization.splitChunks({
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 10000,
      automaticNameDelimiter: '-',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
            return `chunk.${packageName.replace('@', '')}`
          },
          priority: 30
        }
      }
    })
    config.resolve.alias.set('@', resolve('src'))
    config.plugin('html').tap(args => {
      args[0].title = ''
      return args
    })
    config.module.rule('media').test(/\.(vtt|mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/)
    config.module.rule('images').use('image-webpack-loader').loader('image-webpack-loader').options({
      bypassOnDebug: true
    })
  },
  css: {
    // extract: {
    //   filename: 'static/css/[name].[chunkhash].vw.css',
    //   chunkFilename: 'static/css/[name].[chunkhash].vw.css'
    // },
    // 是否开启 CSS source map？
    sourceMap: false,
    loaderOptions: {
      sass: {
        prependData: `
          @import "src/styles/variables.scss";
          @import "src/styles/mixin.scss";
        `
      },
      scss: {
        prependData: `
          @import "src/styles/variables.scss";
          @import "src/styles/mixin.scss";
        `
      }
    }
  }
}
