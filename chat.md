2.1 对话创建/更新对话接口 -- 存储用户对话历史

POST /egress/cs/conversation/update
Request:
{
    "conversation_id": string,    // 会话ID
    "user_id": string,           // 用户ID
    "cat_id": number,            // 分类ID
    "ticket_id": number,
    "history": [
        {
            "role": string,
            "question_key": string,
            "content": string
        }
    ],
    "question_get_list": [
        {
            "question_key": string,
            "answer": string,
            "has_answer": boolean,
            "question_ask_count": number
        }
    ]
}
Response:
{
    "code": 0,
    "message": "success",
    "data": {
        "uuid": string,
        "conversation_id": string,
        "timestamp": number
    }
}

2.2 对话历史接口 -恢复对话接口

post /egress/cs/conversation/history
Request:
{
    "conversation_id": string,    // 会话ID
}
Response:
{
    "code": 0,
    "message": "success",
    "data": {
        "history": [
            {
                "role": string,
                "content": string,
                "question_key": string,
                "timestamp": number,
                "uuid": string
            }
        ],
        "question_hit_list": [
            {
                "question_key": string,
                "has_answer": boolean
            }
        ],
        "question_get_list": [
            {
                "question_key": string,
                "answer": string,
                "has_answer": boolean,
                "question_ask_count": number
            }
        ]
    }
}

2.3 对话交互接口
POST /egress/cs/conversation/chat
Request:
{
    "conversation_id": string,        // 会话ID
    "now_question_key": string,     // 当前问题key
    "now_question_content": string, // 当前问题内容
    "now_answer_content": [
        {
            "text": string
        }
        {
            "text": string
        }
    ],
    "question_list": [{
            "question_key": "name",
            "question_content": "你是什么名字",
    }, {
            "question_key": "time",
            "question_content": "你是什么时候发生的"
    }]
}
Response:
{
    "code": 0,
    "data": {
        "history_answer_list": [{
            "question_key": "time",
            "quesion_answer": "22年10月"
        },
        {
            "question_key": "name",
            "quesion_answer": "123"
        }],
        "now_question_key" : "year_old",
        "now_answer_content": "我明天要上班",
        "hit_question": false
        "completed": boolean         // 是否完成所有问题
    }
}
